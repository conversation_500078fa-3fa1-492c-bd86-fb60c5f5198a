rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    
    // Helper functions
    function isAuthenticated() {
      return request.auth != null;
    }
    
    function isOwner(userId) {
      return isAuthenticated() && request.auth.uid == userId;
    }
    
    function isAdmin() {
      return isAuthenticated() && 
             exists(/databases/$(database)/documents/admins/$(request.auth.uid));
    }
    
    function isValidUser(userData) {
      return userData.keys().hasAll(['email', 'displayName', 'createdAt']) &&
             userData.email is string &&
             userData.displayName is string;
    }
    
    function isValidPlant(plantData) {
      return plantData.keys().hasAll(['name', 'latinName', 'category', 'description']) &&
             plantData.name is string &&
             plantData.latinName is string &&
             plantData.category is string &&
             plantData.description is string;
    }
    
    function isValidArticle(articleData) {
      return articleData.keys().hasAll(['title', 'content', 'author', 'category']) &&
             articleData.title is string &&
             articleData.content is string &&
             articleData.author is string &&
             articleData.category is string;
    }

    // Users collection
    match /users/{userId} {
      allow read, write: if isOwner(userId);
      allow create: if isAuthenticated() && 
                       isOwner(userId) && 
                       isValidUser(resource.data);
    }

    // Plants collection
    match /plants/{plantId} {
      // Anyone can read published plants
      allow read: if true;
      
      // Only admins can create, update, delete plants
      allow create: if isAdmin() && isValidPlant(request.resource.data);
      allow update: if isAdmin() && isValidPlant(request.resource.data);
      allow delete: if isAdmin();
    }

    // Articles collection
    match /articles/{articleId} {
      // Anyone can read published articles
      allow read: if resource.data.isPublished == true;
      
      // Admins can read all articles (including drafts)
      allow read: if isAdmin();
      
      // Only admins can create, update, delete articles
      allow create: if isAdmin() && isValidArticle(request.resource.data);
      allow update: if isAdmin() && isValidArticle(request.resource.data);
      allow delete: if isAdmin();
    }

    // Categories collection
    match /categories/{categoryId} {
      // Anyone can read categories
      allow read: if true;
      
      // Only admins can modify categories
      allow create, update, delete: if isAdmin();
    }

    // Plant care logs collection
    match /plantCareLogs/{logId} {
      // Users can only access their own care logs
      allow read, write: if isAuthenticated() && 
                            resource.data.userId == request.auth.uid;
      allow create: if isAuthenticated() && 
                       request.resource.data.userId == request.auth.uid;
    }

    // Notifications collection
    match /notifications/{notificationId} {
      // Users can only access their own notifications
      allow read: if isAuthenticated() && 
                     resource.data.userId == request.auth.uid;
      allow update: if isAuthenticated() && 
                       resource.data.userId == request.auth.uid &&
                       // Only allow updating isRead field
                       request.resource.data.diff(resource.data).affectedKeys()
                         .hasOnly(['isRead']);
      
      // System can create notifications
      allow create: if isAdmin();
      allow delete: if isAdmin();
    }

    // App settings collection
    match /appSettings/{settingId} {
      // Anyone can read app settings
      allow read: if true;
      
      // Only admins can modify app settings
      allow create, update, delete: if isAdmin();
    }

    // Admins collection (for admin role management)
    match /admins/{adminId} {
      // Only existing admins can read admin list
      allow read: if isAdmin();
      
      // Only existing admins can add/remove admins
      allow create, update, delete: if isAdmin();
    }

    // User favorites subcollection (alternative approach)
    match /users/{userId}/favorites/{favoriteId} {
      allow read, write: if isOwner(userId);
    }

    // User plants subcollection (alternative approach)
    match /users/{userId}/userPlants/{plantId} {
      allow read, write: if isOwner(userId);
    }

    // Comments subcollection for articles
    match /articles/{articleId}/comments/{commentId} {
      // Anyone can read comments on published articles
      allow read: if get(/databases/$(database)/documents/articles/$(articleId)).data.isPublished == true;
      
      // Authenticated users can create comments
      allow create: if isAuthenticated() && 
                       request.resource.data.userId == request.auth.uid;
      
      // Users can update/delete their own comments
      allow update, delete: if isAuthenticated() && 
                               resource.data.userId == request.auth.uid;
      
      // Admins can moderate all comments
      allow update, delete: if isAdmin();
    }

    // File uploads metadata (if using Firestore for file tracking)
    match /uploads/{uploadId} {
      // Users can only access their own uploads
      allow read, write: if isAuthenticated() && 
                            resource.data.uploadedBy == request.auth.uid;
      allow create: if isAuthenticated() && 
                       request.resource.data.uploadedBy == request.auth.uid;
      
      // Admins can access all uploads
      allow read, write: if isAdmin();
    }

    // Analytics collection (for app usage tracking)
    match /analytics/{analyticsId} {
      // Only admins can read analytics
      allow read: if isAdmin();
      
      // System can write analytics (server-side)
      allow create: if isAdmin();
    }

    // Feedback collection
    match /feedback/{feedbackId} {
      // Users can create feedback
      allow create: if isAuthenticated();
      
      // Users can read their own feedback
      allow read: if isAuthenticated() && 
                     resource.data.userId == request.auth.uid;
      
      // Admins can read all feedback
      allow read, update: if isAdmin();
    }

    // Default deny rule for any other paths
    match /{document=**} {
      allow read, write: if false;
    }
  }
}

// Storage rules (for Firebase Storage)
// Place this in storage.rules file:
/*
rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    // Images uploads
    match /images/{allPaths=**} {
      allow read: if true;
      allow write: if request.auth != null &&
                      request.resource.size < 5 * 1024 * 1024 && // 5MB limit
                      request.resource.contentType.matches('image/.*');
    }
    
    // User profile pictures
    match /profiles/{userId}/{allPaths=**} {
      allow read: if true;
      allow write: if request.auth != null && 
                      request.auth.uid == userId &&
                      request.resource.size < 2 * 1024 * 1024 && // 2MB limit
                      request.resource.contentType.matches('image/.*');
    }
    
    // Plant care photos
    match /care-logs/{userId}/{allPaths=**} {
      allow read, write: if request.auth != null && 
                            request.auth.uid == userId &&
                            request.resource.size < 10 * 1024 * 1024; // 10MB limit
    }
    
    // Admin uploads
    match /admin/{allPaths=**} {
      allow read: if true;
      allow write: if request.auth != null; // Add admin check here
    }
  }
}
*/
