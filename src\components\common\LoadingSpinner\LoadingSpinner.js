import React from 'react';
import { View, ActivityIndicator, Text } from 'react-native';
import { COLORS } from '../../../utils/constants';
import styles from './LoadingSpinner.styles.js';

const LoadingSpinner = ({
  size = 'large',
  color = COLORS.primary,
  text = null,
  style,
  overlay = false,
  ...props
}) => {
  const containerStyle = [
    styles.container,
    overlay && styles.overlay,
    style
  ];

  return (
    <View style={containerStyle}>
      <ActivityIndicator 
        size={size} 
        color={color} 
        {...props}
      />
      {text && (
        <Text style={[styles.text, { color }]}>
          {text}
        </Text>
      )}
    </View>
  );
};

export default LoadingSpinner;
