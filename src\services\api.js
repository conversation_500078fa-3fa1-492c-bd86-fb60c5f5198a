// API servis fonksiyonları
import { SAMPLE_PLANTS, KNOWLEDGE_BASE_ARTICLES } from '../utils/constants';

// Simüle edilmiş API gecikmeleri
const delay = (ms) => new Promise(resolve => setTimeout(resolve, ms));

class ApiService {
  // Tüm bitkileri getir
  async getAllPlants() {
    await delay(500); // API çağrısını simüle et
    return {
      success: true,
      data: SAMPLE_PLANTS
    };
  }

  // Belirli bir bitkiyi getir
  async getPlantById(id) {
    await delay(300);
    const plant = SAMPLE_PLANTS.find(p => p.id === parseInt(id));
    
    if (plant) {
      return {
        success: true,
        data: plant
      };
    } else {
      return {
        success: false,
        error: 'Bit<PERSON> bulunamadı'
      };
    }
  }

  // Bitki ara
  async searchPlants(query) {
    await delay(400);
    
    if (!query) {
      return {
        success: true,
        data: SAMPLE_PLANTS
      };
    }

    const filteredPlants = SAMPLE_PLANTS.filter(plant =>
      plant.name.toLowerCase().includes(query.toLowerCase()) ||
      plant.latinName.toLowerCase().includes(query.toLowerCase()) ||
      plant.description.toLowerCase().includes(query.toLowerCase())
    );

    return {
      success: true,
      data: filteredPlants
    };
  }

  // Popüler bitkileri getir
  async getPopularPlants() {
    await delay(300);
    const popularPlants = SAMPLE_PLANTS.filter(plant => plant.isPopular);
    
    return {
      success: true,
      data: popularPlants
    };
  }

  // Yeni bitkileri getir
  async getNewPlants() {
    await delay(300);
    const newPlants = SAMPLE_PLANTS.filter(plant => plant.isNew);
    
    return {
      success: true,
      data: newPlants
    };
  }

  // Kategoriye göre bitkileri getir
  async getPlantsByCategory(category) {
    await delay(400);
    
    if (!category || category === 'all') {
      return {
        success: true,
        data: SAMPLE_PLANTS
      };
    }

    const filteredPlants = SAMPLE_PLANTS.filter(plant => 
      plant.category === category
    );

    return {
      success: true,
      data: filteredPlants
    };
  }

  // Bilgi bankası makalelerini getir
  async getKnowledgeBaseArticles() {
    await delay(300);
    return {
      success: true,
      data: KNOWLEDGE_BASE_ARTICLES
    };
  }

  // Belirli bir makaleyi getir
  async getArticleById(id) {
    await delay(200);
    const article = KNOWLEDGE_BASE_ARTICLES.find(a => a.id === parseInt(id));
    
    if (article) {
      return {
        success: true,
        data: article
      };
    } else {
      return {
        success: false,
        error: 'Makale bulunamadı'
      };
    }
  }

  // Kategoriye göre makaleleri getir
  async getArticlesByCategory(category) {
    await delay(300);
    
    if (!category || category === 'all') {
      return {
        success: true,
        data: KNOWLEDGE_BASE_ARTICLES
      };
    }

    const filteredArticles = KNOWLEDGE_BASE_ARTICLES.filter(article => 
      article.category === category
    );

    return {
      success: true,
      data: filteredArticles
    };
  }

  // Makale ara
  async searchArticles(query) {
    await delay(300);
    
    if (!query) {
      return {
        success: true,
        data: KNOWLEDGE_BASE_ARTICLES
      };
    }

    const filteredArticles = KNOWLEDGE_BASE_ARTICLES.filter(article =>
      article.title.toLowerCase().includes(query.toLowerCase()) ||
      article.summary.toLowerCase().includes(query.toLowerCase()) ||
      article.content.toLowerCase().includes(query.toLowerCase())
    );

    return {
      success: true,
      data: filteredArticles
    };
  }
}

export default new ApiService();
