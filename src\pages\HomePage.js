import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  Image,
  ActivityIndicator,
  StyleSheet,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';

// Mock data - Tasarım aşaması için
const mockPlants = [
  {
    id: '1',
    name: '<PERSON><PERSON><PERSON>',
    latinName: '<PERSON> damascena',
    category: 'Çi<PERSON>ek',
    description: '<PERSON>üzel kokulu ve renkli çiçeklere sahip popüler bir bitki. Bahçelerde ve saksılarda yetiştirilebilir.',
    mainImage: 'https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=400&h=400&fit=crop',
    care: {
      water: 'Düzenli',
      light: 'Güneş',
      soil: 'İyi drene',
      temperature: '15-25°C'
    }
  },
  {
    id: '2',
    name: '<PERSON><PERSON><PERSON>',
    latinName: '<PERSON><PERSON><PERSON><PERSON> angustifolia',
    category: 'Aromatik',
    description: 'Rahatlatıcı kokusu ile bilinen mor çiçekli bitki. Kurutulmuş çiçekleri çay olarak kullanılır.',
    mainImage: 'https://images.unsplash.com/photo-1611909023032-2d6b3134ecba?w=400&h=400&fit=crop',
    care: {
      water: 'Az',
      light: 'Güneş',
      soil: 'Kuru',
      temperature: '10-30°C'
    }
  },
  {
    id: '3',
    name: 'Nane',
    latinName: 'Mentha spicata',
    category: 'Aromatik',
    description: 'Ferahlatıcı kokusu ve tadı olan yeşil yapraklı bitki. Çay ve yemeklerde kullanılır.',
    mainImage: 'https://images.unsplash.com/photo-1628556270448-4d4e4148e1b1?w=400&h=400&fit=crop',
    care: {
      water: 'Bol',
      light: 'Yarı gölge',
      soil: 'Nemli',
      temperature: '15-25°C'
    }
  },
  {
    id: '4',
    name: 'Kekik',
    latinName: 'Thymus vulgaris',
    category: 'Aromatik',
    description: 'Güçlü aroması olan küçük yapraklı bitki. Mutfakta baharat olarak kullanılır.',
    mainImage: 'https://images.unsplash.com/photo-1594736797933-d0401ba2fe65?w=400&h=400&fit=crop',
    care: {
      water: 'Az',
      light: 'Güneş',
      soil: 'Kuru',
      temperature: '10-25°C'
    }
  },
  {
    id: '5',
    name: 'Papatya',
    latinName: 'Matricaria chamomilla',
    category: 'Çiçek',
    description: 'Beyaz çiçekli şifalı bitki. Çay olarak içilir ve cilt bakımında kullanılır.',
    mainImage: 'https://images.unsplash.com/photo-1574684891174-df6b02ab38d7?w=400&h=400&fit=crop',
    care: {
      water: 'Orta',
      light: 'Güneş',
      soil: 'Normal',
      temperature: '15-20°C'
    }
  }
];

const HomePage = () => {
  const navigation = useNavigation();
  const [plants, setPlants] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Mock veri yükle - Firebase entegrasyonu için hazır
  const loadPlants = async () => {
    try {
      console.log('🌱 Bitkiler yükleniyor...');
      setLoading(true);
      setError(null);
      
      // Simulate loading delay
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      setPlants(mockPlants);
      console.log('✅ Plants loaded:', mockPlants.length);
    } catch (err) {
      console.error('❌ Error loading plants:', err);
      setError('Bitkiler yüklenirken hata oluştu');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadPlants();
  }, []);

  // Bitki detayına git
  const handlePlantPress = (plant) => {
    navigation.navigate('PlantDetail', { plantId: plant.id, plant });
  };

  // Loading state
  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.header}>
          <View style={styles.logoSection}>
            <Image
              source={require('../../assets/wikiplantlogo.png')}
              style={styles.logo}
              resizeMode="contain"
            />
            <View style={styles.titleSection}>
              <Text style={styles.title}>Dallardan Bilgi</Text>
              <Text style={styles.subtitle}>Bitki Ansiklopedisi</Text>
            </View>
          </View>
        </View>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#7C4D25" />
          <Text style={styles.loadingText}>Bitkiler yükleniyor...</Text>
        </View>
      </SafeAreaView>
    );
  }

  // Error state
  if (error) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.header}>
          <View style={styles.logoSection}>
            <Image
              source={require('../../assets/wikiplantlogo.png')}
              style={styles.logo}
              resizeMode="contain"
            />
            <View style={styles.titleSection}>
              <Text style={styles.title}>Dallardan Bilgi</Text>
              <Text style={styles.subtitle}>Bitki Ansiklopedisi</Text>
            </View>
          </View>
        </View>
        <View style={styles.errorContainer}>
          <Ionicons name="alert-circle" size={64} color="#FF6B6B" />
          <Text style={styles.errorText}>{error}</Text>
          <TouchableOpacity style={styles.retryButton} onPress={loadPlants}>
            <Text style={styles.retryButtonText}>Tekrar Dene</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Header */}
        <View style={styles.header}>
          <View style={styles.logoSection}>
            <Image
              source={require('../../assets/wikiplantlogo.png')}
              style={styles.logo}
              resizeMode="contain"
            />
            <View style={styles.titleSection}>
              <Text style={styles.title}>Dallardan Bilgi</Text>
              <Text style={styles.subtitle}>Bitki Ansiklopedisi</Text>
            </View>
          </View>
        </View>

        {/* Plant Count */}
        <View style={styles.countContainer}>
          <Text style={styles.countText}>{plants.length} Bitki Bulundu</Text>
        </View>

        {/* Plant List */}
        {plants.length === 0 ? (
          <View style={styles.emptyContainer}>
            <Ionicons name="leaf" size={64} color="#CCCCCC" />
            <Text style={styles.emptyText}>Henüz bitki eklenmemiş</Text>
          </View>
        ) : (
          plants.map((plant) => (
            <TouchableOpacity
              key={plant.id}
              style={styles.plantCard}
              onPress={() => handlePlantPress(plant)}
              activeOpacity={0.8}
            >
              <Image
                source={{ uri: plant.mainImage }}
                style={styles.plantImage}
                resizeMode="cover"
              />
              <View style={styles.plantInfo}>
                <Text style={styles.plantName}>{plant.name}</Text>
                <Text style={styles.plantLatin}>{plant.latinName}</Text>
                <Text style={styles.plantCategory}>{plant.category}</Text>
                <Text style={styles.plantDescription} numberOfLines={2}>
                  {plant.description}
                </Text>
                <View style={styles.detailButton}>
                  <Text style={styles.detailButtonText}>Detay</Text>
                  <Ionicons name="chevron-forward" size={16} color="#FFFFFF" />
                </View>
              </View>
            </TouchableOpacity>
          ))
        )}
      </ScrollView>
    </SafeAreaView>
  );
};

// Stiller
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  scrollView: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#333333',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
  },
  errorText: {
    fontSize: 16,
    color: '#FF6B6B',
    textAlign: 'center',
    marginVertical: 16,
  },
  retryButton: {
    backgroundColor: '#7C4D25',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 8,
  },
  retryButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: 'bold',
  },
  header: {
    backgroundColor: '#FC7138',
    padding: 20,
    paddingTop: 40,
  },
  logoSection: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  logo: {
    width: 40,
    height: 40,
    marginRight: 12,
  },
  titleSection: {
    flex: 1,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  subtitle: {
    fontSize: 14,
    color: '#FFFFFF',
    opacity: 0.9,
  },
  countContainer: {
    padding: 16,
    backgroundColor: '#FFFFFF',
  },
  countText: {
    fontSize: 16,
    color: '#333333',
    fontWeight: '600',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
  },
  emptyText: {
    fontSize: 16,
    color: '#CCCCCC',
    marginTop: 16,
  },
  plantCard: {
    backgroundColor: '#FFFFFF',
    marginHorizontal: 16,
    marginVertical: 8,
    borderRadius: 12,
    flexDirection: 'row',
    overflow: 'hidden',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  plantImage: {
    width: 100,
    height: 120,
  },
  plantInfo: {
    flex: 1,
    padding: 12,
    justifyContent: 'space-between',
  },
  plantName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333333',
  },
  plantLatin: {
    fontSize: 14,
    fontStyle: 'italic',
    color: '#CCCCCC',
    marginTop: 2,
  },
  plantCategory: {
    fontSize: 12,
    color: '#7C4D25',
    fontWeight: '600',
    marginTop: 4,
  },
  plantDescription: {
    fontSize: 14,
    color: '#333333',
    marginTop: 8,
    lineHeight: 20,
  },
  detailButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#7C4D25',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 6,
    alignSelf: 'flex-start',
    marginTop: 8,
  },
  detailButtonText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: '600',
    marginRight: 4,
  },
});

export default HomePage;
