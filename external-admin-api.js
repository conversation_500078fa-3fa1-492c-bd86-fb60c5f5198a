// External Admin API for WikiPlant Data Loading
// This file can be used to create a separate web admin panel

const express = require('express');
const cors = require('cors');
const { initializeApp } = require('firebase/app');
const { 
  getFirestore, 
  collection, 
  addDoc, 
  getDocs, 
  writeBatch,
  doc 
} = require('firebase/firestore');

const app = express();
const PORT = process.env.PORT || 3001;

// Middleware
app.use(cors());
app.use(express.json());

// Firebase Configuration (same as your mobile app)
const firebaseConfig = {
  apiKey: "AIzaSyCT9Tn0Z7MVjKeonhNuovpdbXVz7syQwJ8",
  authDomain: "wikiplant-aebaf.firebaseapp.com",
  projectId: "wikiplant-aebaf",
  storageBucket: "wikiplant-aebaf.firebasestorage.app",
  messagingSenderId: "847754813681",
  appId: "1:847754813681:web:22559d4547eca2ab594950",
  measurementId: "G-27R9QPVXF6"
};

// Initialize Firebase
const firebaseApp = initializeApp(firebaseConfig);
const db = getFirestore(firebaseApp);

// Sample Data
const SAMPLE_PLANTS = [
  {
    name: 'Gül',
    latinName: 'Rosa damascena',
    category: 'Çiçek',
    description: 'Güzel kokulu ve renkli çiçeklere sahip çok yıllık bitki. Bahçe düzenlemelerinde ve parfüm endüstrisinde yaygın kullanılır.',
    origin: 'Asya ve Avrupa',
    lifespan: 'Çok yıllık (5-50 yıl)',
    size: '30-200 cm',
    difficulty: 'Orta',
    isNew: false,
    isPopular: true,
    mainImage: 'https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=400&h=400&fit=crop',
    images: [
      'https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=400&h=400&fit=crop',
      'https://images.unsplash.com/photo-1441974231531-c6227db76b6e?w=400&h=400&fit=crop',
      'https://images.unsplash.com/photo-1563241527-3004b7be0ffd?w=400&h=400&fit=crop'
    ],
    care: {
      water: 'Düzenli sulama, haftada 2-3 kez',
      light: 'Günde 6-8 saat güneş ışığı',
      soil: 'İyi drene olan, organik madde açısından zengin toprak',
      temperature: '15-25°C ideal sıcaklık aralığı',
      humidity: '%50-70 nem oranı',
      fertilizer: 'İlkbahar ve yaz aylarında aylık gübreleme'
    },
    stages: [
      {
        stageId: 1,
        name: 'Tohum Ekim',
        duration: '1-2 hafta',
        description: 'Tohumlar nemli toprakta çimlenmeye başlar',
        image: 'https://images.unsplash.com/photo-1416879595882-3373a0480b5b?w=300&h=200&fit=crop',
        tips: ['Toprağı nemli tutun', 'Sıcaklığı 18-22°C arasında tutun']
      },
      {
        stageId: 2,
        name: 'Fide Dönemi',
        duration: '2-4 hafta',
        description: 'İlk yapraklar çıkar ve kök sistemi gelişir',
        image: 'https://images.unsplash.com/photo-1416879595882-3373a0480b5b?w=300&h=200&fit=crop',
        tips: ['Günlük sulama yapın', 'Güneş ışığına alıştırın']
      },
      {
        stageId: 3,
        name: 'Genç Bitki',
        duration: '2-3 ay',
        description: 'Bitki büyür ve dallanmaya başlar',
        image: 'https://images.unsplash.com/photo-1416879595882-3373a0480b5b?w=300&h=200&fit=crop',
        tips: ['Budama yapın', 'Gübre verin']
      },
      {
        stageId: 4,
        name: 'Çiçeklenme',
        duration: '4-6 ay',
        description: 'İlk çiçekler açar ve koku yayar',
        image: 'https://images.unsplash.com/photo-1416879595882-3373a0480b5b?w=300&h=200&fit=crop',
        tips: ['Düzenli sulama', 'Solmuş çiçekleri temizleyin']
      },
      {
        stageId: 5,
        name: 'Olgun Bitki',
        duration: 'Sürekli',
        description: 'Tam gelişmiş bitki sürekli çiçek verir',
        image: 'https://images.unsplash.com/photo-1416879595882-3373a0480b5b?w=300&h=200&fit=crop',
        tips: ['Kış bakımı yapın', 'Yıllık budama']
      }
    ],
    problems: {
      recommendations: [
        {
          problem: 'Yaprak Biti',
          description: 'Yapraklarda küçük yeşil böcekler',
          solution: 'Sabunlu su ile püskürtün veya doğal predatörler kullanın',
          products: [
            { name: 'Neem Yağı', dosage: '10ml/1L su', timing: 'Akşam saatlerinde' },
            { name: 'Sabunlu Su', dosage: '1 çorba kaşığı/1L su', timing: 'Günde 2 kez' }
          ],
          timing: 'İlk belirtilerde hemen müdahale edin'
        },
        {
          problem: 'Külleme',
          description: 'Yapraklarda beyaz toz görünümü',
          solution: 'Havalandırmayı artırın ve fungisit uygulayın',
          products: [
            { name: 'Bakır Sülfat', dosage: '2g/1L su', timing: 'Haftada 1 kez' }
          ],
          timing: 'Nemli havalarda önleyici olarak'
        }
      ]
    },
    nutritionalInfo: {
      benefits: ['Vitamin C kaynağı', 'Antioksidan özellikleri', 'Aromaterapi'],
      uses: ['Çay yapımı', 'Parfüm', 'Kozmetik', 'Dekorasyon']
    }
  },
  {
    name: 'Domates',
    latinName: 'Solanum lycopersicum',
    category: 'Sebze',
    description: 'Lezzetli ve besleyici meyveleri olan yıllık sebze bitkisi. Mutfakta vazgeçilmez, vitamin ve mineral açısından zengin.',
    origin: 'Güney Amerika (Peru, Ekvador)',
    lifespan: 'Yıllık (4-6 ay)',
    size: '50-200 cm (çeşide göre)',
    difficulty: 'Kolay',
    isNew: true,
    isPopular: true,
    mainImage: 'https://images.unsplash.com/photo-1546470427-e26264be0b0d?w=400&h=400&fit=crop',
    images: [
      'https://images.unsplash.com/photo-1546470427-e26264be0b0d?w=400&h=400&fit=crop',
      'https://images.unsplash.com/photo-1592924357228-91a4daadcfea?w=400&h=400&fit=crop',
      'https://images.unsplash.com/photo-1518977676601-b53f82aba655?w=400&h=400&fit=crop'
    ],
    care: {
      water: 'Bol ve düzenli sulama, günde 1-2 kez',
      light: 'Günde 8-10 saat doğrudan güneş ışığı',
      soil: 'Organik madde açısından zengin, pH 6.0-6.8',
      temperature: '18-30°C gündüz, 15-20°C gece',
      humidity: '%60-80 nem oranı',
      fertilizer: '15 günde bir organik gübre'
    },
    stages: [
      {
        stageId: 1,
        name: 'Tohum Çimlenmesi',
        duration: '5-10 gün',
        description: 'Tohumlar sıcak ve nemli ortamda çimlenmeye başlar',
        image: 'https://images.unsplash.com/photo-1416879595882-3373a0480b5b?w=300&h=200&fit=crop',
        tips: ['Sıcaklığı 20-25°C tutun', 'Toprağı nemli ama ıslak değil']
      },
      {
        stageId: 2,
        name: 'Fide Gelişimi',
        duration: '3-4 hafta',
        description: 'Gerçek yapraklar çıkar ve kök sistemi güçlenir',
        image: 'https://images.unsplash.com/photo-1416879595882-3373a0480b5b?w=300&h=200&fit=crop',
        tips: ['Günlük sulama', 'Yeterli ışık sağlayın']
      },
      {
        stageId: 3,
        name: 'Dikim ve Büyüme',
        duration: '4-6 hafta',
        description: 'Fideler ana yere dikilir ve hızla büyür',
        image: 'https://images.unsplash.com/photo-1416879595882-3373a0480b5b?w=300&h=200&fit=crop',
        tips: ['Destek çubuğu kullanın', 'Alt dalları budayın']
      },
      {
        stageId: 4,
        name: 'Çiçeklenme',
        duration: '6-8 hafta',
        description: 'Sarı çiçekler açar ve tozlaşma gerçekleşir',
        image: 'https://images.unsplash.com/photo-1416879595882-3373a0480b5b?w=300&h=200&fit=crop',
        tips: ['Çiçekleri hafifçe sallayın', 'Bol gübre verin']
      },
      {
        stageId: 5,
        name: 'Meyve Gelişimi',
        duration: '8-12 hafta',
        description: 'Yeşil meyveler oluşur ve büyümeye başlar',
        image: 'https://images.unsplash.com/photo-1416879595882-3373a0480b5b?w=300&h=200&fit=crop',
        tips: ['Düzenli sulama', 'Meyveleri destekleyin']
      },
      {
        stageId: 6,
        name: 'Olgunlaşma ve Hasat',
        duration: '2-4 hafta',
        description: 'Meyveler kırmızıya döner ve hasat edilir',
        image: 'https://images.unsplash.com/photo-1416879595882-3373a0480b5b?w=300&h=200&fit=crop',
        tips: ['Kırmızılaşmaya başlayınca toplayın', 'Düzenli hasat yapın']
      }
    ],
    problems: {
      recommendations: [
        {
          problem: 'Mildiyö',
          description: 'Yapraklarda kahverengi lekeler ve beyaz küf',
          solution: 'Havalandırmayı artırın ve fungisit uygulayın',
          products: [
            { name: 'Bordeaux Karışımı', dosage: '20g/1L su', timing: 'Haftada 1 kez' },
            { name: 'Bakır Oksiklorür', dosage: '3g/1L su', timing: '10 günde bir' }
          ],
          timing: 'Nemli havalarda önleyici olarak'
        },
        {
          problem: 'Beyaz Sinek',
          description: 'Yaprak altında küçük beyaz böcekler',
          solution: 'Sarı yapışkan tuzaklar ve insektisit kullanın',
          products: [
            { name: 'Neem Yağı', dosage: '15ml/1L su', timing: 'Akşam saatlerinde' }
          ],
          timing: 'İlk görüldüğünde hemen'
        }
      ]
    },
    nutritionalInfo: {
      benefits: ['Likopen antioksidanı', 'Vitamin C', 'Potasyum', 'Folat'],
      uses: ['Salata', 'Sos', 'Çorba', 'Konserve', 'Kurutma']
    }
  },
  {
    name: 'Lavanta',
    latinName: 'Lavandula angustifolia',
    category: 'Aromatik',
    description: 'Güçlü kokusu ve mor çiçekleri ile tanınan aromatik bitki. Rahatlatıcı özellikleri ile aromaterapi ve kozmetikte kullanılır.',
    origin: 'Akdeniz Bölgesi',
    lifespan: 'Çok yıllık (10-15 yıl)',
    size: '30-60 cm',
    difficulty: 'Kolay',
    isNew: false,
    isPopular: true,
    mainImage: 'https://images.unsplash.com/photo-1611909023032-2d6b3134ecba?w=400&h=400&fit=crop',
    images: [
      'https://images.unsplash.com/photo-1611909023032-2d6b3134ecba?w=400&h=400&fit=crop',
      'https://images.unsplash.com/photo-1595147389795-37094173bfd8?w=400&h=400&fit=crop'
    ],
    care: {
      water: 'Az sulama, kuraklığa dayanıklı',
      light: 'Tam güneş, günde 6-8 saat',
      soil: 'İyi drene olan, alkali toprak (pH 7-8)',
      temperature: '15-30°C, soğuğa dayanıklı',
      humidity: '%40-60 nem oranı',
      fertilizer: 'Yılda 1-2 kez organik gübre'
    },
    stages: [
      {
        stageId: 1,
        name: 'Tohum/Çelik',
        duration: '2-3 hafta',
        description: 'Tohum çimlenmesi veya çelik köklenmesi',
        image: 'https://images.unsplash.com/photo-1416879595882-3373a0480b5b?w=300&h=200&fit=crop',
        tips: ['Nemli ama ıslak olmayan toprak', 'Sıcak ortam']
      },
      {
        stageId: 2,
        name: 'Genç Bitki',
        duration: '2-3 ay',
        description: 'İlk yapraklar çıkar ve bitki güçlenir',
        image: 'https://images.unsplash.com/photo-1416879595882-3373a0480b5b?w=300&h=200&fit=crop',
        tips: ['Düzenli ama az sulama', 'Güneşli yer']
      },
      {
        stageId: 3,
        name: 'Büyüme',
        duration: '4-6 ay',
        description: 'Bitki büyür ve dallanır',
        image: 'https://images.unsplash.com/photo-1416879595882-3373a0480b5b?w=300&h=200&fit=crop',
        tips: ['Hafif budama', 'Gübre verme']
      },
      {
        stageId: 4,
        name: 'Çiçeklenme',
        duration: '6-8 ay',
        description: 'Mor çiçek başakları oluşur',
        image: 'https://images.unsplash.com/photo-1416879595882-3373a0480b5b?w=300&h=200&fit=crop',
        tips: ['Çiçek açmadan önce hasat', 'Kurutma için kesim']
      }
    ],
    problems: {
      recommendations: [
        {
          problem: 'Kök Çürüklüğü',
          description: 'Aşırı sulamadan kaynaklanan kök problemleri',
          solution: 'Sulamayı azaltın ve drenajı iyileştirin',
          products: [
            { name: 'Fungisit', dosage: '5ml/1L su', timing: 'Ayda 1 kez' }
          ],
          timing: 'Kış aylarında özellikle dikkat'
        }
      ]
    },
    nutritionalInfo: {
      benefits: ['Rahatlatıcı etki', 'Antiseptik özellik', 'Böcek kovucu'],
      uses: ['Aromaterapi', 'Çay', 'Sabun', 'Parfüm', 'Kurutma']
    }
  },
  {
    name: 'Aloe Vera',
    latinName: 'Aloe barbadensis',
    category: 'Sukulent',
    description: 'Kalın etli yaprakları olan sukulent bitki. Tıbbi özellikleri ile tanınan, bakımı kolay ev bitkisi.',
    origin: 'Arap Yarımadası',
    lifespan: 'Çok yıllık (20+ yıl)',
    size: '30-100 cm',
    difficulty: 'Çok Kolay',
    isNew: false,
    isPopular: true,
    mainImage: 'https://images.unsplash.com/photo-1509587584298-0f3b3a3a1797?w=400&h=400&fit=crop',
    images: [
      'https://images.unsplash.com/photo-1509587584298-0f3b3a3a1797?w=400&h=400&fit=crop',
      'https://images.unsplash.com/photo-1615671524827-c1fe3973b648?w=400&h=400&fit=crop'
    ],
    care: {
      water: 'Çok az sulama, ayda 1-2 kez',
      light: 'Parlak dolaylı ışık',
      soil: 'Kaktüs toprağı, çok iyi drene',
      temperature: '18-27°C, soğuktan koruyun',
      humidity: '%30-50 nem oranı',
      fertilizer: 'Yılda 1 kez kaktüs gübresi'
    },
    stages: [
      {
        stageId: 1,
        name: 'Yavru Bitki',
        duration: '1-2 ay',
        description: 'Ana bitkiden ayrılan yavru köklenir',
        image: 'https://images.unsplash.com/photo-1416879595882-3373a0480b5b?w=300&h=200&fit=crop',
        tips: ['Kuru toprakta bekletin', 'Az sulayın']
      },
      {
        stageId: 2,
        name: 'Genç Bitki',
        duration: '6-12 ay',
        description: 'Yapraklar kalınlaşır ve büyür',
        image: 'https://images.unsplash.com/photo-1416879595882-3373a0480b5b?w=300&h=200&fit=crop',
        tips: ['Güneşli yer', 'Seyrek sulama']
      },
      {
        stageId: 3,
        name: 'Olgun Bitki',
        duration: 'Sürekli',
        description: 'Tam boyutuna ulaşır ve yavru verir',
        image: 'https://images.unsplash.com/photo-1416879595882-3373a0480b5b?w=300&h=200&fit=crop',
        tips: ['Yavrularını ayırın', 'Kış bakımı']
      }
    ],
    problems: {
      recommendations: [
        {
          problem: 'Yaprak Sararması',
          description: 'Aşırı sulama veya yetersiz ışık',
          solution: 'Sulamayı azaltın ve daha aydınlık yere koyun',
          products: [],
          timing: 'Hemen müdahale edin'
        }
      ]
    },
    nutritionalInfo: {
      benefits: ['Cilt bakımı', 'Yara iyileştirici', 'Antiinflamatuar'],
      uses: ['Kozmetik', 'Tıbbi jel', 'İçecek', 'Cilt kremi']
    }
  },
  {
    name: 'Fesleğen',
    latinName: 'Ocimum basilicum',
    category: 'Aromatik',
    description: 'Güçlü aroması olan mutfak bitkisi. İtalyan mutfağının vazgeçilmezi, taze veya kurutulmuş kullanılır.',
    origin: 'Hindistan ve Güneydoğu Asya',
    lifespan: 'Yıllık (4-6 ay)',
    size: '20-60 cm',
    difficulty: 'Kolay',
    isNew: true,
    isPopular: true,
    mainImage: 'https://images.unsplash.com/photo-1618164436241-4473940d1f5c?w=400&h=400&fit=crop',
    images: [
      'https://images.unsplash.com/photo-1618164436241-4473940d1f5c?w=400&h=400&fit=crop',
      'https://images.unsplash.com/photo-1628556270448-4d4e4148e1b1?w=400&h=400&fit=crop'
    ],
    care: {
      water: 'Düzenli sulama, toprağı nemli tutun',
      light: 'Günde 6-8 saat güneş ışığı',
      soil: 'İyi drene olan, organik zengin toprak',
      temperature: '20-30°C, soğuktan koruyun',
      humidity: '%60-70 nem oranı',
      fertilizer: '15 günde bir sıvı gübre'
    },
    stages: [
      {
        stageId: 1,
        name: 'Tohum Çimlenmesi',
        duration: '5-10 gün',
        description: 'Tohumlar sıcak ortamda çimlenmeye başlar',
        image: 'https://images.unsplash.com/photo-1416879595882-3373a0480b5b?w=300&h=200&fit=crop',
        tips: ['Sıcak tutun (20-25°C)', 'Nemli toprak']
      },
      {
        stageId: 2,
        name: 'Fide Dönemi',
        duration: '2-3 hafta',
        description: 'Gerçek yapraklar çıkar',
        image: 'https://images.unsplash.com/photo-1416879595882-3373a0480b5b?w=300&h=200&fit=crop',
        tips: ['Günlük sulama', 'Güneş ışığı']
      },
      {
        stageId: 3,
        name: 'Büyüme',
        duration: '4-6 hafta',
        description: 'Bitki büyür ve dallanır',
        image: 'https://images.unsplash.com/photo-1416879595882-3373a0480b5b?w=300&h=200&fit=crop',
        tips: ['Uç alma yapın', 'Düzenli gübre']
      },
      {
        stageId: 4,
        name: 'Hasat',
        duration: 'Sürekli',
        description: 'Yapraklar hasat edilmeye hazır',
        image: 'https://images.unsplash.com/photo-1416879595882-3373a0480b5b?w=300&h=200&fit=crop',
        tips: ['Çiçek açmadan önce toplayın', 'Düzenli hasat']
      }
    ],
    problems: {
      recommendations: [
        {
          problem: 'Yaprak Biti',
          description: 'Yapraklarda küçük yeşil böcekler',
          solution: 'Sabunlu su ile püskürtün',
          products: [
            { name: 'Sabunlu Su', dosage: '1 çorba kaşığı/1L su', timing: 'Günde 2 kez' }
          ],
          timing: 'İlk görüldüğünde'
        }
      ]
    },
    nutritionalInfo: {
      benefits: ['Antioksidan', 'Antibakteriyel', 'Vitamin K'],
      uses: ['Pesto', 'Pizza', 'Salata', 'Çay', 'Kurutma']
    }
  }
];

const SAMPLE_ARTICLES = [
  {
    title: 'Bitki Bakımının Temelleri',
    content: `Bitkilerinizi sağlıklı tutmak için temel bakım kuralları:

## Sulama
Bitkiler için en önemli ihtiyaçlardan biri sudur. Doğru sulama teknikleri:
- Toprağın nemini kontrol edin
- Sabah saatlerinde sulama yapın
- Yapraklara değil, köklere su verin
- Mevsime göre sulama sıklığını ayarlayın

## Işık İhtiyacı
Her bitki farklı ışık ihtiyacına sahiptir:
- Güneş seven bitkiler: Günde 6-8 saat doğrudan ışık
- Yarı gölge bitkiler: Günde 4-6 saat dolaylı ışık
- Gölge bitkiler: Günde 2-4 saat dolaylı ışık

## Toprak ve Gübreleme
Sağlıklı toprak, sağlıklı bitki demektir:
- İyi drene olan toprak kullanın
- Organik madde ekleyin
- pH seviyesini kontrol edin
- Düzenli gübreleme yapın

## Budama ve Bakım
Düzenli bakım bitkinin gelişimini destekler:
- Ölü yaprakları temizleyin
- Hastalıklı kısımları kesin
- Şekil verme budaması yapın
- Zararlıları kontrol edin`,
    summary: 'Bitki bakımı hakkında temel bilgiler ve pratik ipuçları',
    category: 'Bakım',
    image: 'https://images.unsplash.com/photo-1416879595882-3373a0480b5b?w=400&h=200&fit=crop',
    readTime: 8,
    author: 'Bahçıvan Ahmet',
    publishDate: '2024-01-15',
    tags: ['bakım', 'sulama', 'budama', 'temel bilgiler']
  },
  {
    title: 'Doğru Sulama Teknikleri',
    content: `Bitkilerinizi doğru şekilde sulamak için detaylı rehber:

## Sulama Zamanı
En ideal sulama zamanları:
- Sabah erken saatler (06:00-09:00)
- Akşam geç saatler (18:00-20:00)
- Öğlen saatlerinden kaçının
- Rüzgarlı günlerde sulama yapmayın

## Su Kalitesi
Kullanacağınız suyun özellikleri:
- Musluk suyu: 24 saat bekletin (klor uçması için)
- Yağmur suyu: En ideal seçenek
- Damıtık su: Besin eksikliği yaratabilir
- Sert su: Kireç birikimi yapar

## Sulama Miktarı
Bitki türüne göre sulama:
- Sukulent bitkiler: Ayda 1-2 kez
- Çiçekli bitkiler: Haftada 2-3 kez
- Sebze bitkileri: Günlük
- Ağaçlar: Haftada 1 kez derin sulama

## Sulama Yöntemleri
Farklı sulama teknikleri:
- Damla sulama: Su tasarrufu sağlar
- Yağmurlama: Geniş alanlar için
- Elden sulama: Küçük bitkiler için
- Dip sulama: Hassas bitkiler için`,
    summary: 'Sulama teknikleri ve doğru sulama yöntemleri rehberi',
    category: 'Sulama',
    image: 'https://images.unsplash.com/photo-1416879595882-3373a0480b5b?w=400&h=200&fit=crop',
    readTime: 6,
    author: 'Su Uzmanı Ayşe',
    publishDate: '2024-01-20',
    tags: ['sulama', 'su', 'teknik', 'yöntem']
  },
  {
    title: 'Ev İçi Bitki Seçimi',
    content: `Eviniz için en uygun bitkileri seçme rehberi:

## Işık Durumuna Göre Seçim
Evinizin ışık durumunu değerlendirin:
- Güney cepheli pencereler: Kaktüs, sukulent
- Kuzey cepheli pencereler: Pothos, sansevieria
- Doğu/Batı cepheli: Ficus, monstera
- Karanlık köşeler: ZZ plant, cast iron plant

## Bakım Seviyesine Göre
Deneyim seviyenize uygun bitkiler:
- Başlangıç seviyesi: Pothos, sansevieria, aloe vera
- Orta seviye: Ficus, monstera, peace lily
- İleri seviye: Orkide, bonsai, carnivorous plants

## Hava Kalitesi İyileştiren Bitkiler
NASA'nın önerdiği hava temizleyici bitkiler:
- Sansevieria (Kaplan dili)
- Pothos (Altın pothos)
- Peace Lily (Barış çiçeği)
- Spider Plant (Örümcek bitkisi)
- Rubber Plant (Kauçuk ağacı)

## Evcil Hayvan Dostu Bitkiler
Evcil hayvanlarınız için güvenli seçenekler:
- Spider plant
- Boston fern
- Parlor palm
- Prayer plant
- Peperomia`,
    summary: 'Ev içi bitki seçimi için kapsamlı rehber',
    category: 'Ev Bitkileri',
    image: 'https://images.unsplash.com/photo-1416879595882-3373a0480b5b?w=400&h=200&fit=crop',
    readTime: 10,
    author: 'İç Mekan Uzmanı Mehmet',
    publishDate: '2024-02-01',
    tags: ['ev bitkisi', 'seçim', 'iç mekan', 'hava kalitesi']
  },
  {
    title: 'Organik Gübre Yapımı',
    content: `Evde organik gübre hazırlama yöntemleri:

## Kompost Yapımı
Evde kompost hazırlama adımları:
- Yeşil malzemeler: Sebze artıkları, çim
- Kahverengi malzemeler: Kuru yaprak, kağıt
- Karıştırma oranı: 3:1 (kahverengi:yeşil)
- Düzenli karıştırma ve nemlendirme
- 3-6 ay bekletme süresi

## Sıvı Gübre Yapımı
Hızlı etki eden sıvı gübreler:
- Muz kabuğu gübresi: Potasyum açısından zengin
- Yumurta kabuğu gübresi: Kalsiyum kaynağı
- Kahve telvesi gübresi: Asit seven bitkiler için
- Balık suyu gübresi: Azot açısından zengin

## Doğal Pestisit Tarifleri
Zararlılara karşı doğal çözümler:
- Sabunlu su: Yaprak bitleri için
- Neem yağı: Genel amaçlı pestisit
- Sarımsak suyu: Böcek kovucu
- Karabiber çözeltisi: Karınca kovucu

## Uygulama Zamanları
Gübre uygulama takvimi:
- İlkbahar: Büyüme döneminde yoğun gübreleme
- Yaz: Düzenli sıvı gübre uygulaması
- Sonbahar: Kış hazırlığı için potasyum
- Kış: Dinlenme döneminde minimal gübre`,
    summary: 'Evde organik gübre yapımı ve doğal pestisit tarifleri',
    category: 'Gübreleme',
    image: 'https://images.unsplash.com/photo-1416879595882-3373a0480b5b?w=400&h=200&fit=crop',
    readTime: 12,
    author: 'Organik Bahçıvan Fatma',
    publishDate: '2024-02-10',
    tags: ['organik', 'gübre', 'kompost', 'doğal pestisit']
  },
  {
    title: 'Mevsimsel Bahçe Bakımı',
    content: `Mevsimlere göre bahçe bakım rehberi:

## İlkbahar Bakımı (Mart-Mayıs)
Bahçenizi yeni sezona hazırlayın:
- Budama işlemlerini tamamlayın
- Yeni ekimler için toprak hazırlığı
- Gübre uygulaması yapın
- Zararlı kontrolü başlatın
- Sulama sistemini kontrol edin

## Yaz Bakımı (Haziran-Ağustos)
Sıcak günlerde bakım ipuçları:
- Erken sabah veya akşam sulaması
- Mulch (örtü) kullanarak nem koruma
- Düzenli hasat yapın
- Gölgeleme sağlayın
- Zararlı takibi yapın

## Sonbahar Bakımı (Eylül-Kasım)
Kış hazırlığı döneminde:
- Son hasat işlemleri
- Kış bitkileri ekimi
- Yaprak toplama ve kompost yapımı
- Sulama azaltma
- Dondan koruma hazırlıkları

## Kış Bakımı (Aralık-Şubat)
Dinlenme döneminde yapılacaklar:
- Minimal sulama
- Kapalı alan bitki bakımı
- Tohum siparişi ve planlama
- Araç gereç bakımı
- Bahçe planlaması`,
    summary: 'Mevsimlere göre bahçe bakım takvimi ve ipuçları',
    category: 'Mevsimsel Bakım',
    image: 'https://images.unsplash.com/photo-1416879595882-3373a0480b5b?w=400&h=200&fit=crop',
    readTime: 9,
    author: 'Mevsim Uzmanı Ali',
    publishDate: '2024-02-15',
    tags: ['mevsim', 'takvim', 'bahçe', 'planlama']
  }
];

const SAMPLE_CATEGORIES = [
  {
    name: 'Çiçek',
    description: 'Güzel çiçekleri olan süs bitkileri',
    icon: 'flower',
    color: '#FF6B9D',
    plantCount: 0,
    characteristics: ['Renkli çiçekler', 'Dekoratif', 'Koku'],
    careLevel: 'Orta',
    popularPlants: ['Gül', 'Lale', 'Karanfil']
  },
  {
    name: 'Sebze',
    description: 'Yenilebilir meyve ve yaprakları olan bitkiler',
    icon: 'nutrition',
    color: '#4ECDC4',
    plantCount: 0,
    characteristics: ['Yenilebilir', 'Besleyici', 'Hızlı büyüme'],
    careLevel: 'Kolay',
    popularPlants: ['Domates', 'Salatalık', 'Biber']
  },
  {
    name: 'Aromatik',
    description: 'Güçlü kokuya sahip mutfak ve tıbbi bitkiler',
    icon: 'leaf',
    color: '#45B7D1',
    plantCount: 0,
    characteristics: ['Güçlü aroma', 'Mutfakta kullanım', 'Tıbbi özellik'],
    careLevel: 'Kolay',
    popularPlants: ['Fesleğen', 'Lavanta', 'Biberiye']
  },
  {
    name: 'Sukulent',
    description: 'Su depolayan kalın yapraklı bitkiler',
    icon: 'cactus',
    color: '#96CEB4',
    plantCount: 0,
    characteristics: ['Az su ihtiyacı', 'Kalın yapraklar', 'Dayanıklı'],
    careLevel: 'Çok Kolay',
    popularPlants: ['Aloe Vera', 'Kaktüs', 'Echeveria']
  }
];

// API Routes

// Health Check
app.get('/api/health', (req, res) => {
  res.json({ 
    status: 'OK', 
    message: 'WikiPlant Admin API is running',
    timestamp: new Date().toISOString()
  });
});

// Get all plants
app.get('/api/plants', async (req, res) => {
  try {
    const plantsRef = collection(db, 'plants');
    const snapshot = await getDocs(plantsRef);
    const plants = snapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    }));
    
    res.json({
      success: true,
      data: plants,
      count: plants.length
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Load sample plants
app.post('/api/load-plants', async (req, res) => {
  try {
    const batch = writeBatch(db);
    const plantsRef = collection(db, 'plants');
    
    SAMPLE_PLANTS.forEach((plant) => {
      const docRef = doc(plantsRef);
      batch.set(docRef, {
        ...plant,
        createdAt: new Date(),
        updatedAt: new Date()
      });
    });
    
    await batch.commit();
    
    res.json({
      success: true,
      message: `${SAMPLE_PLANTS.length} plants loaded successfully`,
      count: SAMPLE_PLANTS.length
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Load sample articles
app.post('/api/load-articles', async (req, res) => {
  try {
    const batch = writeBatch(db);
    const articlesRef = collection(db, 'articles');
    
    SAMPLE_ARTICLES.forEach((article) => {
      const docRef = doc(articlesRef);
      batch.set(docRef, {
        ...article,
        createdAt: new Date(),
        updatedAt: new Date()
      });
    });
    
    await batch.commit();
    
    res.json({
      success: true,
      message: `${SAMPLE_ARTICLES.length} articles loaded successfully`,
      count: SAMPLE_ARTICLES.length
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Load sample categories
app.post('/api/load-categories', async (req, res) => {
  try {
    const batch = writeBatch(db);
    const categoriesRef = collection(db, 'categories');
    
    SAMPLE_CATEGORIES.forEach((category) => {
      const docRef = doc(categoriesRef);
      batch.set(docRef, {
        ...category,
        createdAt: new Date(),
        updatedAt: new Date()
      });
    });
    
    await batch.commit();
    
    res.json({
      success: true,
      message: `${SAMPLE_CATEGORIES.length} categories loaded successfully`,
      count: SAMPLE_CATEGORIES.length
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Load all data
app.post('/api/load-all', async (req, res) => {
  try {
    const batch = writeBatch(db);
    
    // Add plants
    const plantsRef = collection(db, 'plants');
    SAMPLE_PLANTS.forEach((plant) => {
      const docRef = doc(plantsRef);
      batch.set(docRef, {
        ...plant,
        createdAt: new Date(),
        updatedAt: new Date()
      });
    });
    
    // Add articles
    const articlesRef = collection(db, 'articles');
    SAMPLE_ARTICLES.forEach((article) => {
      const docRef = doc(articlesRef);
      batch.set(docRef, {
        ...article,
        createdAt: new Date(),
        updatedAt: new Date()
      });
    });
    
    // Add categories
    const categoriesRef = collection(db, 'categories');
    SAMPLE_CATEGORIES.forEach((category) => {
      const docRef = doc(categoriesRef);
      batch.set(docRef, {
        ...category,
        createdAt: new Date(),
        updatedAt: new Date()
      });
    });
    
    await batch.commit();
    
    res.json({
      success: true,
      message: 'All data loaded successfully',
      counts: {
        plants: SAMPLE_PLANTS.length,
        articles: SAMPLE_ARTICLES.length,
        categories: SAMPLE_CATEGORIES.length
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Custom data upload
app.post('/api/upload-plants', async (req, res) => {
  try {
    const { plants } = req.body;
    
    if (!plants || !Array.isArray(plants)) {
      return res.status(400).json({
        success: false,
        error: 'Plants array is required'
      });
    }
    
    const batch = writeBatch(db);
    const plantsRef = collection(db, 'plants');
    
    plants.forEach((plant) => {
      const docRef = doc(plantsRef);
      batch.set(docRef, {
        ...plant,
        createdAt: new Date(),
        updatedAt: new Date()
      });
    });
    
    await batch.commit();
    
    res.json({
      success: true,
      message: `${plants.length} custom plants uploaded successfully`,
      count: plants.length
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Start server
app.listen(PORT, () => {
  console.log(`🌱 WikiPlant Admin API running on port ${PORT}`);
  console.log(`📊 Health check: http://localhost:${PORT}/api/health`);
  console.log(`🚀 Load all data: POST http://localhost:${PORT}/api/load-all`);
});

module.exports = app;
