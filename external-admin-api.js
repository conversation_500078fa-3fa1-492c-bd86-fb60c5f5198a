// External Admin API for WikiPlant Data Loading
// This file can be used to create a separate web admin panel

const express = require('express');
const cors = require('cors');
const { initializeApp } = require('firebase/app');
const { 
  getFirestore, 
  collection, 
  addDoc, 
  getDocs, 
  writeBatch,
  doc 
} = require('firebase/firestore');

const app = express();
const PORT = process.env.PORT || 3001;

// Middleware
app.use(cors());
app.use(express.json());

// Firebase Configuration (same as your mobile app)
const firebaseConfig = {
  apiKey: "AIzaSyCT9Tn0Z7MVjKeonhNuovpdbXVz7syQwJ8",
  authDomain: "wikiplant-aebaf.firebaseapp.com",
  projectId: "wikiplant-aebaf",
  storageBucket: "wikiplant-aebaf.firebasestorage.app",
  messagingSenderId: "847754813681",
  appId: "1:847754813681:web:22559d4547eca2ab594950",
  measurementId: "G-27R9QPVXF6"
};

// Initialize Firebase
const firebaseApp = initializeApp(firebaseConfig);
const db = getFirestore(firebaseApp);

// Sample Data
const SAMPLE_PLANTS = [
  {
    name: 'Gül',
    latinName: 'Rosa',
    category: 'Çiçek',
    description: 'Güzel kokulu ve renkli çiçeklere sahip bitki.',
    origin: 'Asya',
    lifespan: 'Çok yıllık',
    size: '30-200 cm',
    difficulty: 'Orta',
    isNew: false,
    isPopular: true,
    mainImage: 'https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=400&h=400&fit=crop',
    care: {
      water: 'Düzenli',
      light: 'Güneşli',
      soil: 'Drenajlı',
      temperature: '15-25°C'
    },
    stages: [
      { stageId: 1, name: 'Tohum', duration: '1-2 hafta' },
      { stageId: 2, name: 'Fide', duration: '2-4 hafta' },
      { stageId: 3, name: 'Genç Bitki', duration: '2-3 ay' },
      { stageId: 4, name: 'Olgun Bitki', duration: 'Sürekli' }
    ]
  },
  {
    name: 'Domates',
    latinName: 'Solanum lycopersicum',
    category: 'Sebze',
    description: 'Lezzetli ve besleyici meyveleri olan sebze bitkisi.',
    origin: 'Güney Amerika',
    lifespan: 'Yıllık',
    size: '50-200 cm',
    difficulty: 'Kolay',
    isNew: true,
    isPopular: true,
    mainImage: 'https://images.unsplash.com/photo-1546470427-e26264be0b0d?w=400&h=400&fit=crop',
    care: {
      water: 'Bol',
      light: 'Güneşli',
      soil: 'Organik',
      temperature: '18-30°C'
    },
    stages: [
      { stageId: 1, name: 'Tohum', duration: '5-10 gün' },
      { stageId: 2, name: 'Fide', duration: '3-4 hafta' },
      { stageId: 3, name: 'Çiçeklenme', duration: '6-8 hafta' },
      { stageId: 4, name: 'Meyve', duration: '8-12 hafta' }
    ]
  }
];

const SAMPLE_ARTICLES = [
  {
    title: 'Bitki Bakımının Temelleri',
    content: 'Bitkilerinizi sağlıklı tutmak için temel bakım kuralları...',
    summary: 'Bitki bakımı hakkında temel bilgiler',
    category: 'Bakım',
    image: 'https://images.unsplash.com/photo-1416879595882-3373a0480b5b?w=400&h=200&fit=crop',
    readTime: 5
  },
  {
    title: 'Doğru Sulama Teknikleri',
    content: 'Bitkilerinizi doğru şekilde sulamak için ipuçları...',
    summary: 'Sulama teknikleri rehberi',
    category: 'Sulama',
    image: 'https://images.unsplash.com/photo-1416879595882-3373a0480b5b?w=400&h=200&fit=crop',
    readTime: 7
  }
];

const SAMPLE_CATEGORIES = [
  { name: 'Çiçek', description: 'Çiçekli bitkiler', icon: 'flower', color: '#FF6B9D' },
  { name: 'Sebze', description: 'Sebze bitkileri', icon: 'nutrition', color: '#4ECDC4' },
  { name: 'Aromatik', description: 'Aromatik bitkiler', icon: 'leaf', color: '#45B7D1' },
  { name: 'Sukulent', description: 'Sukulent bitkiler', icon: 'cactus', color: '#96CEB4' }
];

// API Routes

// Health Check
app.get('/api/health', (req, res) => {
  res.json({ 
    status: 'OK', 
    message: 'WikiPlant Admin API is running',
    timestamp: new Date().toISOString()
  });
});

// Get all plants
app.get('/api/plants', async (req, res) => {
  try {
    const plantsRef = collection(db, 'plants');
    const snapshot = await getDocs(plantsRef);
    const plants = snapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    }));
    
    res.json({
      success: true,
      data: plants,
      count: plants.length
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Load sample plants
app.post('/api/load-plants', async (req, res) => {
  try {
    const batch = writeBatch(db);
    const plantsRef = collection(db, 'plants');
    
    SAMPLE_PLANTS.forEach((plant) => {
      const docRef = doc(plantsRef);
      batch.set(docRef, {
        ...plant,
        createdAt: new Date(),
        updatedAt: new Date()
      });
    });
    
    await batch.commit();
    
    res.json({
      success: true,
      message: `${SAMPLE_PLANTS.length} plants loaded successfully`,
      count: SAMPLE_PLANTS.length
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Load sample articles
app.post('/api/load-articles', async (req, res) => {
  try {
    const batch = writeBatch(db);
    const articlesRef = collection(db, 'articles');
    
    SAMPLE_ARTICLES.forEach((article) => {
      const docRef = doc(articlesRef);
      batch.set(docRef, {
        ...article,
        createdAt: new Date(),
        updatedAt: new Date()
      });
    });
    
    await batch.commit();
    
    res.json({
      success: true,
      message: `${SAMPLE_ARTICLES.length} articles loaded successfully`,
      count: SAMPLE_ARTICLES.length
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Load sample categories
app.post('/api/load-categories', async (req, res) => {
  try {
    const batch = writeBatch(db);
    const categoriesRef = collection(db, 'categories');
    
    SAMPLE_CATEGORIES.forEach((category) => {
      const docRef = doc(categoriesRef);
      batch.set(docRef, {
        ...category,
        createdAt: new Date(),
        updatedAt: new Date()
      });
    });
    
    await batch.commit();
    
    res.json({
      success: true,
      message: `${SAMPLE_CATEGORIES.length} categories loaded successfully`,
      count: SAMPLE_CATEGORIES.length
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Load all data
app.post('/api/load-all', async (req, res) => {
  try {
    const batch = writeBatch(db);
    
    // Add plants
    const plantsRef = collection(db, 'plants');
    SAMPLE_PLANTS.forEach((plant) => {
      const docRef = doc(plantsRef);
      batch.set(docRef, {
        ...plant,
        createdAt: new Date(),
        updatedAt: new Date()
      });
    });
    
    // Add articles
    const articlesRef = collection(db, 'articles');
    SAMPLE_ARTICLES.forEach((article) => {
      const docRef = doc(articlesRef);
      batch.set(docRef, {
        ...article,
        createdAt: new Date(),
        updatedAt: new Date()
      });
    });
    
    // Add categories
    const categoriesRef = collection(db, 'categories');
    SAMPLE_CATEGORIES.forEach((category) => {
      const docRef = doc(categoriesRef);
      batch.set(docRef, {
        ...category,
        createdAt: new Date(),
        updatedAt: new Date()
      });
    });
    
    await batch.commit();
    
    res.json({
      success: true,
      message: 'All data loaded successfully',
      counts: {
        plants: SAMPLE_PLANTS.length,
        articles: SAMPLE_ARTICLES.length,
        categories: SAMPLE_CATEGORIES.length
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Custom data upload
app.post('/api/upload-plants', async (req, res) => {
  try {
    const { plants } = req.body;
    
    if (!plants || !Array.isArray(plants)) {
      return res.status(400).json({
        success: false,
        error: 'Plants array is required'
      });
    }
    
    const batch = writeBatch(db);
    const plantsRef = collection(db, 'plants');
    
    plants.forEach((plant) => {
      const docRef = doc(plantsRef);
      batch.set(docRef, {
        ...plant,
        createdAt: new Date(),
        updatedAt: new Date()
      });
    });
    
    await batch.commit();
    
    res.json({
      success: true,
      message: `${plants.length} custom plants uploaded successfully`,
      count: plants.length
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Start server
app.listen(PORT, () => {
  console.log(`🌱 WikiPlant Admin API running on port ${PORT}`);
  console.log(`📊 Health check: http://localhost:${PORT}/api/health`);
  console.log(`🚀 Load all data: POST http://localhost:${PORT}/api/load-all`);
});

module.exports = app;
