import React, { useState } from 'react';
import {
  View,
  Text,
  Image,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  StyleSheet,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

// Context
import { useFavorites } from '../contexts/FavoritesContext';

// Mock stage data - Firebase entegrasyonu için hazır
const mockStages = [
  {
    id: '1',
    order: 1,
    name: '<PERSON><PERSON> Ekim',
    description: 'Tohumun toprakta ekilmesi ve ilk bakım aşaması. Uygun derinlikte ekim yapılmalı.',
    image: 'https://images.unsplash.com/photo-1416879595882-3373a0480b5b?w=400&h=400&fit=crop'
  },
  {
    id: '2',
    order: 2,
    name: '<PERSON><PERSON><PERSON><PERSON>',
    description: 'Tohumun çimlenmesi ve ilk yaprakların çıkması. Düzenli sulama gereklidir.',
    image: 'https://images.unsplash.com/photo-1585320806297-9794b3e4eeae?w=400&h=400&fit=crop'
  },
  {
    id: '3',
    order: 3,
    name: '<PERSON><PERSON> Dönemi',
    description: '<PERSON><PERSON>n güçlenmesi ve büyümesi. Gübre desteği verilebilir.',
    image: 'https://images.unsplash.com/photo-1416879595882-3373a0480b5b?w=400&h=400&fit=crop'
  },
  {
    id: '4',
    order: 4,
    name: 'Olgunlaşma',
    description: 'Bitkinin tam olgunluğa erişmesi ve çiçek açması.',
    image: 'https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=400&h=400&fit=crop'
  }
];

const PlantDetailPage = ({ route, navigation }) => {
  const { plantId, plant } = route.params;
  const [currentStage, setCurrentStage] = useState(1);
  const [plantStages] = useState(mockStages); // Firebase'den gelecek

  const { favorites, addFavorite, removeFavorite } = useFavorites();
  const isFavorite = favorites.some(fav => fav.id === plantId);

  // Favori toggle
  const handleFavoritePress = () => {
    if (isFavorite) {
      removeFavorite(plantId);
    } else {
      addFavorite(plant);
    }
  };

  // Aşama değiştir
  const handleStageChange = (stageIndex) => {
    setCurrentStage(stageIndex + 1);
  };

  // Mevcut aşama bilgisi
  const getCurrentStageInfo = () => {
    return plantStages[currentStage - 1] || null;
  };

  // Geri git
  const handleGoBack = () => {
    navigation.goBack();
  };

  if (!plant) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.errorContainer}>
          <Ionicons name="leaf" size={64} color="#CCCCCC" />
          <Text style={styles.errorText}>Bitki bulunamadı</Text>
        </View>
      </SafeAreaView>
    );
  }

  const currentStageInfo = getCurrentStageInfo();

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity style={styles.backButton} onPress={handleGoBack}>
            <Ionicons name="arrow-back" size={24} color="#FFFFFF" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>{plant.name}</Text>
          <TouchableOpacity style={styles.favoriteButton} onPress={handleFavoritePress}>
            <Ionicons 
              name={isFavorite ? "heart" : "heart-outline"} 
              size={24} 
              color={isFavorite ? "#FF6B6B" : "#FFFFFF"} 
            />
          </TouchableOpacity>
        </View>

        {/* Plant Image */}
        <View style={styles.imageContainer}>
          <Image
            source={{
              uri: currentStageInfo?.image || plant.mainImage || 'https://images.unsplash.com/photo-1416879595882-3373a0480b5b?w=400&h=400&fit=crop'
            }}
            style={styles.plantImage}
            resizeMode="cover"
          />
        </View>

        {/* Plant Info */}
        <View style={styles.content}>
          <Text style={styles.plantName}>{plant.name}</Text>
          <Text style={styles.latinName}>{plant.latinName}</Text>
          <Text style={styles.category}>{plant.category}</Text>
          <Text style={styles.description}>{plant.description}</Text>

          {/* Stages */}
          {plantStages.length > 0 && (
            <View style={styles.stagesSection}>
              <Text style={styles.sectionTitle}>Büyüme Aşamaları</Text>
              
              {/* Stage Indicators */}
              <View style={styles.stageIndicators}>
                {plantStages.map((stage, index) => (
                  <TouchableOpacity
                    key={stage.id}
                    style={[
                      styles.stageIndicator,
                      currentStage === index + 1 && styles.activeStageIndicator
                    ]}
                    onPress={() => handleStageChange(index)}
                  >
                    <Text style={[
                      styles.stageNumber,
                      currentStage === index + 1 && styles.activeStageNumber
                    ]}>
                      {index + 1}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>

              {/* Current Stage Info */}
              {currentStageInfo && (
                <View style={styles.stageInfo}>
                  <Text style={styles.stageName}>{currentStageInfo.name}</Text>
                  <Text style={styles.stageDescription}>{currentStageInfo.description}</Text>
                </View>
              )}
            </View>
          )}

          {/* Care Info */}
          {plant.care && (
            <View style={styles.careSection}>
              <Text style={styles.sectionTitle}>Bakım Bilgileri</Text>
              <View style={styles.careGrid}>
                {plant.care.water && (
                  <View style={styles.careItem}>
                    <Ionicons name="water" size={20} color="#7C4D25" />
                    <Text style={styles.careLabel}>Sulama</Text>
                    <Text style={styles.careValue}>{plant.care.water}</Text>
                  </View>
                )}
                {plant.care.light && (
                  <View style={styles.careItem}>
                    <Ionicons name="sunny" size={20} color="#FC7138" />
                    <Text style={styles.careLabel}>Işık</Text>
                    <Text style={styles.careValue}>{plant.care.light}</Text>
                  </View>
                )}
                {plant.care.soil && (
                  <View style={styles.careItem}>
                    <Ionicons name="earth" size={20} color="#7C4D25" />
                    <Text style={styles.careLabel}>Toprak</Text>
                    <Text style={styles.careValue}>{plant.care.soil}</Text>
                  </View>
                )}
                {plant.care.temperature && (
                  <View style={styles.careItem}>
                    <Ionicons name="thermometer" size={20} color="#FF6B6B" />
                    <Text style={styles.careLabel}>Sıcaklık</Text>
                    <Text style={styles.careValue}>{plant.care.temperature}</Text>
                  </View>
                )}
              </View>
            </View>
          )}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

// Stiller
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  scrollView: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: '#FC7138',
    paddingHorizontal: 16,
    paddingVertical: 12,
    paddingTop: 40,
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    flex: 1,
    fontSize: 18,
    fontWeight: 'bold',
    color: '#FFFFFF',
    textAlign: 'center',
  },
  favoriteButton: {
    padding: 8,
  },
  imageContainer: {
    height: 250,
  },
  plantImage: {
    width: '100%',
    height: '100%',
  },
  content: {
    padding: 16,
  },
  plantName: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333333',
    marginBottom: 4,
  },
  latinName: {
    fontSize: 16,
    fontStyle: 'italic',
    color: '#CCCCCC',
    marginBottom: 8,
  },
  category: {
    fontSize: 14,
    color: '#7C4D25',
    fontWeight: '600',
    marginBottom: 12,
  },
  description: {
    fontSize: 16,
    color: '#333333',
    lineHeight: 24,
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333333',
    marginBottom: 16,
  },
  stagesSection: {
    marginBottom: 24,
  },
  stageIndicators: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginBottom: 16,
  },
  stageIndicator: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#F5F5F5',
    justifyContent: 'center',
    alignItems: 'center',
    marginHorizontal: 4,
  },
  activeStageIndicator: {
    backgroundColor: '#7C4D25',
  },
  stageNumber: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#CCCCCC',
  },
  activeStageNumber: {
    color: '#FFFFFF',
  },
  stageInfo: {
    backgroundColor: '#FFFFFF',
    padding: 16,
    borderRadius: 12,
  },
  stageName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333333',
    marginBottom: 8,
  },
  stageDescription: {
    fontSize: 14,
    color: '#333333',
    lineHeight: 20,
  },
  careSection: {
    marginBottom: 24,
  },
  careGrid: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
  },
  careItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  careLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333333',
    marginLeft: 12,
    flex: 1,
  },
  careValue: {
    fontSize: 14,
    color: '#CCCCCC',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
  },
  errorText: {
    fontSize: 16,
    color: '#CCCCCC',
    marginTop: 16,
    textAlign: 'center',
  },
});

export default PlantDetailPage;
