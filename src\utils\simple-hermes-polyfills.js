// Simple and safe Hermes polyfills to prevent illegal invocation errors
import 'react-native-url-polyfill/auto';

// Prevent illegal invocation by ensuring proper context
const createSafeMethod = (obj, methodName, fallback) => {
  try {
    if (obj && typeof obj[methodName] === 'function') {
      return function(...args) {
        try {
          return obj[methodName].apply(obj, args);
        } catch (e) {
          if (fallback) return fallback(...args);
          throw e;
        }
      };
    }
    return fallback;
  } catch (e) {
    return fallback;
  }
};

// Safe global object access
const safeGlobal = (() => {
  try {
    return global;
  } catch (e) {
    try {
      return window;
    } catch (e2) {
      return {};
    }
  }
})();

// Minimal URL polyfill
if (!safeGlobal.URL) {
  try {
    const { URL: PolyfillURL } = require('react-native-url-polyfill');
    safeGlobal.URL = PolyfillURL;
  } catch (e) {
    // Minimal URL implementation
    safeGlobal.URL = class URL {
      constructor(url, base) {
        this.href = url;
        this.protocol = url.split(':')[0] + ':';
        this.host = '';
        this.pathname = '';
        this.search = '';
        this.hash = '';
      }
      toString() { return this.href; }
      toJSON() { return this.href; }
    };
  }
}

// Minimal URLSearchParams polyfill
if (!safeGlobal.URLSearchParams) {
  try {
    const { URLSearchParams: PolyfillURLSearchParams } = require('react-native-url-polyfill');
    safeGlobal.URLSearchParams = PolyfillURLSearchParams;
  } catch (e) {
    safeGlobal.URLSearchParams = class URLSearchParams {
      constructor() {
        this._data = new Map();
      }
      get(name) { return this._data.get(name) || null; }
      set(name, value) { this._data.set(name, value); }
      has(name) { return this._data.has(name); }
      delete(name) { this._data.delete(name); }
      toString() { return ''; }
    };
  }
}

// Safe console methods
if (safeGlobal.console) {
  const originalConsole = safeGlobal.console;
  ['log', 'warn', 'error', 'info', 'debug'].forEach(method => {
    if (!originalConsole[method]) {
      originalConsole[method] = createSafeMethod(originalConsole, 'log', () => {});
    }
  });
}

// Safe performance object
if (!safeGlobal.performance) {
  safeGlobal.performance = {
    now: () => Date.now(),
    mark: () => {},
    measure: () => {},
    getEntriesByName: () => [],
    getEntriesByType: () => []
  };
}

// Safe fetch (use React Native's built-in)
if (!safeGlobal.fetch) {
  safeGlobal.fetch = createSafeMethod(
    safeGlobal,
    'fetch',
    () => Promise.reject(new Error('Fetch not available'))
  );
}

// Safe requestAnimationFrame
if (!safeGlobal.requestAnimationFrame) {
  safeGlobal.requestAnimationFrame = (callback) => {
    return setTimeout(callback, 16);
  };
  safeGlobal.cancelAnimationFrame = (id) => {
    clearTimeout(id);
  };
}

// Minimal Blob polyfill
if (!safeGlobal.Blob) {
  safeGlobal.Blob = class Blob {
    constructor(parts = [], options = {}) {
      this.parts = parts;
      this.type = options.type || '';
      this.size = 0;
    }
    slice() { return new safeGlobal.Blob(); }
    text() { return Promise.resolve(''); }
    arrayBuffer() { return Promise.resolve(new ArrayBuffer(0)); }
  };
}

// Minimal File polyfill
if (!safeGlobal.File) {
  safeGlobal.File = class File extends safeGlobal.Blob {
    constructor(parts, name, options = {}) {
      super(parts, options);
      this.name = name;
      this.lastModified = Date.now();
    }
  };
}

// Minimal FormData polyfill
if (!safeGlobal.FormData) {
  safeGlobal.FormData = class FormData {
    constructor() {
      this._data = new Map();
    }
    append(name, value) {
      this._data.set(name, value);
    }
    get(name) {
      return this._data.get(name) || null;
    }
    has(name) {
      return this._data.has(name);
    }
    delete(name) {
      this._data.delete(name);
    }
    set(name, value) {
      this._data.set(name, value);
    }
  };
}

console.log('🔧 Simple Hermes polyfills loaded successfully');
