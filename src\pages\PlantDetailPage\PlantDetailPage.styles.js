import { StyleSheet, Dimensions } from 'react-native';
import { COLORS, FONT_SIZES, SPACING } from '../../utils/constants';

const { height } = Dimensions.get('window');

export default StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },

  scrollView: {
    flex: 1,
    paddingTop: SPACING.sm,
  },
  
  headerFavoriteButton: {
    marginRight: SPACING.md,
  },
  
  imageContainer: {
    height: height * 0.35,
    position: 'relative',
    marginHorizontal: SPACING.md,
    borderRadius: 20,
    overflow: 'hidden',
    marginBottom: SPACING.sm,
  },
  
  stageImage: {
    width: '100%',
    height: '100%',
  },
  
  imageOverlay: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    padding: SPACING.lg,
    borderBottomLeftRadius: 20,
    borderBottomRightRadius: 20,
  },
  
  stageTitle: {
    fontSize: FONT_SIZES.title,
    fontWeight: '700',
    color: COLORS.white,
    marginBottom: SPACING.xs,
  },
  
  stageDuration: {
    fontSize: FONT_SIZES.medium,
    color: COLORS.white,
    opacity: 0.9,
  },
  
  content: {
    padding: SPACING.lg,
    paddingBottom: SPACING.tabBar, // Extra space for floating tab bar
  },
  
  basicInfo: {
    marginBottom: SPACING.xl,
    backgroundColor: COLORS.white,
    padding: SPACING.lg,
    borderRadius: 16,
    shadowColor: COLORS.shadow,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  
  plantName: {
    fontSize: FONT_SIZES.header,
    fontWeight: '700',
    color: COLORS.primary,
    marginBottom: SPACING.xs,
  },
  
  latinName: {
    fontSize: FONT_SIZES.large,
    fontStyle: 'italic',
    color: COLORS.darkGray,
    marginBottom: SPACING.md,
  },
  
  description: {
    fontSize: FONT_SIZES.medium,
    color: COLORS.darkGray,
    lineHeight: 22,
  },
  
  section: {
    marginBottom: SPACING.xl,
    backgroundColor: COLORS.white,
    padding: SPACING.lg,
    borderRadius: 16,
    shadowColor: COLORS.shadow,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  
  sectionTitle: {
    fontSize: FONT_SIZES.xlarge,
    fontWeight: '700',
    color: COLORS.primary,
    marginBottom: SPACING.md,
  },
  
  infoGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: SPACING.md,
  },
  
  infoItem: {
    flex: 1,
    minWidth: '45%',
    backgroundColor: COLORS.white,
    padding: SPACING.md,
    borderRadius: 8,
    shadowColor: COLORS.shadow,
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  
  infoLabel: {
    fontSize: FONT_SIZES.small,
    color: COLORS.darkGray,
    marginBottom: SPACING.xs,
  },
  
  infoValue: {
    fontSize: FONT_SIZES.medium,
    fontWeight: '600',
    color: COLORS.primary,
  },
  
  careGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: SPACING.md,
  },
  
  careItem: {
    flex: 1,
    minWidth: '45%',
    backgroundColor: COLORS.white,
    padding: SPACING.md,
    borderRadius: 12,
    alignItems: 'center',
    shadowColor: COLORS.shadow,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  
  careIconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: COLORS.lightGray,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: SPACING.sm,
  },
  
  careLabel: {
    fontSize: FONT_SIZES.small,
    color: COLORS.darkGray,
    marginBottom: SPACING.xs,
    textAlign: 'center',
  },
  
  careValue: {
    fontSize: FONT_SIZES.medium,
    fontWeight: '600',
    color: COLORS.primary,
    textAlign: 'center',
  },
  
  stageDescription: {
    fontSize: FONT_SIZES.medium,
    color: COLORS.darkGray,
    lineHeight: 20,
    marginBottom: SPACING.sm,
  },
  
  durationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: SPACING.xs,
  },
  
  durationText: {
    fontSize: FONT_SIZES.small,
    color: COLORS.darkGray,
    fontStyle: 'italic',
  },
  
  errorContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: SPACING.xl,
  },
  
  errorTitle: {
    fontSize: FONT_SIZES.xlarge,
    fontWeight: '600',
    color: COLORS.primary,
    marginTop: SPACING.md,
    marginBottom: SPACING.sm,
    textAlign: 'center',
  },
  
  errorText: {
    fontSize: FONT_SIZES.medium,
    color: COLORS.darkGray,
    textAlign: 'center',
    lineHeight: 20,
    marginBottom: SPACING.lg,
  },
  
  retryButton: {
    backgroundColor: COLORS.accent,
    paddingHorizontal: SPACING.lg,
    paddingVertical: SPACING.md,
    borderRadius: 8,
  },
  
  retryButtonText: {
    color: COLORS.white,
    fontSize: FONT_SIZES.medium,
    fontWeight: '600',
  },

  // Tab Styles
  tabContainer: {
    flexDirection: 'row',
    backgroundColor: COLORS.lightGray,
    borderRadius: 16,
    padding: 4,
    marginBottom: SPACING.lg,
  },

  tab: {
    flex: 1,
    paddingVertical: SPACING.md,
    paddingHorizontal: SPACING.sm,
    borderRadius: 12,
    alignItems: 'center',
  },

  activeTab: {
    backgroundColor: COLORS.white,
    shadowColor: COLORS.shadow,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },

  tabText: {
    fontSize: FONT_SIZES.medium,
    fontWeight: '500',
    color: COLORS.darkGray,
    textAlign: 'center',
  },

  activeTabText: {
    color: COLORS.primary,
    fontWeight: '600',
  },
});
