import React from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  Image,
  StyleSheet,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';

// Context
import { useFavorites } from '../contexts/FavoritesContext';

const FavoritesPage = () => {
  const navigation = useNavigation();
  const { favorites, removeFavorite } = useFavorites();

  // Bitki detayına git
  const handlePlantPress = (plant) => {
    navigation.navigate('PlantDetail', { plantId: plant.id, plant });
  };

  // Favoriden çıkar
  const handleRemoveFavorite = (plantId) => {
    removeFavorite(plantId);
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Header */}
        <View style={styles.header}>
          <View style={styles.titleSection}>
            <Text style={styles.title}>Favori <PERSON></Text>
            <Text style={styles.subtitle}>{favorites.length} favori bitki</Text>
          </View>
        </View>

        {/* Favorites List */}
        {favorites.length === 0 ? (
          <View style={styles.emptyContainer}>
            <Ionicons name="heart-outline" size={64} color="#CCCCCC" />
            <Text style={styles.emptyText}>Henüz favori bitki eklenmemiş</Text>
            <Text style={styles.emptySubtext}>
              Beğendiğiniz bitkileri favorilere ekleyerek buradan kolayca erişebilirsiniz
            </Text>
          </View>
        ) : (
          favorites.map((plant) => (
            <TouchableOpacity
              key={plant.id}
              style={styles.plantCard}
              onPress={() => handlePlantPress(plant)}
              activeOpacity={0.8}
            >
              <Image
                source={{
                  uri: plant.mainImage || 'https://images.unsplash.com/photo-1416879595882-3373a0480b5b?w=400&h=400&fit=crop'
                }}
                style={styles.plantImage}
                resizeMode="cover"
              />
              <View style={styles.plantInfo}>
                <Text style={styles.plantName}>{plant.name}</Text>
                <Text style={styles.plantLatin}>{plant.latinName}</Text>
                <Text style={styles.plantCategory}>{plant.category}</Text>
                <Text style={styles.plantDescription} numberOfLines={2}>
                  {plant.description}
                </Text>
                <View style={styles.buttonRow}>
                  <TouchableOpacity
                    style={styles.detailButton}
                    onPress={() => handlePlantPress(plant)}
                  >
                    <Text style={styles.detailButtonText}>Detay</Text>
                    <Ionicons name="chevron-forward" size={16} color="#FFFFFF" />
                  </TouchableOpacity>
                  <TouchableOpacity
                    style={styles.removeButton}
                    onPress={() => handleRemoveFavorite(plant.id)}
                  >
                    <Ionicons name="heart" size={20} color="#FF6B6B" />
                  </TouchableOpacity>
                </View>
              </View>
            </TouchableOpacity>
          ))
        )}
      </ScrollView>
    </SafeAreaView>
  );
};

// Stiller
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  scrollView: {
    flex: 1,
  },
  header: {
    backgroundColor: '#FC7138',
    padding: 20,
    paddingTop: 40,
  },
  titleSection: {
    alignItems: 'center',
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  subtitle: {
    fontSize: 14,
    color: '#FFFFFF',
    opacity: 0.9,
    marginTop: 4,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
    marginTop: 60,
  },
  emptyText: {
    fontSize: 18,
    color: '#CCCCCC',
    marginTop: 16,
    textAlign: 'center',
  },
  emptySubtext: {
    fontSize: 14,
    color: '#CCCCCC',
    marginTop: 8,
    textAlign: 'center',
    lineHeight: 20,
  },
  plantCard: {
    backgroundColor: '#FFFFFF',
    marginHorizontal: 16,
    marginVertical: 8,
    borderRadius: 12,
    flexDirection: 'row',
    overflow: 'hidden',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  plantImage: {
    width: 100,
    height: 120,
  },
  plantInfo: {
    flex: 1,
    padding: 12,
    justifyContent: 'space-between',
  },
  plantName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333333',
  },
  plantLatin: {
    fontSize: 14,
    fontStyle: 'italic',
    color: '#CCCCCC',
    marginTop: 2,
  },
  plantCategory: {
    fontSize: 12,
    color: '#7C4D25',
    fontWeight: '600',
    marginTop: 4,
  },
  plantDescription: {
    fontSize: 14,
    color: '#333333',
    marginTop: 8,
    lineHeight: 20,
  },
  buttonRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 8,
  },
  detailButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#7C4D25',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 6,
  },
  detailButtonText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: '600',
    marginRight: 4,
  },
  removeButton: {
    padding: 8,
  },
});

export default FavoritesPage;
