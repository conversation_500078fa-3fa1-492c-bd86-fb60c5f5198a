import React, { createContext, useContext, useState, useEffect } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';

const FavoritesContext = createContext();

export const useFavorites = () => {
  const context = useContext(FavoritesContext);
  if (!context) {
    throw new Error('useFavorites must be used within a FavoritesProvider');
  }
  return context;
};

export const FavoritesProvider = ({ children }) => {
  const [favorites, setFavorites] = useState([]);
  const [loading, setLoading] = useState(true);

  // AsyncStorage'dan favorileri yükle
  useEffect(() => {
    loadFavorites();
  }, []);

  const loadFavorites = async () => {
    try {
      const storedFavorites = await AsyncStorage.getItem('favorites');
      if (storedFavorites) {
        setFavorites(JSON.parse(storedFavorites));
      }
    } catch (error) {
      console.error('Favoriler yüklenirken hata:', error);
    } finally {
      setLoading(false);
    }
  };

  const saveFavorites = async (newFavorites) => {
    try {
      await AsyncStorage.setItem('favorites', JSON.stringify(newFavorites));
    } catch (error) {
      console.error('Favoriler kaydedilirken hata:', error);
    }
  };

  const addFavorite = async (plant) => {
    const isAlreadyFavorite = favorites.some(fav => fav.id === plant.id);
    if (!isAlreadyFavorite) {
      const newFavorites = [...favorites, plant];
      setFavorites(newFavorites);
      await saveFavorites(newFavorites);
    }
  };

  const removeFavorite = async (plantId) => {
    const newFavorites = favorites.filter(plant => plant.id !== plantId);
    setFavorites(newFavorites);
    await saveFavorites(newFavorites);
  };

  const toggleFavorite = async (plant) => {
    const isAlreadyFavorite = favorites.some(fav => fav.id === plant.id);
    if (isAlreadyFavorite) {
      await removeFavorite(plant.id);
    } else {
      await addFavorite(plant);
    }
  };

  const isFavorite = (plantId) => {
    return favorites.some(plant => plant.id === plantId);
  };

  const clearAllFavorites = async () => {
    setFavorites([]);
    await saveFavorites([]);
  };

  const value = {
    favorites,
    loading,
    addFavorite,
    removeFavorite,
    toggleFavorite,
    isFavorite,
    clearAllFavorites
  };

  return (
    <FavoritesContext.Provider value={value}>
      {children}
    </FavoritesContext.Provider>
  );
};
