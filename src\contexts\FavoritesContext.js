import React, { createContext, useContext, useState, useEffect } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';

const FavoritesContext = createContext();

export const useFavorites = () => {
  const context = useContext(FavoritesContext);
  if (!context) {
    throw new Error('useFavorites must be used within a FavoritesProvider');
  }
  return context;
};

export const FavoritesProvider = ({ children }) => {
  const [favorites, setFavorites] = useState([]);
  const [loading, setLoading] = useState(true);

  // AsyncStorage'dan favorileri yükle
  useEffect(() => {
    loadFavorites();
  }, []);

  const loadFavorites = async () => {
    try {
      const storedFavorites = await AsyncStorage.getItem('favorites');
      if (storedFavorites) {
        setFavorites(JSON.parse(storedFavorites));
      }
    } catch (error) {
      console.error('Favoriler yüklenirken hata:', error);
    } finally {
      setLoading(false);
    }
  };

  const saveFavorites = async (newFavorites) => {
    try {
      await AsyncStorage.setItem('favorites', JSON.stringify(newFavorites));
    } catch (error) {
      console.error('Favoriler kaydedilirken hata:', error);
    }
  };

  const addToFavorites = async (plantId) => {
    const newFavorites = [...favorites, plantId];
    setFavorites(newFavorites);
    await saveFavorites(newFavorites);
  };

  const removeFromFavorites = async (plantId) => {
    const newFavorites = favorites.filter(id => id !== plantId);
    setFavorites(newFavorites);
    await saveFavorites(newFavorites);
  };

  const toggleFavorite = async (plantId) => {
    if (favorites.includes(plantId)) {
      await removeFromFavorites(plantId);
    } else {
      await addToFavorites(plantId);
    }
  };

  const isFavorite = (plantId) => {
    return favorites.includes(plantId);
  };

  const clearAllFavorites = async () => {
    setFavorites([]);
    await saveFavorites([]);
  };

  const value = {
    favorites,
    loading,
    addToFavorites,
    removeFromFavorites,
    toggleFavorite,
    isFavorite,
    clearAllFavorites
  };

  return (
    <FavoritesContext.Provider value={value}>
      {children}
    </FavoritesContext.Provider>
  );
};
