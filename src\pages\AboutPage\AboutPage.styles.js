import { StyleSheet } from 'react-native';
import { COLORS, FONT_SIZES, SPACING } from '../../utils/constants';

export default StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FC7138', // Turuncu status bar için
  },

  scrollContent: {
    backgroundColor: COLORS.background,
    paddingBottom: SPACING.tabBar,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    marginTop: -20, // Header ile overlap
  },

  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: SPACING.lg,
    paddingVertical: SPACING.lg,
    paddingTop: SPACING.xxl,
    backgroundColor: '#FC7138', // Turuncu arka plan
    marginBottom: SPACING.lg,
    borderBottomLeftRadius: 20,
    borderBottomRightRadius: 20,
    shadowColor: COLORS.shadow,
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 6,
  },

  logoSection: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },

  logo: {
    width: 40,
    height: 40,
    marginRight: SPACING.md,
  },

  title: {
    fontSize: FONT_SIZES.header,
    fontWeight: '800',
    color: COLORS.white,
    letterSpacing: 0.5,
  },

  subtitle: {
    fontSize: FONT_SIZES.medium,
    color: COLORS.white,
    textAlign: 'center',
    fontWeight: '500',
    opacity: 0.9,
    letterSpacing: 0.3,
    marginTop: 2,
  },
  
  searchContainer: {
    paddingHorizontal: SPACING.lg,
    marginBottom: SPACING.md,
  },

  searchInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.white,
    borderRadius: 12,
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.sm,
    shadowColor: COLORS.shadow,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.08,
    shadowRadius: 4,
    elevation: 3,
    gap: SPACING.xs,
    borderWidth: 1,
    borderColor: COLORS.lightGray,
  },

  searchInput: {
    flex: 1,
    fontSize: FONT_SIZES.small,
    color: COLORS.primary,
  },
  
  categoriesContainer: {
    marginBottom: SPACING.sm,
  },

  categoriesList: {
    paddingHorizontal: SPACING.md,
  },

  categoryButton: {
    backgroundColor: COLORS.white,
    paddingHorizontal: SPACING.sm,
    paddingVertical: SPACING.xs,
    borderRadius: 16,
    marginRight: SPACING.xs,
    borderWidth: 1,
    borderColor: COLORS.gray,
  },

  activeCategoryButton: {
    backgroundColor: COLORS.secondary,
    borderColor: COLORS.secondary,
  },

  categoryButtonText: {
    fontSize: FONT_SIZES.small - 1,
    color: COLORS.darkGray,
    fontWeight: '500',
  },
  
  activeCategoryButtonText: {
    color: COLORS.white,
    fontWeight: '600',
  },
  
  resultsInfo: {
    paddingHorizontal: SPACING.md,
    marginBottom: SPACING.sm,
  },
  
  resultsText: {
    fontSize: FONT_SIZES.small,
    color: COLORS.darkGray,
    fontStyle: 'italic',
  },
  
  listContent: {
    paddingHorizontal: SPACING.lg,
    paddingBottom: SPACING.tabBar, // Extra space for floating tab bar
    flexGrow: 1,
  },
  
  articleCard: {
    backgroundColor: COLORS.white,
    borderRadius: 16,
    padding: SPACING.lg,
    marginBottom: SPACING.lg,
    shadowColor: COLORS.shadow,
    shadowOffset: {
      width: 0,
      height: 3,
    },
    shadowOpacity: 0.12,
    shadowRadius: 6,
    elevation: 4,
    borderWidth: 1,
    borderColor: 'rgba(124, 77, 37, 0.08)',
  },
  
  articleHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: SPACING.sm,
  },
  
  articleTitle: {
    flex: 1,
    fontSize: FONT_SIZES.large,
    fontWeight: '600',
    color: COLORS.primary,
    marginRight: SPACING.sm,
  },
  
  categoryTag: {
    backgroundColor: COLORS.accent,
    paddingHorizontal: SPACING.sm,
    paddingVertical: 4,
    borderRadius: 12,
  },
  
  categoryTagText: {
    fontSize: FONT_SIZES.small - 1,
    color: COLORS.white,
    fontWeight: '500',
  },
  
  articleSummary: {
    fontSize: FONT_SIZES.medium,
    color: COLORS.darkGray,
    lineHeight: 20,
    marginBottom: SPACING.sm,
  },
  
  articleFooter: {
    alignItems: 'flex-end',
  },
  
  emptyState: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: SPACING.xxl,
  },
  
  emptyStateTitle: {
    fontSize: FONT_SIZES.xlarge,
    fontWeight: '600',
    color: COLORS.primary,
    marginTop: SPACING.md,
    marginBottom: SPACING.sm,
    textAlign: 'center',
  },
  
  emptyStateText: {
    fontSize: FONT_SIZES.medium,
    color: COLORS.darkGray,
    textAlign: 'center',
    paddingHorizontal: SPACING.xl,
    lineHeight: 20,
  },
});
