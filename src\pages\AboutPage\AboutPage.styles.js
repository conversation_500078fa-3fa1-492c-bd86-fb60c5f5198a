import { StyleSheet } from 'react-native';
import { COLORS, FONT_SIZES, SPACING } from '../../utils/constants';

export default StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
    paddingTop: SPACING.md,
  },

  header: {
    paddingHorizontal: SPACING.lg,
    paddingVertical: SPACING.xl,
    alignItems: 'center',
    backgroundColor: COLORS.white,
    marginHorizontal: SPACING.md,
    marginBottom: SPACING.lg,
    borderRadius: 20,
    shadowColor: COLORS.shadow,
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 6,
  },
  
  title: {
    fontSize: FONT_SIZES.header + 2,
    fontWeight: '800',
    color: COLORS.primary,
    marginBottom: SPACING.xs,
    letterSpacing: 0.5,
  },

  subtitle: {
    fontSize: FONT_SIZES.large,
    color: COLORS.secondary,
    textAlign: 'center',
    fontWeight: '500',
    letterSpacing: 0.3,
  },
  
  searchContainer: {
    paddingHorizontal: SPACING.lg,
    marginBottom: SPACING.lg,
  },

  searchInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.white,
    borderRadius: 16,
    paddingHorizontal: SPACING.lg,
    paddingVertical: SPACING.md,
    shadowColor: COLORS.shadow,
    shadowOffset: {
      width: 0,
      height: 3,
    },
    shadowOpacity: 0.12,
    shadowRadius: 6,
    elevation: 4,
    gap: SPACING.sm,
    borderWidth: 1,
    borderColor: COLORS.lightGray,
  },
  
  searchInput: {
    flex: 1,
    fontSize: FONT_SIZES.medium,
    color: COLORS.primary,
  },
  
  categoriesContainer: {
    marginBottom: SPACING.md,
  },
  
  categoriesList: {
    paddingHorizontal: SPACING.md,
  },
  
  categoryButton: {
    backgroundColor: COLORS.white,
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.sm,
    borderRadius: 20,
    marginRight: SPACING.sm,
    borderWidth: 1,
    borderColor: COLORS.gray,
  },
  
  activeCategoryButton: {
    backgroundColor: COLORS.secondary,
    borderColor: COLORS.secondary,
  },
  
  categoryButtonText: {
    fontSize: FONT_SIZES.small,
    color: COLORS.darkGray,
    fontWeight: '500',
  },
  
  activeCategoryButtonText: {
    color: COLORS.white,
    fontWeight: '600',
  },
  
  resultsInfo: {
    paddingHorizontal: SPACING.md,
    marginBottom: SPACING.sm,
  },
  
  resultsText: {
    fontSize: FONT_SIZES.small,
    color: COLORS.darkGray,
    fontStyle: 'italic',
  },
  
  listContent: {
    paddingHorizontal: SPACING.lg,
    paddingBottom: SPACING.tabBar, // Extra space for floating tab bar
    flexGrow: 1,
  },
  
  articleCard: {
    backgroundColor: COLORS.white,
    borderRadius: 16,
    padding: SPACING.lg,
    marginBottom: SPACING.lg,
    shadowColor: COLORS.shadow,
    shadowOffset: {
      width: 0,
      height: 3,
    },
    shadowOpacity: 0.12,
    shadowRadius: 6,
    elevation: 4,
    borderWidth: 1,
    borderColor: 'rgba(124, 77, 37, 0.08)',
  },
  
  articleHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: SPACING.sm,
  },
  
  articleTitle: {
    flex: 1,
    fontSize: FONT_SIZES.large,
    fontWeight: '600',
    color: COLORS.primary,
    marginRight: SPACING.sm,
  },
  
  categoryTag: {
    backgroundColor: COLORS.accent,
    paddingHorizontal: SPACING.sm,
    paddingVertical: 4,
    borderRadius: 12,
  },
  
  categoryTagText: {
    fontSize: FONT_SIZES.small - 1,
    color: COLORS.white,
    fontWeight: '500',
  },
  
  articleSummary: {
    fontSize: FONT_SIZES.medium,
    color: COLORS.darkGray,
    lineHeight: 20,
    marginBottom: SPACING.sm,
  },
  
  articleFooter: {
    alignItems: 'flex-end',
  },
  
  emptyState: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: SPACING.xxl,
  },
  
  emptyStateTitle: {
    fontSize: FONT_SIZES.xlarge,
    fontWeight: '600',
    color: COLORS.primary,
    marginTop: SPACING.md,
    marginBottom: SPACING.sm,
    textAlign: 'center',
  },
  
  emptyStateText: {
    fontSize: FONT_SIZES.medium,
    color: COLORS.darkGray,
    textAlign: 'center',
    paddingHorizontal: SPACING.xl,
    lineHeight: 20,
  },
});
