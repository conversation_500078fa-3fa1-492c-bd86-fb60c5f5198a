import { StyleSheet } from 'react-native';
import { COLORS, FONT_SIZES, SPACING } from '../../utils/constants';

export default StyleSheet.create({
  container: {
    paddingVertical: SPACING.lg,
    backgroundColor: COLORS.white,
    marginHorizontal: SPACING.md,
    borderRadius: 16,
    shadowColor: COLORS.shadow,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    marginBottom: SPACING.md,
  },
  
  scrollContent: {
    paddingHorizontal: SPACING.md,
    alignItems: 'center',
  },
  
  stageContainer: {
    alignItems: 'center',
    marginRight: SPACING.lg,
    position: 'relative',
  },
  
  stageButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: COLORS.lightGray,
    borderWidth: 2,
    borderColor: COLORS.gray,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: SPACING.xs,
  },
  
  activeStageButton: {
    backgroundColor: COLORS.accent,
    borderColor: COLORS.accent,
    shadowColor: COLORS.accent,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 5,
  },
  
  passedStageButton: {
    backgroundColor: COLORS.secondary,
    borderColor: COLORS.secondary,
  },
  
  stageNumber: {
    fontSize: FONT_SIZES.medium,
    fontWeight: '600',
    color: COLORS.darkGray,
  },
  
  activeStageNumber: {
    color: COLORS.white,
    fontWeight: '700',
  },
  
  passedStageNumber: {
    color: COLORS.white,
  },
  
  stageName: {
    fontSize: FONT_SIZES.small,
    color: COLORS.darkGray,
    textAlign: 'center',
    maxWidth: 60,
  },
  
  activeStageName: {
    color: COLORS.primary,
    fontWeight: '600',
  },
  
  connector: {
    position: 'absolute',
    top: 20,
    left: 40,
    width: SPACING.lg,
    height: 2,
    backgroundColor: COLORS.gray,
    zIndex: -1,
  },
  
  passedConnector: {
    backgroundColor: COLORS.secondary,
  },
});
