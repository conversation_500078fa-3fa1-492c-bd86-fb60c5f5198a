import React, { useState } from 'react';
import {
  View,
  Text,
  Modal,
  TouchableOpacity,
  ScrollView,
  Switch,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { COLORS } from '../../utils/constants';
import styles from './FilterModal.styles';

const FilterModal = ({ 
  visible, 
  onClose, 
  onApplyFilters,
  currentFilters = {}
}) => {
  const [filters, setFilters] = useState({
    category: currentFilters.category || 'Tümü',
    careLevel: currentFilters.careLevel || 'Tümü',
    lightRequirement: currentFilters.lightRequirement || 'Tümü',
    wateringFrequency: currentFilters.wateringFrequency || 'Tümü',
    isNew: currentFilters.isNew || false,
    isPopular: currentFilters.isPopular || false,
    size: currentFilters.size || 'Tümü',
  });

  const categories = ['Tümü', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>romati<PERSON>', 'Sukulent'];
  const careLevels = ['Tümü', '<PERSON><PERSON>', 'Orta', 'Zor'];
  const lightRequirements = ['Tümü', 'Doğrudan güneş', 'Parlak dolaylı', 'Yarı gölge', 'Gölge'];
  const wateringFrequencies = ['Tümü', 'Günlük', 'Haftada 2-3', 'Haftada 1', 'Az'];
  const sizes = ['Tümü', 'Küçük (0-30cm)', 'Orta (30-100cm)', 'Büyük (100cm+)'];

  const handleFilterChange = (key, value) => {
    setFilters(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const handleApply = () => {
    onApplyFilters(filters);
    onClose();
  };

  const handleReset = () => {
    const resetFilters = {
      category: 'Tümü',
      careLevel: 'Tümü',
      lightRequirement: 'Tümü',
      wateringFrequency: 'Tümü',
      isNew: false,
      isPopular: false,
      size: 'Tümü',
    };
    setFilters(resetFilters);
  };

  const renderFilterSection = (title, options, selectedValue, filterKey) => (
    <View style={styles.filterSection}>
      <Text style={styles.sectionTitle}>{title}</Text>
      <View style={styles.optionsContainer}>
        {options.map((option) => (
          <TouchableOpacity
            key={option}
            style={[
              styles.optionButton,
              selectedValue === option && styles.selectedOption
            ]}
            onPress={() => handleFilterChange(filterKey, option)}
          >
            <Text style={[
              styles.optionText,
              selectedValue === option && styles.selectedOptionText
            ]}>
              {option}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );

  return (
    <Modal
      visible={visible}
      animationType="fade"
      transparent={true}
      onRequestClose={onClose}
    >
      <View style={styles.overlay}>
        <View style={styles.container}>
          {/* Header */}
          <View style={styles.header}>
            <Text style={styles.headerTitle}>Filtrele</Text>
            <TouchableOpacity onPress={onClose} style={styles.closeButton}>
              <Ionicons name="close" size={20} color={COLORS.darkGray} />
            </TouchableOpacity>
          </View>

        {/* Content */}
        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          {/* Category Filter */}
          {renderFilterSection('Kategori', categories, filters.category, 'category')}

          {/* Care Level Filter */}
          {renderFilterSection('Bakım Seviyesi', careLevels, filters.careLevel, 'careLevel')}

          {/* Light Requirement Filter */}
          {renderFilterSection('Işık İhtiyacı', lightRequirements, filters.lightRequirement, 'lightRequirement')}

          {/* Watering Frequency Filter */}
          {renderFilterSection('Sulama Sıklığı', wateringFrequencies, filters.wateringFrequency, 'wateringFrequency')}

          {/* Size Filter */}
          {renderFilterSection('Boyut', sizes, filters.size, 'size')}

          {/* Special Filters */}
          <View style={styles.filterSection}>
            <Text style={styles.sectionTitle}>Özel Filtreler</Text>
            
            <View style={styles.switchContainer}>
              <Text style={styles.switchLabel}>Sadece Yeni Bitkiler</Text>
              <Switch
                value={filters.isNew}
                onValueChange={(value) => handleFilterChange('isNew', value)}
                trackColor={{ false: COLORS.lightGray, true: COLORS.secondary }}
                thumbColor={filters.isNew ? COLORS.white : COLORS.gray}
              />
            </View>

            <View style={styles.switchContainer}>
              <Text style={styles.switchLabel}>Sadece Popüler Bitkiler</Text>
              <Switch
                value={filters.isPopular}
                onValueChange={(value) => handleFilterChange('isPopular', value)}
                trackColor={{ false: COLORS.lightGray, true: COLORS.secondary }}
                thumbColor={filters.isPopular ? COLORS.white : COLORS.gray}
              />
            </View>
          </View>
        </ScrollView>

          {/* Footer */}
          <View style={styles.footer}>
            <TouchableOpacity style={styles.resetButton} onPress={handleReset}>
              <Text style={styles.resetButtonText}>Sıfırla</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.applyButton} onPress={handleApply}>
              <Text style={styles.applyButtonText}>Uygula</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
};

export default FilterModal;
