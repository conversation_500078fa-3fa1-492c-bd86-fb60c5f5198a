import React, { useState } from 'react';
import { View, Text, Image, TouchableOpacity, Animated } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { COLORS } from '../../utils/constants';
import { useFavoriteToggle } from '../../hooks/useFavorites';
import styles from './PlantCard.styles.js';

const PlantCard = ({
  plant,
  onPress,
  style,
  showFavoriteButton = true,
  ...props
}) => {
  const { toggleFavorite, isFavorite, loading } = useFavoriteToggle();
  const [scaleValue] = useState(new Animated.Value(1));
  const [isPressed, setIsPressed] = useState(false);

  const handleFavoritePress = (e) => {
    e.stopPropagation();
    toggleFavorite(plant.id);
  };

  const handleCardPress = () => {
    if (onPress) {
      onPress(plant);
    }
  };

  const handlePressIn = () => {
    setIsPressed(true);
    Animated.spring(scaleValue, {
      toValue: 0.95,
      useNativeDriver: true,
      tension: 300,
      friction: 10,
    }).start();
  };

  const handlePressOut = () => {
    setIsPressed(false);
    Animated.spring(scaleValue, {
      toValue: 1,
      useNativeDriver: true,
      tension: 300,
      friction: 10,
    }).start();
  };

  return (
    <Animated.View
      style={[
        styles.card,
        style,
        {
          transform: [{ scale: scaleValue }],
        },
        isPressed && styles.cardPressed,
      ]}
    >
      <View style={styles.gradientBackground}>
        <TouchableOpacity
          style={styles.cardTouchable}
          onPress={handleCardPress}
          onPressIn={handlePressIn}
          onPressOut={handlePressOut}
          activeOpacity={1}
          {...props}
        >
          {/* Sol Taraf - Görsel */}
          <View style={styles.imageContainer}>
            <Image
              source={{
                uri: plant.mainImage || 'https://images.unsplash.com/photo-1416879595882-3373a0480b5b?w=400&h=400&fit=crop&crop=center'
              }}
              style={styles.image}
              resizeMode="cover"
            />
          </View>

          {/* Sağ Taraf - İçerik */}
          <View style={styles.textContainer}>
            <View style={styles.contentArea}>
              <View style={styles.topSection}>
                <Text style={styles.category}>{plant.category || 'Bitki'}</Text>
                {(plant.isNew || plant.isPopular) && (
                  <View style={styles.statusBadge}>
                    <Text style={styles.statusText}>
                      {plant.isNew ? 'YENİ' : 'POPÜLER'}
                    </Text>
                  </View>
                )}
              </View>

              <Text style={styles.title}>{plant.name}</Text>
              <Text style={styles.latinName}>{plant.latinName}</Text>
              <Text style={styles.description} numberOfLines={2}>
                {plant.description}
              </Text>

              <View style={styles.careSection}>
                <View style={styles.careBubble}>
                  <Ionicons name="water-outline" size={12} color={COLORS.white} />
                  <Text style={styles.careText}>{plant.care?.watering || 'Orta'}</Text>
                </View>
                <View style={styles.careBubble}>
                  <Ionicons name="sunny-outline" size={12} color={COLORS.white} />
                  <Text style={styles.careText}>{plant.care?.light || 'Güneşli'}</Text>
                </View>
              </View>
            </View>
          </View>

          {/* Detay Oku Butonu - Card'ın sağ altında */}
          <TouchableOpacity
            style={styles.detailButton}
            onPress={handleCardPress}
            activeOpacity={0.8}
          >
            <Text style={styles.detailButtonText}>Detay Oku</Text>
            <Ionicons name="arrow-forward" size={14} color={COLORS.white} />
          </TouchableOpacity>
        </TouchableOpacity>
      </View>
    </Animated.View>
  );
};

export default PlantCard;
