import React from 'react';
import { TouchableOpacity, Text, ActivityIndicator, View } from 'react-native';
import { COLORS, FONT_SIZES, SPACING } from '../../../utils/constants';
import styles from './Button.styles.js';

const Button = ({
  title,
  onPress,
  variant = 'primary', // primary, secondary, outline, text
  size = 'medium', // small, medium, large
  disabled = false,
  loading = false,
  icon = null,
  iconPosition = 'left', // left, right
  style,
  textStyle,
  ...props
}) => {
  const getButtonStyle = () => {
    let buttonStyle = [styles.button];

    // Variant styles
    switch (variant) {
      case 'primary':
        buttonStyle.push(styles.primaryButton);
        break;
      case 'secondary':
        buttonStyle.push(styles.secondaryButton);
        break;
      case 'outline':
        buttonStyle.push(styles.outlineButton);
        break;
      case 'text':
        buttonStyle.push(styles.textButton);
        break;
      default:
        buttonStyle.push(styles.primaryButton);
    }

    // Size styles
    switch (size) {
      case 'small':
        buttonStyle.push(styles.smallButton);
        break;
      case 'medium':
        buttonStyle.push(styles.mediumButton);
        break;
      case 'large':
        buttonStyle.push(styles.largeButton);
        break;
      default:
        buttonStyle.push(styles.mediumButton);
    }

    // Disabled style
    if (disabled) {
      buttonStyle.push(styles.disabledButton);
    }

    // Custom style
    if (style) {
      buttonStyle.push(style);
    }

    return buttonStyle;
  };

  const getTextStyle = () => {
    let textStyleArray = [styles.buttonText];

    // Variant text styles
    switch (variant) {
      case 'primary':
        textStyleArray.push(styles.primaryButtonText);
        break;
      case 'secondary':
        textStyleArray.push(styles.secondaryButtonText);
        break;
      case 'outline':
        textStyleArray.push(styles.outlineButtonText);
        break;
      case 'text':
        textStyleArray.push(styles.textButtonText);
        break;
      default:
        textStyleArray.push(styles.primaryButtonText);
    }

    // Size text styles
    switch (size) {
      case 'small':
        textStyleArray.push(styles.smallButtonText);
        break;
      case 'medium':
        textStyleArray.push(styles.mediumButtonText);
        break;
      case 'large':
        textStyleArray.push(styles.largeButtonText);
        break;
      default:
        textStyleArray.push(styles.mediumButtonText);
    }

    // Disabled text style
    if (disabled) {
      textStyleArray.push(styles.disabledButtonText);
    }

    // Custom text style
    if (textStyle) {
      textStyleArray.push(textStyle);
    }

    return textStyleArray;
  };

  const renderContent = () => {
    if (loading) {
      return (
        <ActivityIndicator 
          size="small" 
          color={variant === 'primary' ? COLORS.white : COLORS.primary} 
        />
      );
    }

    const textElement = <Text style={getTextStyle()}>{title}</Text>;
    
    if (!icon) {
      return textElement;
    }

    return (
      <View style={styles.buttonContent}>
        {iconPosition === 'left' && icon}
        {textElement}
        {iconPosition === 'right' && icon}
      </View>
    );
  };

  return (
    <TouchableOpacity
      style={getButtonStyle()}
      onPress={onPress}
      disabled={disabled || loading}
      activeOpacity={0.7}
      {...props}
    >
      {renderContent()}
    </TouchableOpacity>
  );
};

export default Button;
