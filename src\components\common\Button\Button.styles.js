import { StyleSheet } from 'react-native';
import { COLORS, FONT_SIZES, SPACING } from '../../../utils/constants';

export default StyleSheet.create({
  button: {
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row',
  },
  
  // Variant styles
  primaryButton: {
    backgroundColor: COLORS.accent,
    borderWidth: 0,
  },
  
  secondaryButton: {
    backgroundColor: COLORS.secondary,
    borderWidth: 0,
  },
  
  outlineButton: {
    backgroundColor: 'transparent',
    borderWidth: 2,
    borderColor: COLORS.primary,
  },
  
  textButton: {
    backgroundColor: 'transparent',
    borderWidth: 0,
  },
  
  // Size styles
  smallButton: {
    paddingHorizontal: SPACING.sm,
    paddingVertical: SPACING.xs,
    minHeight: 32,
  },
  
  mediumButton: {
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.sm,
    minHeight: 44,
  },
  
  largeButton: {
    paddingHorizontal: SPACING.lg,
    paddingVertical: SPACING.md,
    minHeight: 52,
  },
  
  // Disabled style
  disabledButton: {
    backgroundColor: COLORS.gray,
    borderColor: COLORS.gray,
  },
  
  // Text styles
  buttonText: {
    fontWeight: '600',
    textAlign: 'center',
  },
  
  // Variant text styles
  primaryButtonText: {
    color: COLORS.white,
  },
  
  secondaryButtonText: {
    color: COLORS.primary,
  },
  
  outlineButtonText: {
    color: COLORS.primary,
  },
  
  textButtonText: {
    color: COLORS.primary,
  },
  
  // Size text styles
  smallButtonText: {
    fontSize: FONT_SIZES.small,
  },
  
  mediumButtonText: {
    fontSize: FONT_SIZES.medium,
  },
  
  largeButtonText: {
    fontSize: FONT_SIZES.large,
  },
  
  // Disabled text style
  disabledButtonText: {
    color: COLORS.darkGray,
  },
  
  // Button content
  buttonContent: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: SPACING.xs,
  },
});
