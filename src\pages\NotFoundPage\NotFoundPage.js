import React from 'react';
import { View, Text, TouchableOpacity, SafeAreaView } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { COLORS } from '../../utils/constants';
import styles from './NotFoundPage.styles.js';

const NotFoundPage = ({ navigation }) => {
  const handleGoHome = () => {
    navigation.navigate('Main', { screen: 'Home' });
  };

  const handleGoBack = () => {
    navigation.goBack();
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.content}>
        <Ionicons name="alert-circle-outline" size={100} color={COLORS.gray} />
        
        <Text style={styles.title}>Sayfa Bulunamadı</Text>
        
        <Text style={styles.description}>
          Aradığınız sayfa bulunamadı veya kaldırılmış olabilir.
        </Text>
        
        <View style={styles.buttonContainer}>
          <TouchableOpacity style={styles.primaryButton} onPress={handleGoHome}>
            <Ionicons name="home" size={20} color={COLORS.white} />
            <Text style={styles.primaryButtonText}>Ana Sayfaya Dön</Text>
          </TouchableOpacity>
          
          <TouchableOpacity style={styles.secondaryButton} onPress={handleGoBack}>
            <Ionicons name="arrow-back" size={20} color={COLORS.primary} />
            <Text style={styles.secondaryButtonText}>Geri Dön</Text>
          </TouchableOpacity>
        </View>
      </View>
    </SafeAreaView>
  );
};

export default NotFoundPage;
