// Renk paleti
export const COLORS = {
  primary: '#7C4D25',      // <PERSON><PERSON> kahverengi - ana metin ve başlıklar
  secondary: '#C9E265',    // A<PERSON>ık yeşil - vurgular ve butonlar  
  accent: '#FC7138',       // <PERSON>runcu - aktif durumlar ve önemli butonlar
  white: '#FFFFFF',
  lightGray: '#F5F5F5',
  gray: '#CCCCCC',
  darkGray: '#666666',
  black: '#000000',
  background: '#FAFAFA',
  cardBackground: '#FFFFFF',
  shadow: 'rgba(0, 0, 0, 0.1)'
};

// Font boyutları
export const FONT_SIZES = {
  small: 12,
  medium: 14,
  large: 16,
  xlarge: 18,
  xxlarge: 20,
  title: 24,
  header: 28
};

// Spacing değerleri
export const SPACING = {
  xs: 4,
  sm: 8,
  md: 16,
  lg: 24,
  xl: 32,
  xxl: 48,
  tabBar: 120 // Extra space for floating tab bar
};

// Bitki gelişim aşamaları
export const PLANT_STAGES = [
  { id: 1, name: 'To<PERSON>', description: 'Tohumun toprakta beklemesi' },
  { id: 2, name: '<PERSON>imlenme', description: 'İlk filizin çıkması' },
  { id: 3, name: 'Fide', description: 'Küçük bitki halinde büyüme' },
  { id: 4, name: 'Yapraklanma', description: 'İlk gerçek yaprakların oluşması' },
  { id: 5, name: 'Büyüme', description: 'Hızlı büyüme dönemi' },
  { id: 6, name: 'Dallanma', description: 'Yan dalların oluşması' },
  { id: 7, name: 'Tomurcuklanma', description: 'Çiçek tomurcuklarının oluşması' },
  { id: 8, name: 'Çiçeklenme', description: 'Çiçeklerin açması' },
  { id: 9, name: 'Meyve/Tohum', description: 'Meyve veya tohum oluşumu' },
  { id: 10, name: 'Olgunluk', description: 'Tam olgun bitki hali' }
];

// Örnek bitki verileri - Firebase'den yüklenecek
export const SAMPLE_PLANTS = [];


// Bilgi bankası makaleleri - Firebase'den yüklenecek
export const KNOWLEDGE_BASE_ARTICLES = [];
