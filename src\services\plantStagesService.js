import { 
  collection, 
  doc, 
  getDocs, 
  getDoc, 
  addDoc, 
  updateDoc, 
  deleteDoc, 
  query, 
  where, 
  orderBy, 
  limit,
  serverTimestamp 
} from 'firebase/firestore';
import { db } from '../config/firebase';
import { COLLECTIONS } from '../database/schema';

class PlantStagesService {
  constructor() {
    this.collectionRef = collection(db, COLLECTIONS.PLANT_STAGES);
  }

  // Get all stages for a specific plant
  async getPlantStages(plantId) {
    try {
      const q = query(
        this.collectionRef,
        where('plantId', '==', plantId),
        orderBy('stageNumber', 'asc')
      );

      const querySnapshot = await getDocs(q);
      const stages = [];
      
      querySnapshot.forEach((doc) => {
        stages.push({
          id: doc.id,
          ...doc.data()
        });
      });

      return stages;
    } catch (error) {
      console.error('Error getting plant stages:', error);
      throw error;
    }
  }

  // Get a specific stage by ID
  async getStageById(stageId) {
    try {
      const docRef = doc(db, COLLECTIONS.PLANT_STAGES, stageId);
      const docSnap = await getDoc(docRef);

      if (docSnap.exists()) {
        return {
          id: docSnap.id,
          ...docSnap.data()
        };
      } else {
        throw new Error('Stage not found');
      }
    } catch (error) {
      console.error('Error getting stage:', error);
      throw error;
    }
  }

  // Get stage by plant ID and stage number
  async getStageByNumber(plantId, stageNumber) {
    try {
      const q = query(
        this.collectionRef,
        where('plantId', '==', plantId),
        where('stageNumber', '==', stageNumber),
        limit(1)
      );

      const querySnapshot = await getDocs(q);
      
      if (!querySnapshot.empty) {
        const doc = querySnapshot.docs[0];
        return {
          id: doc.id,
          ...doc.data()
        };
      } else {
        return null;
      }
    } catch (error) {
      console.error('Error getting stage by number:', error);
      throw error;
    }
  }

  // Add a new stage (admin only)
  async addStage(stageData) {
    try {
      const docRef = await addDoc(this.collectionRef, {
        ...stageData,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp()
      });

      return docRef.id;
    } catch (error) {
      console.error('Error adding stage:', error);
      throw error;
    }
  }

  // Update a stage (admin only)
  async updateStage(stageId, stageData) {
    try {
      const docRef = doc(db, COLLECTIONS.PLANT_STAGES, stageId);
      await updateDoc(docRef, {
        ...stageData,
        updatedAt: serverTimestamp()
      });

      return true;
    } catch (error) {
      console.error('Error updating stage:', error);
      throw error;
    }
  }

  // Delete a stage (admin only)
  async deleteStage(stageId) {
    try {
      const docRef = doc(db, COLLECTIONS.PLANT_STAGES, stageId);
      await deleteDoc(docRef);
      return true;
    } catch (error) {
      console.error('Error deleting stage:', error);
      throw error;
    }
  }

  // Add multiple stages for a plant
  async addPlantStages(plantId, stagesData) {
    try {
      const stageIds = [];
      
      for (let i = 0; i < stagesData.length; i++) {
        const stageData = {
          ...stagesData[i],
          plantId: plantId,
          stageNumber: i + 1,
          createdAt: serverTimestamp(),
          updatedAt: serverTimestamp()
        };

        const docRef = await addDoc(this.collectionRef, stageData);
        stageIds.push(docRef.id);
      }

      return stageIds;
    } catch (error) {
      console.error('Error adding plant stages:', error);
      throw error;
    }
  }

  // Get next stage for a plant
  async getNextStage(plantId, currentStageNumber) {
    try {
      const nextStageNumber = currentStageNumber + 1;
      return await this.getStageByNumber(plantId, nextStageNumber);
    } catch (error) {
      console.error('Error getting next stage:', error);
      throw error;
    }
  }

  // Get previous stage for a plant
  async getPreviousStage(plantId, currentStageNumber) {
    try {
      if (currentStageNumber <= 1) return null;
      
      const previousStageNumber = currentStageNumber - 1;
      return await this.getStageByNumber(plantId, previousStageNumber);
    } catch (error) {
      console.error('Error getting previous stage:', error);
      throw error;
    }
  }

  // Get stage statistics
  async getStageStats(plantId) {
    try {
      const stages = await this.getPlantStages(plantId);
      
      const stats = {
        totalStages: stages.length,
        averageDuration: 0,
        totalDuration: 0,
        stageBreakdown: {}
      };

      let totalDays = 0;
      stages.forEach(stage => {
        const avgDuration = stage.duration?.average || 0;
        totalDays += avgDuration;
        
        stats.stageBreakdown[stage.name] = {
          duration: stage.duration,
          tips: stage.tips?.length || 0,
          problems: stage.commonProblems?.length || 0
        };
      });

      stats.averageDuration = stages.length > 0 ? totalDays / stages.length : 0;
      stats.totalDuration = totalDays;

      return stats;
    } catch (error) {
      console.error('Error getting stage stats:', error);
      throw error;
    }
  }

  // Search stages by name or description
  async searchStages(searchTerm, plantId = null) {
    try {
      let q = query(this.collectionRef);
      
      if (plantId) {
        q = query(q, where('plantId', '==', plantId));
      }

      const querySnapshot = await getDocs(q);
      const stages = [];
      
      querySnapshot.forEach((doc) => {
        const data = doc.data();
        const name = data.name.toLowerCase();
        const description = data.description.toLowerCase();
        const search = searchTerm.toLowerCase();

        if (name.includes(search) || description.includes(search)) {
          stages.push({
            id: doc.id,
            ...data
          });
        }
      });

      return stages;
    } catch (error) {
      console.error('Error searching stages:', error);
      throw error;
    }
  }

  // Get stages with specific care requirements
  async getStagesByCareType(careType, plantId = null) {
    try {
      let q = query(this.collectionRef);
      
      if (plantId) {
        q = query(q, where('plantId', '==', plantId));
      }

      const querySnapshot = await getDocs(q);
      const stages = [];
      
      querySnapshot.forEach((doc) => {
        const data = doc.data();
        const careInstructions = data.careInstructions || {};
        
        if (careInstructions[careType]) {
          stages.push({
            id: doc.id,
            ...data
          });
        }
      });

      return stages;
    } catch (error) {
      console.error('Error getting stages by care type:', error);
      throw error;
    }
  }

  // Update stage images
  async updateStageImages(stageId, images) {
    try {
      const docRef = doc(db, COLLECTIONS.PLANT_STAGES, stageId);
      await updateDoc(docRef, {
        images: images,
        updatedAt: serverTimestamp()
      });

      return true;
    } catch (error) {
      console.error('Error updating stage images:', error);
      throw error;
    }
  }

  // Add tip to stage
  async addStageTip(stageId, tip) {
    try {
      const stage = await this.getStageById(stageId);
      const currentTips = stage.tips || [];
      
      const newTip = {
        id: Date.now().toString(),
        ...tip,
        createdAt: new Date().toISOString()
      };

      const updatedTips = [...currentTips, newTip];
      
      const docRef = doc(db, COLLECTIONS.PLANT_STAGES, stageId);
      await updateDoc(docRef, {
        tips: updatedTips,
        updatedAt: serverTimestamp()
      });

      return newTip.id;
    } catch (error) {
      console.error('Error adding stage tip:', error);
      throw error;
    }
  }

  // Remove tip from stage
  async removeStageTip(stageId, tipId) {
    try {
      const stage = await this.getStageById(stageId);
      const currentTips = stage.tips || [];
      
      const updatedTips = currentTips.filter(tip => tip.id !== tipId);
      
      const docRef = doc(db, COLLECTIONS.PLANT_STAGES, stageId);
      await updateDoc(docRef, {
        tips: updatedTips,
        updatedAt: serverTimestamp()
      });

      return true;
    } catch (error) {
      console.error('Error removing stage tip:', error);
      throw error;
    }
  }
}

export default new PlantStagesService();
