import { useState, useEffect } from 'react';
// Firebase service import disabled - using mock data only
// import { plantStagesService, realtimeService } from '../services/firebaseService';

// Mock stages data
const mockStages = [
  {
    id: 'stage-1',
    plantId: 'mock-1',
    order: 1,
    name: '<PERSON><PERSON>',
    description: 'Tohumun toprakta ekilmesi ve ilk bakım aşaması. Uygun derinlikte ekim yapılmalı.',
    image: 'https://images.unsplash.com/photo-1416879595882-3373a0480b5b?w=400&h=400&fit=crop',
    duration: '1-2 hafta',
    tips: ['Toprağı nemli tutun', '<PERSON><PERSON><PERSON>ş ışığından koruyun']
  },
  {
    id: 'stage-2',
    plantId: 'mock-1',
    order: 2,
    name: 'Çimlen<PERSON>',
    description: 'Tohumun çimlenmesi ve ilk yaprakların çıkması. Düzenli sulama gereklidir.',
    image: 'https://images.unsplash.com/photo-1585320806297-9794b3e4eeae?w=400&h=400&fit=crop',
    duration: '1-3 hafta',
    tips: ['Düzenli sulama yapın', 'İlk yaprakları bekleyin']
  },
  {
    id: 'stage-3',
    plantId: 'mock-1',
    order: 3,
    name: 'Fide Dönemi',
    description: 'Bitkinin güçlenmesi ve büyümesi. Gübre desteği verilebilir.',
    image: 'https://images.unsplash.com/photo-1416879595882-3373a0480b5b?w=400&h=400&fit=crop',
    duration: '2-4 hafta',
    tips: ['Gübre desteği verin', 'Büyümeyi takip edin']
  },
  {
    id: 'stage-4',
    plantId: 'mock-1',
    order: 4,
    name: 'Olgunlaşma',
    description: 'Bitkinin tam olgunluğa erişmesi ve çiçek açması.',
    image: 'https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=400&h=400&fit=crop',
    duration: '4-8 hafta',
    tips: ['Çiçeklenmeyi bekleyin', 'Bakımı sürdürün']
  }
];

// Bitki aşamalarını getiren hook
export const usePlantStages = (plantId, useRealtime = false) => {
  const [stages, setStages] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    if (plantId) {
      if (useRealtime) {
        // Real-time listener kullan (mock)
        console.log('🔄 Mock real-time stages listener başlatılıyor:', plantId);
        setTimeout(() => {
          const mockPlantStages = mockStages.filter(stage => stage.plantId === plantId);
          setStages(mockPlantStages);
          console.log('✅ Mock stages kullanılıyor (Firebase disabled):', mockPlantStages.length);
          setLoading(false);
          setError(null);
        }, 1000);

        return () => {
          console.log('🔄 Mock real-time stages listener kapatılıyor...');
        };
      } else {
        // Tek seferlik veri çekme
        loadStages();
      }
    }
  }, [plantId, useRealtime]);

  const loadStages = async () => {
    try {
      setLoading(true);
      setError(null);
      
      // Mock Firebase service call
      const stagesData = [];
      
      if (stagesData.length > 0) {
        setStages(stagesData);
        console.log('✅ Firebase stages yüklendi:', stagesData.length);
      } else {
        // Firebase'de veri yoksa mock data kullan
        const mockPlantStages = mockStages.filter(stage => stage.plantId === plantId);
        setStages(mockPlantStages);
        console.log('✅ Mock stages kullanılıyor (Firebase boş):', mockPlantStages.length);
      }
    } catch (err) {
      console.error('❌ Stages yüklenemedi, mock data kullanılıyor:', err);
      const mockPlantStages = mockStages.filter(stage => stage.plantId === plantId);
      setStages(mockPlantStages);
      setError('Firebase bağlantı hatası, örnek veriler gösteriliyor');
    } finally {
      setLoading(false);
    }
  };

  const refreshStages = () => {
    if (!useRealtime) {
      loadStages();
    }
  };

  // Belirli bir aşamayı getir
  const getStageByOrder = (order) => {
    return stages.find(stage => stage.order === order);
  };

  // Sonraki aşamayı getir
  const getNextStage = (currentOrder) => {
    return stages.find(stage => stage.order === currentOrder + 1);
  };

  // Önceki aşamayı getir
  const getPreviousStage = (currentOrder) => {
    return stages.find(stage => stage.order === currentOrder - 1);
  };

  return {
    stages,
    loading,
    error,
    refreshStages,
    getStageByOrder,
    getNextStage,
    getPreviousStage
  };
};

// Yeni aşama ekleme hook'u
export const useAddPlantStage = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(false);

  const addStage = async (stageData) => {
    try {
      setLoading(true);
      setError(null);
      setSuccess(false);
      
      // Mock Firebase service call
      const stageId = 'mock-stage-id';
      
      setSuccess(true);
      console.log('✅ Yeni stage eklendi:', stageId);
      
      return stageId;
    } catch (err) {
      console.error('❌ Stage eklenemedi:', err);
      setError('Aşama eklenemedi');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const resetState = () => {
    setError(null);
    setSuccess(false);
  };

  return {
    addStage,
    loading,
    error,
    success,
    resetState
  };
};

// Aşama navigasyonu için hook
export const useStageNavigation = (stages) => {
  const [currentStageOrder, setCurrentStageOrder] = useState(1);

  const currentStage = stages.find(stage => stage.order === currentStageOrder);
  const isFirstStage = currentStageOrder === 1;
  const isLastStage = currentStageOrder === stages.length;

  const goToNextStage = () => {
    if (!isLastStage) {
      setCurrentStageOrder(prev => prev + 1);
    }
  };

  const goToPreviousStage = () => {
    if (!isFirstStage) {
      setCurrentStageOrder(prev => prev - 1);
    }
  };

  const goToStage = (order) => {
    if (order >= 1 && order <= stages.length) {
      setCurrentStageOrder(order);
    }
  };

  const resetToFirstStage = () => {
    setCurrentStageOrder(1);
  };

  return {
    currentStage,
    currentStageOrder,
    isFirstStage,
    isLastStage,
    goToNextStage,
    goToPreviousStage,
    goToStage,
    resetToFirstStage
  };
};
