import React from 'react';
import { View, Text, Image, TouchableOpacity } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { COLORS } from '../../utils/constants';
import styles from './UniversalCard.styles';

const UniversalCard = ({ 
  item, 
  onPress, 
  imageSource, 
  title, 
  subtitle, 
  description, 
  category, 
  isNew, 
  isPopular, 
  careInfo, 
  buttonText = "Detay Oku",
  style 
}) => {
  const handleCardPress = () => {
    if (onPress) {
      onPress(item);
    }
  };

  return (
    <TouchableOpacity 
      style={[styles.card, style]} 
      onPress={handleCardPress}
      activeOpacity={0.9}
    >
      <View style={styles.gradientBackground}>
        {/* Sol Taraf - Görsel */}
        <View style={styles.imageContainer}>
          <Image
            source={imageSource}
            style={styles.image}
            resizeMode="cover"
          />
        </View>

        {/* Sağ Taraf - İçerik */}
        <View style={styles.textContainer}>
          <View style={styles.contentArea}>
            <View style={styles.topSection}>
              <Text style={styles.category}>{category || 'Kategori'}</Text>
              {(isNew || isPopular) && (
                <View style={styles.statusBadge}>
                  <Text style={styles.statusText}>
                    {isNew ? 'YENİ' : 'POPÜLER'}
                  </Text>
                </View>
              )}
            </View>
            
            <Text style={styles.title}>{title}</Text>
            {subtitle && (
              <Text style={styles.subtitle}>{subtitle}</Text>
            )}
            {description && (
              <Text style={styles.description} numberOfLines={2}>
                {description}
              </Text>
            )}
            
            {careInfo && (
              <View style={styles.careSection}>
                <View style={styles.careBubble}>
                  <Ionicons name="water-outline" size={12} color={COLORS.white} />
                  <Text style={styles.careText}>{careInfo.watering || 'Orta'}</Text>
                </View>
                <View style={styles.careBubble}>
                  <Ionicons name="sunny-outline" size={12} color={COLORS.white} />
                  <Text style={styles.careText}>{careInfo.light || 'Güneşli'}</Text>
                </View>
              </View>
            )}
          </View>
        </View>

        {/* Detay Oku Butonu - Card'ın sağ altında */}
        <TouchableOpacity
          style={styles.detailButton}
          onPress={handleCardPress}
          activeOpacity={0.8}
        >
          <Text style={styles.detailButtonText}>{buttonText}</Text>
          <Ionicons name="arrow-forward" size={14} color={COLORS.white} />
        </TouchableOpacity>
      </View>
    </TouchableOpacity>
  );
};

export default UniversalCard;
