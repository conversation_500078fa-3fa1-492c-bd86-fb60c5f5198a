import React from 'react';
import { View, Text, Image, TouchableOpacity } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { COLORS } from '../../utils/constants';
import styles from './UniversalCard.styles';

const UniversalCard = ({ 
  item, 
  onPress, 
  imageSource, 
  title, 
  subtitle, 
  description, 
  category, 
  isNew, 
  isPopular, 
  careInfo, 
  buttonText = "Detay Oku",
  style 
}) => {
  const handleCardPress = () => {
    if (onPress) {
      onPress(item);
    }
  };

  return (
    <TouchableOpacity 
      style={[styles.card, style]} 
      onPress={handleCardPress}
      activeOpacity={0.9}
    >
      <View style={styles.gradientBackground}>
        {/* Sol Taraf - Görsel */}
        <View style={styles.imageContainer}>
          <Image
            source={imageSource}
            style={styles.image}
            resizeMode="cover"
          />
        </View>

        {/* Sağ Taraf - İçerik */}
        <View style={styles.textContainer}>
          <View style={styles.contentArea}>
            <Text style={styles.category}>{category || 'Kategori'}</Text>
            <Text style={styles.title}>{title}</Text>
            {subtitle && (
              <Text style={styles.subtitle}>{subtitle}</Text>
            )}
          </View>
        </View>

        {/* Detay Oku Butonu - Card'ın sağ altında */}
        <TouchableOpacity
          style={styles.detailButton}
          onPress={handleCardPress}
          activeOpacity={0.8}
        >
          <Text style={styles.detailButtonText}>{buttonText}</Text>
          <Ionicons name="arrow-forward" size={14} color={COLORS.white} />
        </TouchableOpacity>
      </View>
    </TouchableOpacity>
  );
};

export default UniversalCard;
