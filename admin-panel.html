<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WikiPlant Admin Panel</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #7C4D25 0%, #C9E265 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: #FC7138;
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .content {
            padding: 40px;
        }

        .section {
            margin-bottom: 40px;
        }

        .section h2 {
            color: #7C4D25;
            font-size: 1.5rem;
            margin-bottom: 20px;
            border-bottom: 2px solid #C9E265;
            padding-bottom: 10px;
        }

        .button-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .action-button {
            background: linear-gradient(135deg, #C9E265 0%, #7C4D25 100%);
            color: white;
            border: none;
            padding: 20px;
            border-radius: 15px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }

        .action-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.2);
        }

        .action-button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .action-button.plants { background: linear-gradient(135deg, #C9E265 0%, #4ECDC4 100%); }
        .action-button.articles { background: linear-gradient(135deg, #FC7138 0%, #FF6B9D 100%); }
        .action-button.categories { background: linear-gradient(135deg, #7C4D25 0%, #45B7D1 100%); }
        .action-button.all { background: linear-gradient(135deg, #2E7D32 0%, #4CAF50 100%); }

        .logs-container {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 20px;
            max-height: 400px;
            overflow-y: auto;
            border: 1px solid #e9ecef;
        }

        .log-entry {
            padding: 10px 15px;
            margin-bottom: 10px;
            border-radius: 8px;
            border-left: 4px solid;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
        }

        .log-info { background: #e3f2fd; border-left-color: #2196f3; }
        .log-success { background: #e8f5e8; border-left-color: #4caf50; }
        .log-error { background: #ffebee; border-left-color: #f44336; }

        .log-timestamp {
            color: #666;
            font-size: 0.8rem;
            margin-bottom: 5px;
        }

        .status-bar {
            background: #f8f9fa;
            padding: 15px 20px;
            border-top: 1px solid #e9ecef;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .status-indicator {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .status-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #4caf50;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }

        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #FC7138;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .clear-logs {
            background: #6c757d;
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 0.9rem;
        }

        .api-info {
            background: #e3f2fd;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
        }

        .api-info h3 {
            color: #1976d2;
            margin-bottom: 10px;
        }

        .api-endpoint {
            background: white;
            padding: 10px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            margin: 5px 0;
            border-left: 3px solid #2196f3;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🌱 WikiPlant Admin Panel</h1>
            <p>Dallardan Bilgi - Veri Yönetim Sistemi</p>
        </div>

        <div class="content">
            <!-- API Information -->
            <div class="section">
                <h2>📡 API Bilgileri</h2>
                <div class="api-info">
                    <h3>API Endpoint'leri:</h3>
                    <div class="api-endpoint">GET /api/health - Sistem durumu</div>
                    <div class="api-endpoint">POST /api/load-plants - Bitki verilerini yükle</div>
                    <div class="api-endpoint">POST /api/load-articles - Makale verilerini yükle</div>
                    <div class="api-endpoint">POST /api/load-categories - Kategori verilerini yükle</div>
                    <div class="api-endpoint">POST /api/load-all - Tüm verileri yükle</div>
                </div>
            </div>

            <!-- Data Loading Actions -->
            <div class="section">
                <h2>📊 Veri Yükleme İşlemleri</h2>
                <div class="button-grid">
                    <button class="action-button plants" onclick="loadData('plants')">
                        🌱 Bitki Verilerini Yükle
                    </button>
                    <button class="action-button articles" onclick="loadData('articles')">
                        📚 Makale Verilerini Yükle
                    </button>
                    <button class="action-button categories" onclick="loadData('categories')">
                        📂 Kategori Verilerini Yükle
                    </button>
                    <button class="action-button all" onclick="loadData('all')">
                        🚀 Tüm Verileri Yükle
                    </button>
                </div>
            </div>

            <!-- Loading Indicator -->
            <div class="loading" id="loading">
                <div class="spinner"></div>
                <p>İşlem devam ediyor...</p>
            </div>

            <!-- Logs Section -->
            <div class="section">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                    <h2>📝 İşlem Logları</h2>
                    <button class="clear-logs" onclick="clearLogs()">🗑️ Temizle</button>
                </div>
                <div class="logs-container" id="logs">
                    <div class="log-entry log-info">
                        <div class="log-timestamp">Sistem başlatıldı</div>
                        <div>WikiPlant Admin Panel hazır</div>
                    </div>
                </div>
            </div>
        </div>

        <div class="status-bar">
            <div class="status-indicator">
                <div class="status-dot"></div>
                <span>API Bağlantısı Aktif</span>
            </div>
            <div>
                <span id="timestamp">Son güncelleme: </span>
            </div>
        </div>
    </div>

    <script>
        const API_BASE_URL = 'http://localhost:3001/api';
        
        function addLog(message, type = 'info') {
            const logsContainer = document.getElementById('logs');
            const timestamp = new Date().toLocaleTimeString();
            
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry log-${type}`;
            logEntry.innerHTML = `
                <div class="log-timestamp">${timestamp}</div>
                <div>${message}</div>
            `;
            
            logsContainer.appendChild(logEntry);
            logsContainer.scrollTop = logsContainer.scrollHeight;
        }

        function clearLogs() {
            const logsContainer = document.getElementById('logs');
            logsContainer.innerHTML = '';
            addLog('Loglar temizlendi', 'info');
        }

        function showLoading(show) {
            const loading = document.getElementById('loading');
            const buttons = document.querySelectorAll('.action-button');
            
            loading.style.display = show ? 'block' : 'none';
            buttons.forEach(btn => btn.disabled = show);
        }

        async function loadData(type) {
            showLoading(true);
            
            const endpoints = {
                'plants': '/load-plants',
                'articles': '/load-articles', 
                'categories': '/load-categories',
                'all': '/load-all'
            };

            const messages = {
                'plants': '🌱 Bitki verilerini yükleme başlatıldı...',
                'articles': '📚 Makale verilerini yükleme başlatıldı...',
                'categories': '📂 Kategori verilerini yükleme başlatıldı...',
                'all': '🚀 Tüm verileri yükleme başlatıldı...'
            };

            addLog(messages[type], 'info');

            try {
                const response = await fetch(API_BASE_URL + endpoints[type], {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                const result = await response.json();

                if (result.success) {
                    addLog(`✅ ${result.message}`, 'success');
                    if (result.counts) {
                        addLog(`📊 Yüklenen veriler: ${JSON.stringify(result.counts)}`, 'info');
                    } else if (result.count) {
                        addLog(`📊 Yüklenen kayıt sayısı: ${result.count}`, 'info');
                    }
                } else {
                    addLog(`❌ Hata: ${result.error}`, 'error');
                }
            } catch (error) {
                addLog(`❌ Bağlantı hatası: ${error.message}`, 'error');
            } finally {
                showLoading(false);
                updateTimestamp();
            }
        }

        function updateTimestamp() {
            const timestampElement = document.getElementById('timestamp');
            timestampElement.textContent = `Son güncelleme: ${new Date().toLocaleString()}`;
        }

        // Check API health on load
        async function checkAPIHealth() {
            try {
                const response = await fetch(API_BASE_URL + '/health');
                const result = await response.json();
                
                if (result.status === 'OK') {
                    addLog('✅ API bağlantısı başarılı', 'success');
                } else {
                    addLog('⚠️ API bağlantısında sorun var', 'error');
                }
            } catch (error) {
                addLog('❌ API\'ye bağlanılamıyor. Sunucunun çalıştığından emin olun.', 'error');
            }
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            updateTimestamp();
            checkAPIHealth();
        });
    </script>
</body>
</html>
