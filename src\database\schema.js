// Firebase Firestore Database Schema for <PERSON><PERSON><PERSON> Bilgi <PERSON>pp

/*
DATABASE STRUCTURE:

1. USERS COLLECTION
/users/{userId}
{
  id: string,
  email: string,
  displayName: string,
  photoURL: string,
  createdAt: timestamp,
  updatedAt: timestamp,
  preferences: {
    language: string,
    notifications: boolean,
    theme: string
  },
  favorites: [plantId1, plantId2, ...],
  userPlants: [
    {
      plantId: string,
      nickname: string,
      plantedDate: timestamp,
      currentStage: number,
      notes: string,
      images: [imageUrl1, imageUrl2, ...]
    }
  ]
}

2. PLANTS COLLECTION
/plants/{plantId}
{
  id: string,
  name: string,
  latinName: string,
  category: string,
  description: string,
  origin: string,
  lifespan: string,
  size: string,
  difficulty: string, // 'Kolay', 'Orta', 'Zor'
  isNew: boolean,
  isPopular: boolean,
  mainImage: string,
  images: [imageUrl1, imageUrl2, ...],
  care: {
    watering: string,
    light: string,
    soil: string,
    temperature: string,
    humidity: string,
    fertilizer: string
  },
  stages: [
    {
      stageId: number,
      name: string,
      description: string,
      duration: string,
      image: string,
      tips: [tip1, tip2, ...]
    }
  ],
  treatment: {
    recommendations: [
      {
        problem: string,
        description: string,
        solution: string,
        products: [
          {
            name: string,
            dosage: string,
            price: number,
            link: string
          }
        ],
        timing: string,
        severity: string // 'Düşük', 'Orta', 'Yüksek'
      }
    ]
  },
  tags: [tag1, tag2, ...],
  createdAt: timestamp,
  updatedAt: timestamp,
  createdBy: userId,
  likes: number,
  views: number
}

3. ARTICLES COLLECTION
/articles/{articleId}
{
  id: string,
  title: string,
  content: string,
  summary: string,
  author: string,
  authorId: string,
  category: string,
  tags: [tag1, tag2, ...],
  image: string,
  images: [imageUrl1, imageUrl2, ...],
  publishedAt: timestamp,
  updatedAt: timestamp,
  isPublished: boolean,
  isFeatured: boolean,
  readTime: number, // minutes
  likes: number,
  views: number,
  comments: [
    {
      id: string,
      userId: string,
      userName: string,
      comment: string,
      createdAt: timestamp,
      likes: number
    }
  ]
}

4. CATEGORIES COLLECTION
/categories/{categoryId}
{
  id: string,
  name: string,
  description: string,
  icon: string,
  color: string,
  plantCount: number,
  articleCount: number,
  order: number,
  isActive: boolean
}

5. PLANT_CARE_LOGS COLLECTION
/plantCareLogs/{logId}
{
  id: string,
  userId: string,
  plantId: string,
  userPlantId: string,
  action: string, // 'watering', 'fertilizing', 'pruning', 'repotting'
  notes: string,
  images: [imageUrl1, imageUrl2, ...],
  date: timestamp,
  nextDueDate: timestamp,
  reminder: boolean
}

6. NOTIFICATIONS COLLECTION
/notifications/{notificationId}
{
  id: string,
  userId: string,
  type: string, // 'care_reminder', 'new_article', 'plant_stage', 'system'
  title: string,
  message: string,
  data: object, // Additional data based on type
  isRead: boolean,
  createdAt: timestamp,
  scheduledFor: timestamp
}

7. APP_SETTINGS COLLECTION
/appSettings/general
{
  version: string,
  minVersion: string,
  maintenanceMode: boolean,
  featuredPlants: [plantId1, plantId2, ...],
  featuredArticles: [articleId1, articleId2, ...],
  announcements: [
    {
      id: string,
      title: string,
      message: string,
      type: string,
      startDate: timestamp,
      endDate: timestamp,
      isActive: boolean
    }
  ]
}

INDEXES NEEDED:
- plants: category, difficulty, isNew, isPopular, createdAt
- articles: category, publishedAt, isFeatured, isPublished
- users: email, createdAt
- plantCareLogs: userId, plantId, date, nextDueDate
- notifications: userId, isRead, createdAt, scheduledFor

SECURITY RULES:
- Users can only read/write their own data
- Plants and articles are readable by all authenticated users
- Only admins can create/update plants and articles
- Care logs are private to each user
- Categories and app settings are read-only for users
*/

export const COLLECTIONS = {
  USERS: 'users',
  PLANTS: 'plants',
  ARTICLES: 'articles',
  CATEGORIES: 'categories',
  PLANT_CARE_LOGS: 'plantCareLogs',
  NOTIFICATIONS: 'notifications',
  APP_SETTINGS: 'appSettings'
};

export const PLANT_DIFFICULTIES = {
  EASY: 'Kolay',
  MEDIUM: 'Orta',
  HARD: 'Zor'
};

export const CARE_ACTIONS = {
  WATERING: 'watering',
  FERTILIZING: 'fertilizing',
  PRUNING: 'pruning',
  REPOTTING: 'repotting'
};

export const NOTIFICATION_TYPES = {
  CARE_REMINDER: 'care_reminder',
  NEW_ARTICLE: 'new_article',
  PLANT_STAGE: 'plant_stage',
  SYSTEM: 'system'
};

export const ARTICLE_CATEGORIES = {
  CARE: 'Bakım',
  DISEASES: 'Hastalıklar',
  PROPAGATION: 'Çoğaltma',
  SEASONAL: 'Mevsimsel',
  BEGINNER: 'Başlangıç',
  ADVANCED: 'İleri Seviye'
};

export const PLANT_CATEGORIES = {
  FLOWER: 'Çiçek',
  VEGETABLE: 'Sebze',
  AROMATIC: 'Aromatik',
  SUCCULENT: 'Sukulent',
  TREE: 'Ağaç',
  INDOOR: 'İç Mekan',
  OUTDOOR: 'Dış Mekan'
};
