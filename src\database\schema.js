// Firebase Firestore Database Schema for <PERSON><PERSON><PERSON> Bilgi App

/*
ENHANCED DATABASE STRUCTURE:

1. USERS COLLECTION
/users/{userId}
{
  id: string,
  email: string,
  displayName: string,
  photoURL: string,
  createdAt: timestamp,
  updatedAt: timestamp,
  preferences: {
    language: string,
    notifications: boolean,
    theme: string
  },
  favorites: [plantId1, plantId2, ...],
  userPlants: [
    {
      id: string,
      plantId: string,
      nickname: string,
      plantedDate: timestamp,
      currentStageId: string,
      notes: string,
      images: [imageUrl1, imageUrl2, ...],
      careSchedule: {
        watering: { frequency: string, lastDate: timestamp, nextDate: timestamp },
        fertilizing: { frequency: string, lastDate: timestamp, nextDate: timestamp },
        pruning: { frequency: string, lastDate: timestamp, nextDate: timestamp }
      }
    }
  ]
}

2. PLANTS COLLECTION
/plants/{plantId}
{
  id: string,
  name: string,
  latinName: string,
  category: string,
  description: string,
  origin: string,
  lifespan: string,
  size: string,
  difficulty: string, // 'Kolay', 'Orta', 'Zor'
  isNew: boolean,
  isPopular: boolean,
  mainImage: string,
  images: [imageUrl1, imageUrl2, ...],
  care: {
    watering: {
      frequency: string, // 'Günlük', 'Haftada 2-3', 'Haftada 1', 'Az'
      amount: string,    // 'Az', 'Orta', 'Bol'
      method: string,    // 'Üstten', 'Alttan', 'Püskürtme'
      notes: string
    },
    light: {
      type: string,      // 'Doğrudan güneş', 'Parlak dolaylı', 'Yarı gölge', 'Gölge'
      duration: string,  // '6-8 saat', '4-6 saat', '2-4 saat'
      direction: string, // 'Güney', 'Doğu', 'Batı', 'Kuzey'
      notes: string
    },
    soil: {
      type: string,      // 'Kaktüs toprağı', 'Organik toprak', 'Kum karışımı'
      ph: string,        // 'Asidik (5.5-6.5)', 'Nötr (6.5-7.5)', 'Bazik (7.5-8.5)'
      drainage: string,  // 'İyi drene', 'Orta drene', 'Nemli'
      nutrients: string
    },
    temperature: {
      min: number,       // Minimum sıcaklık
      max: number,       // Maksimum sıcaklık
      optimal: string,   // Optimal aralık
      winter: string,    // Kış bakımı
      summer: string     // Yaz bakımı
    },
    humidity: {
      level: string,     // 'Düşük', 'Orta', 'Yüksek'
      percentage: string, // '%40-60'
      methods: [string]  // ['Püskürtme', 'Su kabı', 'Nemlendiriciler']
    },
    fertilizer: {
      type: string,      // 'Sıvı gübre', 'Granül gübre', 'Organik gübre'
      frequency: string, // 'Ayda 1', 'Ayda 2', 'Mevsimlik'
      season: string,    // 'İlkbahar-Yaz', 'Tüm yıl', 'Büyüme dönemi'
      npk: string        // '10-10-10', '20-20-20'
    }
  },
  stageIds: [stageId1, stageId2, ...], // Reference to plant_stages collection
  treatmentIds: [treatmentId1, treatmentId2, ...], // Reference to plant_treatments collection
  tags: [tag1, tag2, ...],
  createdAt: timestamp,
  updatedAt: timestamp,
  createdBy: userId,
  likes: number,
  views: number,
  averageGrowthTime: number, // days
  successRate: number // percentage
}

3. PLANT_STAGES COLLECTION
/plant_stages/{stageId}
{
  id: string,
  plantId: string,
  stageNumber: number, // 1, 2, 3, etc.
  name: string,        // 'Tohum', 'Çimlenme', 'Fide', etc.
  description: string,
  duration: {
    min: number,       // Minimum gün
    max: number,       // Maksimum gün
    average: number,   // Ortalama gün
    unit: string       // 'gün', 'hafta', 'ay'
  },
  images: [
    {
      url: string,
      caption: string,
      isPrimary: boolean
    }
  ],
  careInstructions: {
    watering: string,
    light: string,
    temperature: string,
    humidity: string,
    fertilizer: string,
    specialCare: [string]
  },
  tips: [
    {
      title: string,
      description: string,
      importance: string // 'Düşük', 'Orta', 'Yüksek'
    }
  ],
  commonProblems: [
    {
      problem: string,
      solution: string,
      prevention: string
    }
  ],
  nextStageIndicators: [string], // Signs that plant is ready for next stage
  createdAt: timestamp,
  updatedAt: timestamp
}

4. PLANT_TREATMENTS COLLECTION
/plant_treatments/{treatmentId}
{
  id: string,
  plantId: string,
  problem: string,
  category: string,    // 'Hastalık', 'Zararlı', 'Beslenme', 'Çevre'
  severity: string,    // 'Düşük', 'Orta', 'Yüksek', 'Kritik'
  description: string,
  symptoms: [string],  // Belirtiler
  causes: [string],    // Nedenler
  prevention: {
    methods: [string],
    frequency: string,
    seasonality: string
  },
  treatment: {
    immediate: [string], // Acil müdahale
    longTerm: [string],  // Uzun vadeli tedavi
    organic: [string],   // Organik çözümler
    chemical: [string]   // Kimyasal çözümler
  },
  products: [
    {
      name: string,
      type: string,      // 'Organik', 'Kimyasal', 'Biyolojik'
      dosage: string,
      application: string, // Uygulama şekli
      frequency: string,
      price: {
        min: number,
        max: number,
        currency: string
      },
      availability: string, // 'Yaygın', 'Özel', 'İthal'
      link: string
    }
  ],
  timing: {
    bestTime: string,    // En iyi uygulama zamanı
    avoidTime: string,   // Kaçınılması gereken zaman
    frequency: string,
    duration: string
  },
  images: [
    {
      url: string,
      caption: string,
      type: string       // 'problem', 'solution', 'before', 'after'
    }
  ],
  effectiveness: number, // 1-10 arası etkinlik skoru
  difficulty: string,    // 'Kolay', 'Orta', 'Zor'
  createdAt: timestamp,
  updatedAt: timestamp
}

5. ARTICLES COLLECTION
/articles/{articleId}
{
  id: string,
  title: string,
  content: string,
  summary: string,
  author: string,
  authorId: string,
  category: string,
  tags: [tag1, tag2, ...],
  image: string,
  images: [imageUrl1, imageUrl2, ...],
  publishedAt: timestamp,
  updatedAt: timestamp,
  isPublished: boolean,
  isFeatured: boolean,
  readTime: number, // minutes
  likes: number,
  views: number,
  comments: [
    {
      id: string,
      userId: string,
      userName: string,
      comment: string,
      createdAt: timestamp,
      likes: number
    }
  ]
}

4. CATEGORIES COLLECTION
/categories/{categoryId}
{
  id: string,
  name: string,
  description: string,
  icon: string,
  color: string,
  plantCount: number,
  articleCount: number,
  order: number,
  isActive: boolean
}

5. PLANT_CARE_LOGS COLLECTION
/plantCareLogs/{logId}
{
  id: string,
  userId: string,
  plantId: string,
  userPlantId: string,
  action: string, // 'watering', 'fertilizing', 'pruning', 'repotting'
  notes: string,
  images: [imageUrl1, imageUrl2, ...],
  date: timestamp,
  nextDueDate: timestamp,
  reminder: boolean
}

6. NOTIFICATIONS COLLECTION
/notifications/{notificationId}
{
  id: string,
  userId: string,
  type: string, // 'care_reminder', 'new_article', 'plant_stage', 'system'
  title: string,
  message: string,
  data: object, // Additional data based on type
  isRead: boolean,
  createdAt: timestamp,
  scheduledFor: timestamp
}

7. APP_SETTINGS COLLECTION
/appSettings/general
{
  version: string,
  minVersion: string,
  maintenanceMode: boolean,
  featuredPlants: [plantId1, plantId2, ...],
  featuredArticles: [articleId1, articleId2, ...],
  announcements: [
    {
      id: string,
      title: string,
      message: string,
      type: string,
      startDate: timestamp,
      endDate: timestamp,
      isActive: boolean
    }
  ]
}

INDEXES NEEDED:
- plants: category, difficulty, isNew, isPopular, createdAt
- articles: category, publishedAt, isFeatured, isPublished
- users: email, createdAt
- plantCareLogs: userId, plantId, date, nextDueDate
- notifications: userId, isRead, createdAt, scheduledFor

SECURITY RULES:
- Users can only read/write their own data
- Plants and articles are readable by all authenticated users
- Only admins can create/update plants and articles
- Care logs are private to each user
- Categories and app settings are read-only for users
*/

export const COLLECTIONS = {
  USERS: 'users',
  PLANTS: 'plants',
  PLANT_STAGES: 'plant_stages',
  PLANT_TREATMENTS: 'plant_treatments',
  ARTICLES: 'articles',
  CATEGORIES: 'categories',
  PLANT_CARE_LOGS: 'plantCareLogs',
  NOTIFICATIONS: 'notifications',
  APP_SETTINGS: 'appSettings'
};

export const PLANT_DIFFICULTIES = {
  EASY: 'Kolay',
  MEDIUM: 'Orta',
  HARD: 'Zor'
};

export const CARE_ACTIONS = {
  WATERING: 'watering',
  FERTILIZING: 'fertilizing',
  PRUNING: 'pruning',
  REPOTTING: 'repotting'
};

export const NOTIFICATION_TYPES = {
  CARE_REMINDER: 'care_reminder',
  NEW_ARTICLE: 'new_article',
  PLANT_STAGE: 'plant_stage',
  SYSTEM: 'system'
};

export const ARTICLE_CATEGORIES = {
  CARE: 'Bakım',
  DISEASES: 'Hastalıklar',
  PROPAGATION: 'Çoğaltma',
  SEASONAL: 'Mevsimsel',
  BEGINNER: 'Başlangıç',
  ADVANCED: 'İleri Seviye'
};

export const PLANT_CATEGORIES = {
  FLOWER: 'Çiçek',
  VEGETABLE: 'Sebze',
  AROMATIC: 'Aromatik',
  SUCCULENT: 'Sukulent',
  TREE: 'Ağaç',
  INDOOR: 'İç Mekan',
  OUTDOOR: 'Dış Mekan'
};

export const STAGE_NAMES = {
  SEED: 'Tohum',
  GERMINATION: 'Çimlenme',
  SEEDLING: 'Fide',
  VEGETATIVE: 'Yapraklanma',
  GROWTH: 'Büyüme',
  BRANCHING: 'Dallanma',
  BUDDING: 'Tomurcuklanma',
  FLOWERING: 'Çiçeklenme',
  FRUITING: 'Meyve/Tohum',
  MATURITY: 'Olgunluk',
  DORMANCY: 'Dinlenme',
  PRUNING: 'Budama'
};

export const TREATMENT_CATEGORIES = {
  DISEASE: 'Hastalık',
  PEST: 'Zararlı',
  NUTRITION: 'Beslenme',
  ENVIRONMENT: 'Çevre',
  CARE: 'Bakım'
};

export const TREATMENT_SEVERITY = {
  LOW: 'Düşük',
  MEDIUM: 'Orta',
  HIGH: 'Yüksek',
  CRITICAL: 'Kritik'
};

export const WATERING_FREQUENCY = {
  DAILY: 'Günlük',
  TWICE_WEEKLY: 'Haftada 2-3',
  WEEKLY: 'Haftada 1',
  BIWEEKLY: 'İki haftada 1',
  MONTHLY: 'Ayda 1',
  MINIMAL: 'Az'
};

export const LIGHT_TYPES = {
  DIRECT_SUN: 'Doğrudan güneş',
  BRIGHT_INDIRECT: 'Parlak dolaylı',
  PARTIAL_SHADE: 'Yarı gölge',
  SHADE: 'Gölge',
  ARTIFICIAL: 'Yapay ışık'
};

export const SOIL_TYPES = {
  CACTUS: 'Kaktüs toprağı',
  ORGANIC: 'Organik toprak',
  SAND_MIX: 'Kum karışımı',
  CLAY: 'Killi toprak',
  LOAM: 'Balçık toprak',
  PEAT: 'Turba karışımı'
};

export const FERTILIZER_TYPES = {
  LIQUID: 'Sıvı gübre',
  GRANULAR: 'Granül gübre',
  ORGANIC: 'Organik gübre',
  SLOW_RELEASE: 'Yavaş salınımlı',
  COMPOST: 'Kompost'
};
