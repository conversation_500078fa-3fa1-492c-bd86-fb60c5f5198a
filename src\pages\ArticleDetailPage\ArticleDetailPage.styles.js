import { StyleSheet } from 'react-native';
import { COLORS, FONT_SIZES, SPACING } from '../../utils/constants';

export default StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },

  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: SPACING.lg,
    paddingVertical: SPACING.lg,
    paddingTop: SPACING.xxl,
    backgroundColor: '#FC7138', // Turuncu
    borderBottomLeftRadius: 20,
    borderBottomRightRadius: 20,
    shadowColor: COLORS.shadow,
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 6,
  },

  headerButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    alignItems: 'center',
    justifyContent: 'center',
  },

  headerTitle: {
    fontSize: FONT_SIZES.large,
    fontWeight: '700',
    color: COLORS.white,
    flex: 1,
    textAlign: 'center',
    marginHorizontal: SPACING.md,
  },

  shareButtons: {
    flexDirection: 'row',
    gap: SPACING.sm,
  },

  content: {
    flex: 1,
  },

  imageContainer: {
    width: '100%',
    height: 250,
    backgroundColor: COLORS.lightGray,
  },

  articleImage: {
    width: '100%',
    height: '100%',
  },

  articleContainer: {
    padding: SPACING.xl,
  },

  articleTitle: {
    fontSize: FONT_SIZES.header + 2,
    fontWeight: '800',
    color: COLORS.primary,
    lineHeight: FONT_SIZES.header + 8,
    marginBottom: SPACING.lg,
  },

  authorSection: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SPACING.sm,
    gap: SPACING.xs,
  },

  authorText: {
    fontSize: FONT_SIZES.small,
    color: COLORS.darkGray,
    fontWeight: '500',
  },

  dateSection: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SPACING.xl,
    gap: SPACING.xs,
  },

  dateText: {
    fontSize: FONT_SIZES.small,
    color: COLORS.darkGray,
  },

  articleContent: {
    fontSize: FONT_SIZES.medium,
    color: COLORS.primary,
    lineHeight: FONT_SIZES.medium + 8,
    textAlign: 'justify',
    marginBottom: SPACING.xl,
  },

  tagsContainer: {
    marginTop: SPACING.lg,
  },

  tagsTitle: {
    fontSize: FONT_SIZES.medium,
    fontWeight: '600',
    color: COLORS.primary,
    marginBottom: SPACING.md,
  },

  tagsWrapper: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: SPACING.sm,
  },

  tag: {
    backgroundColor: COLORS.lightGray,
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.xs,
    borderRadius: 16,
  },

  tagText: {
    fontSize: FONT_SIZES.small,
    color: COLORS.darkGray,
    fontWeight: '500',
  },



  errorContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: SPACING.xl,
  },

  errorText: {
    fontSize: FONT_SIZES.large,
    color: COLORS.darkGray,
    textAlign: 'center',
    marginBottom: SPACING.xl,
  },

  backButton: {
    backgroundColor: COLORS.primary,
    paddingHorizontal: SPACING.xl,
    paddingVertical: SPACING.md,
    borderRadius: 16,
  },

  backButtonText: {
    fontSize: FONT_SIZES.medium,
    fontWeight: '600',
    color: COLORS.white,
  },
});
