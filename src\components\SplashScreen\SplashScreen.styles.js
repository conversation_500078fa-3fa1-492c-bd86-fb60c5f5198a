import { StyleSheet, Dimensions } from 'react-native';
import { COLORS, FONT_SIZES, SPACING } from '../../utils/constants';

const { width, height } = Dimensions.get('window');

export default StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },

  content: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    position: 'relative',
  },

  logoContainer: {
    alignItems: 'center',
    marginBottom: SPACING.xl,
  },

  logo: {
    width: 120,
    height: 120,
    shadowColor: COLORS.shadow,
    shadowOffset: {
      width: 0,
      height: 8,
    },
    shadowOpacity: 0.3,
    shadowRadius: 16,
    elevation: 12,
  },

  textContainer: {
    alignItems: 'center',
    marginBottom: SPACING.xxl,
  },

  appName: {
    fontSize: FONT_SIZES.header + 8,
    fontWeight: '800',
    color: COLORS.white,
    textAlign: 'center',
    letterSpacing: 2,
    textShadowColor: 'rgba(0, 0, 0, 0.3)',
    textShadowOffset: { width: 0, height: 2 },
    textShadowRadius: 4,
    marginBottom: SPACING.sm,
  },

  tagline: {
    fontSize: FONT_SIZES.large,
    fontWeight: '500',
    color: COLORS.white,
    textAlign: 'center',
    opacity: 0.9,
    letterSpacing: 1,
    textShadowColor: 'rgba(0, 0, 0, 0.2)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
  },

  decorativeContainer: {
    position: 'absolute',
    width: width,
    height: height,
    top: 0,
    left: 0,
    zIndex: -1,
  },

  leaf1: {
    position: 'absolute',
    top: height * 0.15,
    right: width * 0.1,
    width: 40,
    height: 60,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 20,
    transform: [{ rotate: '45deg' }],
  },

  leaf2: {
    position: 'absolute',
    bottom: height * 0.2,
    left: width * 0.1,
    width: 30,
    height: 45,
    backgroundColor: 'rgba(255, 255, 255, 0.08)',
    borderRadius: 15,
    transform: [{ rotate: '-30deg' }],
  },

  leaf3: {
    position: 'absolute',
    top: height * 0.3,
    left: width * 0.15,
    width: 25,
    height: 35,
    backgroundColor: 'rgba(255, 255, 255, 0.06)',
    borderRadius: 12,
    transform: [{ rotate: '60deg' }],
  },

  bottomContainer: {
    position: 'absolute',
    bottom: SPACING.xxl * 2,
    alignItems: 'center',
  },

  bottomText: {
    fontSize: FONT_SIZES.medium,
    fontWeight: '400',
    color: COLORS.white,
    textAlign: 'center',
    opacity: 0.8,
    letterSpacing: 0.5,
    textShadowColor: 'rgba(0, 0, 0, 0.2)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
  },
});
