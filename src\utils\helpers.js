// Yardımcı fonksiyonlar
import { Platform } from 'react-native';

/**
 * Metni belirli uzunlukta keser ve ... ekler
 */
export const truncateText = (text, maxLength = 100) => {
  if (!text) return '';
  if (text.length <= maxLength) return text;
  return text.substring(0, maxLength).trim() + '...';
};

/**
 * Bitki arama fonksiyonu
 */
export const searchPlants = (plants, searchTerm) => {
  if (!searchTerm) return plants;
  
  const term = searchTerm.toLowerCase();
  return plants.filter(plant => 
    plant.name.toLowerCase().includes(term) ||
    plant.latinName.toLowerCase().includes(term) ||
    plant.description.toLowerCase().includes(term)
  );
};

/**
 * Favori durumunu kontrol eder
 */
export const isFavorite = (plantId, favorites) => {
  return favorites.includes(plantId);
};

/**
 * <PERSON><PERSON><PERSON><PERSON> a<PERSON>aması bilgisini getirir
 */
export const getStageInfo = (stageId, stages) => {
  return stages.find(stage => stage.id === stageId);
};

/**
 * Renk kodunu rgba formatına çevirir
 */
export const hexToRgba = (hex, alpha = 1) => {
  const r = parseInt(hex.slice(1, 3), 16);
  const g = parseInt(hex.slice(3, 5), 16);
  const b = parseInt(hex.slice(5, 7), 16);
  return `rgba(${r}, ${g}, ${b}, ${alpha})`;
};

/**
 * Platform kontrolü
 */
export const isIOS = () => {
  return Platform.OS === 'ios';
};

export const isAndroid = () => {
  return Platform.OS === 'android';
};

/**
 * Debounce fonksiyonu
 */
export const debounce = (func, wait) => {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
};

/**
 * Bitki kategorilerini filtreler
 */
export const filterPlantsByCategory = (plants, category) => {
  if (!category || category === 'all') return plants;
  return plants.filter(plant => plant.category === category);
};

/**
 * Popüler bitkileri getirir
 */
export const getPopularPlants = (plants) => {
  return plants.filter(plant => plant.isPopular);
};

/**
 * Yeni bitkileri getirir
 */
export const getNewPlants = (plants) => {
  return plants.filter(plant => plant.isNew);
};

/**
 * Rastgele bitki önerileri getirir
 */
export const getRandomPlants = (plants, count = 5) => {
  const shuffled = [...plants].sort(() => 0.5 - Math.random());
  return shuffled.slice(0, count);
};
