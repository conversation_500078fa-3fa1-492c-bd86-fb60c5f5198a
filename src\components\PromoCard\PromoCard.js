import React from 'react';
import { View, Text, TouchableOpacity, Image } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { COLORS } from '../../utils/constants';
import styles from './PromoCard.styles.js';

const PromoCard = ({ onPress, style, ...props }) => {
  return (
    <View style={[styles.container, style]} {...props}>
      <LinearGradient
        colors={['#4CAF50', '#66BB6A', '#81C784']}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={styles.gradient}
      >
        {/* İçerik */}
        <View style={styles.content}>
          <View style={styles.textContainer}>
            <Text style={styles.subtitle}>Özel İndirim Fırsatı</Text>
            <Text style={styles.title}>%40'a Varan</Text>
            <Text style={styles.title}>İndirim</Text>
            
            {/* Buton */}
            <TouchableOpacity 
              style={styles.button}
              onPress={onPress}
              activeOpacity={0.8}
            >
              <Text style={styles.buttonText}>Keşfet</Text>
              <Ionicons name="arrow-forward" size={16} color={COLORS.primary} />
            </TouchableOpacity>
          </View>

          {/* Görsel */}
          <View style={styles.imageContainer}>
            <Image
              source={{ 
                uri: 'https://images.unsplash.com/photo-1590779033100-9f60a05a013d?w=400&h=400&fit=crop&crop=center'
              }}
              style={styles.image}
              resizeMode="contain"
            />
          </View>
        </View>

        {/* Dekoratif Elementler */}
        <View style={styles.decorativeCircle1} />
        <View style={styles.decorativeCircle2} />
        <View style={styles.decorativeCircle3} />
      </LinearGradient>
    </View>
  );
};

export default PromoCard;
