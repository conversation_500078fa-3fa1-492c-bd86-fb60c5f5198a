import { 
  collection, 
  doc, 
  getDocs, 
  getDoc, 
  addDoc, 
  updateDoc, 
  deleteDoc, 
  query, 
  where, 
  orderBy, 
  limit, 
  startAfter,
  increment,
  serverTimestamp,
  arrayUnion,
  arrayRemove
} from 'firebase/firestore';
import { db } from '../config/firebase';
import { COLLECTIONS, ARTICLE_CATEGORIES } from '../database/schema';

class ArticlesService {
  constructor() {
    this.collectionRef = collection(db, COLLECTIONS.ARTICLES);
  }

  // Get all published articles with optional filters
  async getArticles(filters = {}) {
    try {
      let q = query(
        this.collectionRef,
        where('isPublished', '==', true)
      );

      // Apply filters
      if (filters.category && filters.category !== 'Tümü') {
        q = query(q, where('category', '==', filters.category));
      }

      if (filters.isFeatured) {
        q = query(q, where('isFeatured', '==', true));
      }

      // Add ordering
      q = query(q, orderBy('publishedAt', 'desc'));

      // Add pagination if specified
      if (filters.limitCount) {
        q = query(q, limit(filters.limitCount));
      }

      if (filters.lastDoc) {
        q = query(q, startAfter(filters.lastDoc));
      }

      const querySnapshot = await getDocs(q);
      const articles = [];
      
      querySnapshot.forEach((doc) => {
        articles.push({
          id: doc.id,
          ...doc.data()
        });
      });

      return {
        articles,
        lastDoc: querySnapshot.docs[querySnapshot.docs.length - 1],
        hasMore: querySnapshot.docs.length === (filters.limitCount || 20)
      };
    } catch (error) {
      console.error('Error getting articles:', error);
      throw error;
    }
  }

  // Get a single article by ID
  async getArticleById(articleId) {
    try {
      const docRef = doc(db, COLLECTIONS.ARTICLES, articleId);
      const docSnap = await getDoc(docRef);

      if (docSnap.exists()) {
        const data = docSnap.data();
        
        // Only return if published
        if (data.isPublished) {
          // Increment view count
          await this.incrementViews(articleId);
          
          return {
            id: docSnap.id,
            ...data
          };
        } else {
          throw new Error('Article not published');
        }
      } else {
        throw new Error('Article not found');
      }
    } catch (error) {
      console.error('Error getting article:', error);
      throw error;
    }
  }

  // Search articles by title or content
  async searchArticles(searchTerm) {
    try {
      // Note: Firestore doesn't support full-text search natively
      // This is a basic implementation. For production, consider using Algolia or similar
      const q = query(
        this.collectionRef,
        where('isPublished', '==', true),
        orderBy('publishedAt', 'desc'),
        limit(50)
      );

      const querySnapshot = await getDocs(q);
      const articles = [];
      
      querySnapshot.forEach((doc) => {
        const data = doc.data();
        const title = data.title.toLowerCase();
        const content = data.content.toLowerCase();
        const summary = data.summary?.toLowerCase() || '';
        const search = searchTerm.toLowerCase();

        if (title.includes(search) || content.includes(search) || summary.includes(search)) {
          articles.push({
            id: doc.id,
            ...data
          });
        }
      });

      return articles;
    } catch (error) {
      console.error('Error searching articles:', error);
      throw error;
    }
  }

  // Get featured articles
  async getFeaturedArticles(limitCount = 5) {
    try {
      const q = query(
        this.collectionRef,
        where('isPublished', '==', true),
        where('isFeatured', '==', true),
        orderBy('publishedAt', 'desc'),
        limit(limitCount)
      );

      const querySnapshot = await getDocs(q);
      const articles = [];
      
      querySnapshot.forEach((doc) => {
        articles.push({
          id: doc.id,
          ...doc.data()
        });
      });

      return articles;
    } catch (error) {
      console.error('Error getting featured articles:', error);
      throw error;
    }
  }

  // Get articles by category
  async getArticlesByCategory(category, limitCount = 20) {
    try {
      const q = query(
        this.collectionRef,
        where('isPublished', '==', true),
        where('category', '==', category),
        orderBy('publishedAt', 'desc'),
        limit(limitCount)
      );

      const querySnapshot = await getDocs(q);
      const articles = [];
      
      querySnapshot.forEach((doc) => {
        articles.push({
          id: doc.id,
          ...doc.data()
        });
      });

      return articles;
    } catch (error) {
      console.error('Error getting articles by category:', error);
      throw error;
    }
  }

  // Get recent articles
  async getRecentArticles(limitCount = 10) {
    try {
      const q = query(
        this.collectionRef,
        where('isPublished', '==', true),
        orderBy('publishedAt', 'desc'),
        limit(limitCount)
      );

      const querySnapshot = await getDocs(q);
      const articles = [];
      
      querySnapshot.forEach((doc) => {
        articles.push({
          id: doc.id,
          ...doc.data()
        });
      });

      return articles;
    } catch (error) {
      console.error('Error getting recent articles:', error);
      throw error;
    }
  }

  // Add a new article (admin only)
  async addArticle(articleData) {
    try {
      const docRef = await addDoc(this.collectionRef, {
        ...articleData,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp(),
        publishedAt: articleData.isPublished ? serverTimestamp() : null,
        views: 0,
        likes: 0,
        comments: []
      });

      return docRef.id;
    } catch (error) {
      console.error('Error adding article:', error);
      throw error;
    }
  }

  // Update an article (admin only)
  async updateArticle(articleId, articleData) {
    try {
      const docRef = doc(db, COLLECTIONS.ARTICLES, articleId);
      const updateData = {
        ...articleData,
        updatedAt: serverTimestamp()
      };

      // Set publishedAt if publishing for the first time
      if (articleData.isPublished) {
        const currentDoc = await getDoc(docRef);
        const currentData = currentDoc.data();
        
        if (!currentData.publishedAt) {
          updateData.publishedAt = serverTimestamp();
        }
      }

      await updateDoc(docRef, updateData);
      return true;
    } catch (error) {
      console.error('Error updating article:', error);
      throw error;
    }
  }

  // Delete an article (admin only)
  async deleteArticle(articleId) {
    try {
      const docRef = doc(db, COLLECTIONS.ARTICLES, articleId);
      await deleteDoc(docRef);
      return true;
    } catch (error) {
      console.error('Error deleting article:', error);
      throw error;
    }
  }

  // Increment view count
  async incrementViews(articleId) {
    try {
      const docRef = doc(db, COLLECTIONS.ARTICLES, articleId);
      await updateDoc(docRef, {
        views: increment(1)
      });
    } catch (error) {
      console.error('Error incrementing views:', error);
      // Don't throw error for view counting
    }
  }

  // Like/unlike an article
  async toggleLike(articleId, isLiked) {
    try {
      const docRef = doc(db, COLLECTIONS.ARTICLES, articleId);
      await updateDoc(docRef, {
        likes: increment(isLiked ? 1 : -1)
      });
    } catch (error) {
      console.error('Error toggling like:', error);
      throw error;
    }
  }

  // Add comment to article
  async addComment(articleId, comment) {
    try {
      const docRef = doc(db, COLLECTIONS.ARTICLES, articleId);
      const commentData = {
        id: Date.now().toString(),
        ...comment,
        createdAt: serverTimestamp(),
        likes: 0
      };

      await updateDoc(docRef, {
        comments: arrayUnion(commentData)
      });

      return commentData.id;
    } catch (error) {
      console.error('Error adding comment:', error);
      throw error;
    }
  }

  // Remove comment from article
  async removeComment(articleId, commentId) {
    try {
      const docRef = doc(db, COLLECTIONS.ARTICLES, articleId);
      const docSnap = await getDoc(docRef);
      
      if (docSnap.exists()) {
        const data = docSnap.data();
        const comments = data.comments || [];
        const updatedComments = comments.filter(comment => comment.id !== commentId);
        
        await updateDoc(docRef, {
          comments: updatedComments
        });
      }
    } catch (error) {
      console.error('Error removing comment:', error);
      throw error;
    }
  }

  // Get article statistics
  async getArticleStats() {
    try {
      const querySnapshot = await getDocs(this.collectionRef);
      const stats = {
        total: 0,
        published: 0,
        draft: 0,
        featured: 0,
        byCategory: {},
        totalViews: 0,
        totalLikes: 0
      };

      querySnapshot.forEach((doc) => {
        const data = doc.data();
        stats.total++;

        if (data.isPublished) stats.published++;
        else stats.draft++;

        if (data.isFeatured) stats.featured++;

        // Count by category
        if (data.category) {
          stats.byCategory[data.category] = (stats.byCategory[data.category] || 0) + 1;
        }

        // Sum views and likes
        stats.totalViews += data.views || 0;
        stats.totalLikes += data.likes || 0;
      });

      return stats;
    } catch (error) {
      console.error('Error getting article stats:', error);
      throw error;
    }
  }
}

export default new ArticlesService();
