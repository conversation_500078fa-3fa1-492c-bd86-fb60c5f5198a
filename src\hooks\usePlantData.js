import { useState, useEffect } from 'react';
import PlantDataService from '../services/plantDataService';
import apiService from '../services/apiService';

export const usePlantData = () => {
  const [plants, setPlants] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [refreshing, setRefreshing] = useState(false);

  const loadPlants = async () => {
    try {
      setError(null);
      // Use new API service with caching
      const plants = await apiService.getPlants();
      console.log('📊 Firebase\'den yüklenen bitki sayısı:', plants.length);
      setPlants(plants);
    } catch (err) {
      setError('Bitkiler yüklenirken hata oluştu');
      console.error('Plant loading error:', err);
      // Fallback to constants if Firebase fails
      const { SAMPLE_PLANTS } = require('../utils/constants');
      setPlants(SAMPLE_PLANTS);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const refreshPlants = async () => {
    setRefreshing(true);
    // Clear cache and reload
    await apiService.clearCache();
    await loadPlants();
  };

  useEffect(() => {
    loadPlants();
  }, []);

  return {
    plants,
    loading,
    error,
    refreshing,
    refreshPlants,
    reloadPlants: loadPlants
  };
};

export const usePlantDetail = (plantId) => {
  const [plant, setPlant] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  const loadPlantDetail = async () => {
    if (!plantId) {
      setLoading(false);
      return;
    }

    try {
      setError(null);
      const plant = await apiService.getPlant(plantId);
      setPlant(plant);
    } catch (err) {
      setError('Bitki detayı yüklenirken hata oluştu');
      console.error('Plant detail loading error:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadPlantDetail();
  }, [plantId]);

  return {
    plant,
    loading,
    error,
    reloadPlant: loadPlantDetail
  };
};

export const useSearchPlants = () => {
  const [searchResults, setSearchResults] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const searchPlants = async (query) => {
    if (!query.trim()) {
      setSearchResults([]);
      return;
    }

    try {
      setLoading(true);
      setError(null);

      const results = await apiService.searchPlants(query);
      setSearchResults(results);
    } catch (err) {
      setError('Arama sırasında hata oluştu');
      setSearchResults([]);
      console.error('Search error:', err);
    } finally {
      setLoading(false);
    }
  };

  const clearSearch = () => {
    setSearchResults([]);
    setError(null);
  };

  return {
    searchResults,
    loading,
    error,
    searchPlants,
    clearSearch
  };
};


