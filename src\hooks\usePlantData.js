import { useState, useEffect } from 'react';
import PlantDataService from '../services/plantDataService';

export const usePlantData = () => {
  const [plants, setPlants] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [refreshing, setRefreshing] = useState(false);

  const loadPlants = async () => {
    try {
      setError(null);
      const response = await PlantDataService.getAllPlants();
      
      if (response.success) {
        setPlants(response.data);
      } else {
        setError(response.error || 'Bitkiler yüklenirken hata oluştu');
      }
    } catch (err) {
      setError('Bitkiler yüklenirken hata oluştu');
      console.error('Plant loading error:', err);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const refreshPlants = async () => {
    setRefreshing(true);
    PlantDataService.clearCacheKey('all_plants');
    await loadPlants();
  };

  useEffect(() => {
    loadPlants();
  }, []);

  return {
    plants,
    loading,
    error,
    refreshing,
    refreshPlants,
    reloadPlants: loadPlants
  };
};

export const usePlantDetail = (plantId) => {
  const [plant, setPlant] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  const loadPlantDetail = async () => {
    if (!plantId) {
      setLoading(false);
      return;
    }

    try {
      setError(null);
      const response = await PlantDataService.getPlantDetail(plantId);
      
      if (response.success) {
        setPlant(response.data);
      } else {
        setError(response.error || 'Bitki detayı yüklenirken hata oluştu');
      }
    } catch (err) {
      setError('Bitki detayı yüklenirken hata oluştu');
      console.error('Plant detail loading error:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadPlantDetail();
  }, [plantId]);

  return {
    plant,
    loading,
    error,
    reloadPlant: loadPlantDetail
  };
};

export const useSearchPlants = () => {
  const [searchResults, setSearchResults] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const searchPlants = async (query) => {
    if (!query.trim()) {
      setSearchResults([]);
      return;
    }

    try {
      setLoading(true);
      setError(null);
      
      const response = await PlantDataService.searchPlants(query);
      
      if (response.success) {
        setSearchResults(response.data);
      } else {
        setError(response.error || 'Arama sırasında hata oluştu');
        setSearchResults([]);
      }
    } catch (err) {
      setError('Arama sırasında hata oluştu');
      setSearchResults([]);
      console.error('Search error:', err);
    } finally {
      setLoading(false);
    }
  };

  const clearSearch = () => {
    setSearchResults([]);
    setError(null);
  };

  return {
    searchResults,
    loading,
    error,
    searchPlants,
    clearSearch
  };
};


