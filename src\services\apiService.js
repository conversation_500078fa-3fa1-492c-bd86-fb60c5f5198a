import { 
  collection, 
  doc, 
  getDocs, 
  getDoc, 
  addDoc, 
  updateDoc, 
  deleteDoc, 
  query, 
  where, 
  orderBy, 
  limit,
  startAfter,
  writeBatch
} from 'firebase/firestore';
import { db } from '../config/firebase';
import cacheService from './cacheService';

class ApiService {
  constructor() {
    this.collections = {
      PLANTS: 'plants',
      PLANT_STAGES: 'plant_stages',
      PLANT_TREATMENTS: 'plant_treatments',
      ARTICLES: 'articles',
      CATEGORIES: 'categories',
      USERS: 'users'
    };
    
    this.cacheTTL = {
      SHORT: 2 * 60 * 1000,      // 2 minutes
      MEDIUM: 10 * 60 * 1000,    // 10 minutes
      LONG: 30 * 60 * 1000,      // 30 minutes
      VERY_LONG: 60 * 60 * 1000  // 1 hour
    };
  }

  // Generic CRUD Operations with Caching

  // GET Operations
  async getCollection(collectionName, options = {}) {
    const { 
      useCache = true, 
      ttl = this.cacheTTL.MEDIUM,
      filters = [],
      orderByField = null,
      orderDirection = 'asc',
      limitCount = null
    } = options;

    const cacheKey = cacheService.generateKey(`${collectionName}_list`, {
      filters: JSON.stringify(filters),
      orderBy: orderByField,
      order: orderDirection,
      limit: limitCount
    });

    if (useCache) {
      const cached = await cacheService.get(cacheKey);
      if (cached) return cached;
    }

    try {
      let q = collection(db, collectionName);

      // Apply filters
      filters.forEach(filter => {
        q = query(q, where(filter.field, filter.operator, filter.value));
      });

      // Apply ordering
      if (orderByField) {
        q = query(q, orderBy(orderByField, orderDirection));
      }

      // Apply limit
      if (limitCount) {
        q = query(q, limit(limitCount));
      }

      const snapshot = await getDocs(q);
      const data = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));

      if (useCache) {
        await cacheService.set(cacheKey, data, ttl);
      }

      return data;
    } catch (error) {
      console.error(`Error fetching ${collectionName}:`, error);
      throw error;
    }
  }

  async getDocument(collectionName, docId, useCache = true, ttl = this.cacheTTL.MEDIUM) {
    const cacheKey = cacheService.generateKey(`${collectionName}_doc`, { id: docId });

    if (useCache) {
      const cached = await cacheService.get(cacheKey);
      if (cached) return cached;
    }

    try {
      const docRef = doc(db, collectionName, docId);
      const docSnap = await getDoc(docRef);

      if (docSnap.exists()) {
        const data = { id: docSnap.id, ...docSnap.data() };
        
        if (useCache) {
          await cacheService.set(cacheKey, data, ttl);
        }
        
        return data;
      } else {
        return null;
      }
    } catch (error) {
      console.error(`Error fetching document ${docId} from ${collectionName}:`, error);
      throw error;
    }
  }

  // POST Operations
  async createDocument(collectionName, data) {
    try {
      const docRef = await addDoc(collection(db, collectionName), {
        ...data,
        createdAt: new Date(),
        updatedAt: new Date()
      });

      // Invalidate related cache
      await cacheService.invalidatePattern(collectionName);

      return { id: docRef.id, ...data };
    } catch (error) {
      console.error(`Error creating document in ${collectionName}:`, error);
      throw error;
    }
  }

  // PUT Operations
  async updateDocument(collectionName, docId, data) {
    try {
      const docRef = doc(db, collectionName, docId);
      await updateDoc(docRef, {
        ...data,
        updatedAt: new Date()
      });

      // Invalidate related cache
      await cacheService.invalidatePattern(collectionName);
      await cacheService.remove(cacheService.generateKey(`${collectionName}_doc`, { id: docId }));

      return { id: docId, ...data };
    } catch (error) {
      console.error(`Error updating document ${docId} in ${collectionName}:`, error);
      throw error;
    }
  }

  // DELETE Operations
  async deleteDocument(collectionName, docId) {
    try {
      const docRef = doc(db, collectionName, docId);
      await deleteDoc(docRef);

      // Invalidate related cache
      await cacheService.invalidatePattern(collectionName);
      await cacheService.remove(cacheService.generateKey(`${collectionName}_doc`, { id: docId }));

      return { success: true, id: docId };
    } catch (error) {
      console.error(`Error deleting document ${docId} from ${collectionName}:`, error);
      throw error;
    }
  }

  // Batch Operations
  async batchWrite(operations) {
    try {
      const batch = writeBatch(db);

      operations.forEach(operation => {
        const { type, collectionName, docId, data } = operation;
        const docRef = docId ? doc(db, collectionName, docId) : doc(collection(db, collectionName));

        switch (type) {
          case 'create':
            batch.set(docRef, { ...data, createdAt: new Date(), updatedAt: new Date() });
            break;
          case 'update':
            batch.update(docRef, { ...data, updatedAt: new Date() });
            break;
          case 'delete':
            batch.delete(docRef);
            break;
        }
      });

      await batch.commit();

      // Invalidate all related cache
      const affectedCollections = [...new Set(operations.map(op => op.collectionName))];
      for (const collectionName of affectedCollections) {
        await cacheService.invalidatePattern(collectionName);
      }

      return { success: true, operationsCount: operations.length };
    } catch (error) {
      console.error('Batch write error:', error);
      throw error;
    }
  }

  // Specialized Plant API Methods
  async getPlants(options = {}) {
    return this.getCollection(this.collections.PLANTS, {
      ...options,
      ttl: this.cacheTTL.LONG
    });
  }

  async getPlant(plantId) {
    return this.getDocument(this.collections.PLANTS, plantId, true, this.cacheTTL.LONG);
  }

  async getPlantsByCategory(category) {
    return this.getCollection(this.collections.PLANTS, {
      filters: [{ field: 'category', operator: '==', value: category }],
      ttl: this.cacheTTL.LONG
    });
  }

  async getPopularPlants() {
    return this.getCollection(this.collections.PLANTS, {
      filters: [{ field: 'isPopular', operator: '==', value: true }],
      orderByField: 'name',
      ttl: this.cacheTTL.VERY_LONG
    });
  }

  async getNewPlants() {
    return this.getCollection(this.collections.PLANTS, {
      filters: [{ field: 'isNew', operator: '==', value: true }],
      orderByField: 'createdAt',
      orderDirection: 'desc',
      limitCount: 10,
      ttl: this.cacheTTL.MEDIUM
    });
  }

  // Plant Stages API
  async getPlantStages(plantId) {
    return this.getCollection(this.collections.PLANT_STAGES, {
      filters: [{ field: 'plantId', operator: '==', value: plantId }],
      orderByField: 'stageOrder',
      ttl: this.cacheTTL.LONG
    });
  }

  // Plant Treatments API
  async getPlantTreatments(plantId) {
    return this.getCollection(this.collections.PLANT_TREATMENTS, {
      filters: [{ field: 'plantId', operator: '==', value: plantId }],
      ttl: this.cacheTTL.LONG
    });
  }

  // Articles API
  async getArticles(options = {}) {
    return this.getCollection(this.collections.ARTICLES, {
      ...options,
      ttl: this.cacheTTL.MEDIUM
    });
  }

  async getArticle(articleId) {
    return this.getDocument(this.collections.ARTICLES, articleId, true, this.cacheTTL.MEDIUM);
  }

  async getArticlesByCategory(category) {
    return this.getCollection(this.collections.ARTICLES, {
      filters: [{ field: 'category', operator: '==', value: category }],
      ttl: this.cacheTTL.MEDIUM
    });
  }

  // Categories API
  async getCategories() {
    return this.getCollection(this.collections.CATEGORIES, {
      orderByField: 'name',
      ttl: this.cacheTTL.VERY_LONG
    });
  }

  // Search API
  async searchPlants(searchTerm) {
    const cacheKey = cacheService.generateKey('search_plants', { term: searchTerm.toLowerCase() });
    
    return cacheService.cacheOrExecute(cacheKey, async () => {
      // Note: Firestore doesn't support full-text search natively
      // This is a simple implementation - consider using Algolia or similar for production
      const plants = await this.getPlants({ useCache: false });
      
      return plants.filter(plant => 
        plant.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        plant.latinName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        plant.description.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }, this.cacheTTL.SHORT);
  }

  // Data Migration API
  async migratePlants(plantsData) {
    try {
      const operations = plantsData.map(plant => ({
        type: 'create',
        collectionName: this.collections.PLANTS,
        data: plant
      }));

      const result = await this.batchWrite(operations);
      console.log(`✅ Migrated ${plantsData.length} plants`);
      return result;
    } catch (error) {
      console.error('Plant migration error:', error);
      throw error;
    }
  }

  async migrateArticles(articlesData) {
    try {
      const operations = articlesData.map(article => ({
        type: 'create',
        collectionName: this.collections.ARTICLES,
        data: article
      }));

      const result = await this.batchWrite(operations);
      console.log(`✅ Migrated ${articlesData.length} articles`);
      return result;
    } catch (error) {
      console.error('Article migration error:', error);
      throw error;
    }
  }

  async migrateCategories(categoriesData) {
    try {
      const operations = categoriesData.map(category => ({
        type: 'create',
        collectionName: this.collections.CATEGORIES,
        data: category
      }));

      const result = await this.batchWrite(operations);
      console.log(`✅ Migrated ${categoriesData.length} categories`);
      return result;
    } catch (error) {
      console.error('Category migration error:', error);
      throw error;
    }
  }

  // Cache Management
  async clearCache() {
    await cacheService.clear();
  }

  async getCacheInfo() {
    return cacheService.getCacheInfo();
  }

  async preloadData() {
    const preloadKeys = [
      {
        key: 'plants_list_popular',
        asyncFunction: () => this.getPopularPlants(),
        ttl: this.cacheTTL.VERY_LONG
      },
      {
        key: 'plants_list_new',
        asyncFunction: () => this.getNewPlants(),
        ttl: this.cacheTTL.MEDIUM
      },
      {
        key: 'categories_list',
        asyncFunction: () => this.getCategories(),
        ttl: this.cacheTTL.VERY_LONG
      }
    ];

    await cacheService.preloadCache(preloadKeys);
  }
}

export default new ApiService();
