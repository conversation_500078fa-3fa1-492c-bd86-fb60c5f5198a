import AsyncStorage from '@react-native-async-storage/async-storage';

class CacheService {
  constructor() {
    this.memoryCache = new Map();
    this.cacheExpiry = new Map();
    this.defaultTTL = 5 * 60 * 1000; // 5 minutes default TTL
  }

  // Generate cache key
  generateKey(prefix, params = {}) {
    const paramString = Object.keys(params)
      .sort()
      .map(key => `${key}:${params[key]}`)
      .join('|');
    return paramString ? `${prefix}_${paramString}` : prefix;
  }

  // Set cache with TTL
  async set(key, data, ttl = this.defaultTTL) {
    try {
      const cacheData = {
        data,
        timestamp: Date.now(),
        ttl
      };

      // Memory cache
      this.memoryCache.set(key, cacheData);
      this.cacheExpiry.set(key, Date.now() + ttl);

      // Persistent cache
      await AsyncStorage.setItem(key, JSON.stringify(cacheData));
      
      console.log(`✅ Cache set: ${key}`);
    } catch (error) {
      console.error('Cache set error:', error);
    }
  }

  // Get from cache
  async get(key) {
    try {
      // Check memory cache first
      if (this.memoryCache.has(key)) {
        const expiry = this.cacheExpiry.get(key);
        if (Date.now() < expiry) {
          console.log(`🎯 Memory cache hit: ${key}`);
          return this.memoryCache.get(key).data;
        } else {
          // Expired, remove from memory
          this.memoryCache.delete(key);
          this.cacheExpiry.delete(key);
        }
      }

      // Check persistent cache
      const cached = await AsyncStorage.getItem(key);
      if (cached) {
        const cacheData = JSON.parse(cached);
        const isExpired = Date.now() - cacheData.timestamp > cacheData.ttl;
        
        if (!isExpired) {
          // Restore to memory cache
          this.memoryCache.set(key, cacheData);
          this.cacheExpiry.set(key, cacheData.timestamp + cacheData.ttl);
          
          console.log(`💾 Persistent cache hit: ${key}`);
          return cacheData.data;
        } else {
          // Expired, remove from storage
          await AsyncStorage.removeItem(key);
        }
      }

      console.log(`❌ Cache miss: ${key}`);
      return null;
    } catch (error) {
      console.error('Cache get error:', error);
      return null;
    }
  }

  // Remove specific cache
  async remove(key) {
    try {
      this.memoryCache.delete(key);
      this.cacheExpiry.delete(key);
      await AsyncStorage.removeItem(key);
      console.log(`🗑️ Cache removed: ${key}`);
    } catch (error) {
      console.error('Cache remove error:', error);
    }
  }

  // Clear all cache
  async clear() {
    try {
      this.memoryCache.clear();
      this.cacheExpiry.clear();
      
      // Get all keys and remove cache keys
      const keys = await AsyncStorage.getAllKeys();
      const cacheKeys = keys.filter(key => 
        key.startsWith('plants_') || 
        key.startsWith('articles_') || 
        key.startsWith('categories_') ||
        key.startsWith('stages_') ||
        key.startsWith('treatments_')
      );
      
      if (cacheKeys.length > 0) {
        await AsyncStorage.multiRemove(cacheKeys);
      }
      
      console.log('🧹 All cache cleared');
    } catch (error) {
      console.error('Cache clear error:', error);
    }
  }

  // Get cache info
  async getCacheInfo() {
    try {
      const keys = await AsyncStorage.getAllKeys();
      const cacheKeys = keys.filter(key => 
        key.startsWith('plants_') || 
        key.startsWith('articles_') || 
        key.startsWith('categories_') ||
        key.startsWith('stages_') ||
        key.startsWith('treatments_')
      );

      const info = {
        memoryCache: this.memoryCache.size,
        persistentCache: cacheKeys.length,
        totalKeys: cacheKeys,
        memoryKeys: Array.from(this.memoryCache.keys())
      };

      return info;
    } catch (error) {
      console.error('Cache info error:', error);
      return { memoryCache: 0, persistentCache: 0, totalKeys: [], memoryKeys: [] };
    }
  }

  // Cache with function execution
  async cacheOrExecute(key, asyncFunction, ttl = this.defaultTTL) {
    // Try to get from cache first
    const cached = await this.get(key);
    if (cached !== null) {
      return cached;
    }

    // Execute function and cache result
    try {
      console.log(`🔄 Executing and caching: ${key}`);
      const result = await asyncFunction();
      await this.set(key, result, ttl);
      return result;
    } catch (error) {
      console.error(`Function execution error for ${key}:`, error);
      throw error;
    }
  }

  // Preload cache
  async preloadCache(keys) {
    console.log('🚀 Preloading cache...');
    const promises = keys.map(async ({ key, asyncFunction, ttl }) => {
      try {
        const cached = await this.get(key);
        if (cached === null) {
          const result = await asyncFunction();
          await this.set(key, result, ttl || this.defaultTTL);
          console.log(`✅ Preloaded: ${key}`);
        } else {
          console.log(`⚡ Already cached: ${key}`);
        }
      } catch (error) {
        console.error(`❌ Preload failed for ${key}:`, error);
      }
    });

    await Promise.allSettled(promises);
    console.log('🎉 Cache preload completed');
  }

  // Cache invalidation patterns
  async invalidatePattern(pattern) {
    try {
      // Memory cache
      const memoryKeys = Array.from(this.memoryCache.keys());
      memoryKeys.forEach(key => {
        if (key.includes(pattern)) {
          this.memoryCache.delete(key);
          this.cacheExpiry.delete(key);
        }
      });

      // Persistent cache
      const keys = await AsyncStorage.getAllKeys();
      const matchingKeys = keys.filter(key => key.includes(pattern));
      
      if (matchingKeys.length > 0) {
        await AsyncStorage.multiRemove(matchingKeys);
      }

      console.log(`🔄 Invalidated pattern: ${pattern} (${matchingKeys.length} keys)`);
    } catch (error) {
      console.error('Cache invalidation error:', error);
    }
  }

  // Smart cache refresh
  async refreshCache(key, asyncFunction, ttl = this.defaultTTL) {
    try {
      console.log(`🔄 Refreshing cache: ${key}`);
      const result = await asyncFunction();
      await this.set(key, result, ttl);
      return result;
    } catch (error) {
      console.error(`Cache refresh error for ${key}:`, error);
      // Return cached data if refresh fails
      const cached = await this.get(key);
      if (cached !== null) {
        console.log(`⚠️ Using stale cache for ${key}`);
        return cached;
      }
      throw error;
    }
  }
}

export default new CacheService();
