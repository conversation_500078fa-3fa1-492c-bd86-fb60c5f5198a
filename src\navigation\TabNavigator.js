import React from 'react';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { Ionicons } from '@expo/vector-icons';

import HomePage from '../pages/HomePage';
import FavoritesPage from '../pages/FavoritesPage/FavoritesPage';
import { COLORS, FONT_SIZES } from '../utils/constants';

const Tab = createBottomTabNavigator();

const TabNavigator = () => {
  return (
    <Tab.Navigator
      screenOptions={({ route }) => ({
        tabBarIcon: ({ focused, color, size }) => {
          let iconName;

          if (route.name === 'Home') {
            iconName = focused ? 'leaf' : 'leaf-outline';
          } else if (route.name === 'Favorites') {
            iconName = focused ? 'heart' : 'heart-outline';
          }

          return <Ionicons name={iconName} size={size} color={color} />;
        },
        tabBarActiveTintColor: COLORS.accent,
        tabBarInactiveTintColor: COLORS.darkGray,
        tabBarStyle: {
          backgroundColor: COLORS.white,
          borderTopWidth: 0,
          paddingBottom: 45, // Much more padding for home indicator
          paddingTop: 15,
          height: 110, // Much more height
          shadowColor: COLORS.shadow,
          shadowOffset: {
            width: 0,
            height: -6,
          },
          shadowOpacity: 0.25,
          shadowRadius: 16,
          elevation: 20,
          borderTopLeftRadius: 30,
          borderTopRightRadius: 30,
          position: 'absolute', // Make it float above content
          bottom: 0, // Stick to bottom
        },
        tabBarLabelStyle: {
          fontSize: FONT_SIZES.small,
          fontWeight: '500',
        },
        headerStyle: {
          backgroundColor: COLORS.white,
          shadowColor: COLORS.shadow,
          shadowOffset: {
            width: 0,
            height: 2,
          },
          shadowOpacity: 0.1,
          shadowRadius: 4,
          elevation: 3,
        },
        headerTitleStyle: {
          fontSize: FONT_SIZES.large,
          fontWeight: '600',
          color: COLORS.primary,
        },
        headerTintColor: COLORS.primary,
      })}
    >
      <Tab.Screen
        name="Home"
        component={HomePage}
        options={{
          title: 'Keşfet',
          headerShown: false, // HomePage kendi header'ını yönetir
        }}
      />
      <Tab.Screen
        name="Favorites"
        component={FavoritesPage}
        options={{
          title: 'Favoriler',
          headerShown: false, // FavoritesPage kendi header'ını yönetir
        }}
      />

    </Tab.Navigator>
  );
};

export default TabNavigator;
