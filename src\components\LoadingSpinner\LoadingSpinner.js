import React, { useEffect, useRef } from 'react';
import {
  View,
  Text,
  Animated,
  Easing,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { COLORS } from '../../utils/constants';
import styles from './LoadingSpinner.styles';

const LoadingSpinner = ({
  size = 'medium',
  color = COLORS.secondary,
  text = 'Veriler yükleniyor...',
  showText = true
}) => {
  const pulseValue = useRef(new Animated.Value(0.8)).current;
  const fadeValue = useRef(new Animated.Value(0.3)).current;

  const sizeMap = {
    small: 24,
    medium: 40,
    large: 60,
  };

  const iconSize = sizeMap[size] || sizeMap.medium;

  useEffect(() => {
    // Pulse animasyonu
    const pulseAnimation = Animated.loop(
      Animated.sequence([
        Animated.timing(pulseValue, {
          toValue: 1.2,
          duration: 1000,
          easing: Easing.inOut(Easing.ease),
          useNativeDriver: true,
        }),
        Animated.timing(pulseValue, {
          toValue: 0.8,
          duration: 1000,
          easing: Easing.inOut(Easing.ease),
          useNativeDriver: true,
        }),
      ])
    );

    // Fade animasyonu
    const fadeAnimation = Animated.loop(
      Animated.sequence([
        Animated.timing(fadeValue, {
          toValue: 1,
          duration: 1500,
          easing: Easing.inOut(Easing.ease),
          useNativeDriver: true,
        }),
        Animated.timing(fadeValue, {
          toValue: 0.3,
          duration: 1500,
          easing: Easing.inOut(Easing.ease),
          useNativeDriver: true,
        }),
      ])
    );

    // Animasyonları başlat
    pulseAnimation.start();
    fadeAnimation.start();

    return () => {
      pulseAnimation.stop();
      fadeAnimation.stop();
    };
  }, [pulseValue, fadeValue]);

  return (
    <View style={styles.container}>
      {/* Yaprak İkonu */}
      <Animated.View
        style={[
          styles.leafContainer,
          {
            transform: [{ scale: pulseValue }],
            opacity: fadeValue,
          },
        ]}
      >
        <Ionicons
          name="leaf"
          size={iconSize}
          color={color}
        />
      </Animated.View>

      {/* Loading Text */}
      {showText && (
        <Text style={styles.loadingText}>
          {text}
        </Text>
      )}
    </View>
  );
};

export default LoadingSpinner;
