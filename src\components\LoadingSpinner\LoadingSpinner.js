import React, { useEffect, useRef } from 'react';
import {
  View,
  Text,
  Animated,
  Easing,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { COLORS } from '../../utils/constants';
import styles from './LoadingSpinner.styles';

const LoadingSpinner = ({ 
  size = 'medium', 
  color = COLORS.primary, 
  text = 'Yükleniyor...', 
  showText = true 
}) => {
  const spinValue = useRef(new Animated.Value(0)).current;
  const scaleValue = useRef(new Animated.Value(1)).current;
  const leaf1Rotation = useRef(new Animated.Value(0)).current;
  const leaf2Rotation = useRef(new Animated.Value(0)).current;
  const leaf3Rotation = useRef(new Animated.Value(0)).current;

  const sizeMap = {
    small: 24,
    medium: 40,
    large: 60,
  };

  const iconSize = sizeMap[size] || sizeMap.medium;

  useEffect(() => {
    // Ana spinner animasyonu
    const spinAnimation = Animated.loop(
      Animated.timing(spinValue, {
        toValue: 1,
        duration: 2000,
        easing: Easing.linear,
        useNativeDriver: true,
      })
    );

    // Pulse animasyonu
    const pulseAnimation = Animated.loop(
      Animated.sequence([
        Animated.timing(scaleValue, {
          toValue: 1.1,
          duration: 800,
          easing: Easing.inOut(Easing.ease),
          useNativeDriver: true,
        }),
        Animated.timing(scaleValue, {
          toValue: 1,
          duration: 800,
          easing: Easing.inOut(Easing.ease),
          useNativeDriver: true,
        }),
      ])
    );

    // Yaprak animasyonları
    const leaf1Animation = Animated.loop(
      Animated.timing(leaf1Rotation, {
        toValue: 1,
        duration: 3000,
        easing: Easing.linear,
        useNativeDriver: true,
      })
    );

    const leaf2Animation = Animated.loop(
      Animated.timing(leaf2Rotation, {
        toValue: 1,
        duration: 2500,
        easing: Easing.linear,
        useNativeDriver: true,
      })
    );

    const leaf3Animation = Animated.loop(
      Animated.timing(leaf3Rotation, {
        toValue: 1,
        duration: 3500,
        easing: Easing.linear,
        useNativeDriver: true,
      })
    );

    // Tüm animasyonları başlat
    spinAnimation.start();
    pulseAnimation.start();
    leaf1Animation.start();
    leaf2Animation.start();
    leaf3Animation.start();

    return () => {
      spinAnimation.stop();
      pulseAnimation.stop();
      leaf1Animation.stop();
      leaf2Animation.stop();
      leaf3Animation.stop();
    };
  }, [spinValue, scaleValue, leaf1Rotation, leaf2Rotation, leaf3Rotation]);

  const spin = spinValue.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '360deg'],
  });

  const leaf1Spin = leaf1Rotation.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '360deg'],
  });

  const leaf2Spin = leaf2Rotation.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '-360deg'],
  });

  const leaf3Spin = leaf3Rotation.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '360deg'],
  });

  return (
    <View style={[styles.container, { minHeight: iconSize + 60 }]}>
      {/* Ana Spinner */}
      <View style={styles.spinnerContainer}>
        <Animated.View
          style={[
            styles.spinner,
            {
              transform: [
                { rotate: spin },
                { scale: scaleValue },
              ],
            },
          ]}
        >
          <Ionicons 
            name="refresh" 
            size={iconSize} 
            color={color} 
          />
        </Animated.View>

        {/* Yaprak Dekorasyonları */}
        <Animated.View
          style={[
            styles.leaf,
            styles.leaf1,
            {
              transform: [{ rotate: leaf1Spin }],
            },
          ]}
        >
          <Ionicons 
            name="leaf" 
            size={iconSize * 0.4} 
            color={COLORS.secondary} 
          />
        </Animated.View>

        <Animated.View
          style={[
            styles.leaf,
            styles.leaf2,
            {
              transform: [{ rotate: leaf2Spin }],
            },
          ]}
        >
          <Ionicons 
            name="leaf" 
            size={iconSize * 0.3} 
            color={COLORS.accent} 
          />
        </Animated.View>

        <Animated.View
          style={[
            styles.leaf,
            styles.leaf3,
            {
              transform: [{ rotate: leaf3Spin }],
            },
          ]}
        >
          <Ionicons 
            name="leaf" 
            size={iconSize * 0.35} 
            color="#4CAF50" 
          />
        </Animated.View>
      </View>

      {/* Loading Text */}
      {showText && (
        <Text style={[styles.loadingText, { color }]}>
          {text}
        </Text>
      )}
    </View>
  );
};

export default LoadingSpinner;
