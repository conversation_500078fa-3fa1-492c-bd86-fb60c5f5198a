import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  Image,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  Animated,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

import StageIndicator from '../../components/StageIndicator/StageIndicator';
import LoadingSpinner from '../../components/common/LoadingSpinner/LoadingSpinner';
import { usePlantDetail } from '../../hooks/usePlantData';
import { useFavoriteToggle } from '../../hooks/useFavorites';
import { COLORS, PLANT_STAGES } from '../../utils/constants';
import styles from './PlantDetailPage.styles.js';

const PlantDetailPage = ({ route, navigation }) => {
  const { plantId, plant: initialPlant } = route.params;
  const [currentStage, setCurrentStage] = useState(1);
  const [imageOpacity] = useState(new Animated.Value(1));
  
  const { plant, loading, error } = usePlantDetail(plantId);
  const { toggleFavorite, isFavorite, loading: favoriteLoading } = useFavoriteToggle();

  // Use initial plant data if available, otherwise use loaded plant
  const displayPlant = plant || initialPlant;

  useEffect(() => {
    if (displayPlant) {
      navigation.setOptions({
        title: displayPlant.name,
        headerRight: () => (
          <TouchableOpacity
            style={styles.headerFavoriteButton}
            onPress={handleFavoritePress}
            disabled={favoriteLoading}
          >
            <Ionicons
              name={isFavorite(displayPlant.id) ? 'heart' : 'heart-outline'}
              size={24}
              color={isFavorite(displayPlant.id) ? COLORS.accent : COLORS.primary}
            />
          </TouchableOpacity>
        ),
      });
    }
  }, [displayPlant, isFavorite, favoriteLoading]);

  const handleFavoritePress = () => {
    if (displayPlant) {
      toggleFavorite(displayPlant.id);
    }
  };

  const handleStageChange = (stageId) => {
    if (stageId !== currentStage) {
      // Fade out current image
      Animated.timing(imageOpacity, {
        toValue: 0,
        duration: 150,
        useNativeDriver: true,
      }).start(() => {
        setCurrentStage(stageId);
        // Fade in new image
        Animated.timing(imageOpacity, {
          toValue: 1,
          duration: 150,
          useNativeDriver: true,
        }).start();
      });
    }
  };

  const getCurrentStageData = () => {
    if (!displayPlant || !displayPlant.stages) return null;
    return displayPlant.stages.find(stage => stage.stageId === currentStage);
  };

  const getCurrentStageInfo = () => {
    return PLANT_STAGES.find(stage => stage.id === currentStage);
  };

  if (loading && !displayPlant) {
    return (
      <SafeAreaView style={styles.container}>
        <LoadingSpinner text="Bitki detayı yükleniyor..." />
      </SafeAreaView>
    );
  }

  if (error && !displayPlant) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.errorContainer}>
          <Ionicons name="alert-circle" size={64} color={COLORS.accent} />
          <Text style={styles.errorTitle}>Hata Oluştu</Text>
          <Text style={styles.errorText}>{error}</Text>
          <TouchableOpacity
            style={styles.retryButton}
            onPress={() => navigation.goBack()}
          >
            <Text style={styles.retryButtonText}>Geri Dön</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  if (!displayPlant) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.errorContainer}>
          <Ionicons name="leaf" size={64} color={COLORS.gray} />
          <Text style={styles.errorTitle}>Bitki Bulunamadı</Text>
          <Text style={styles.errorText}>
            Aradığınız bitki bulunamadı veya kaldırılmış olabilir.
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  const currentStageData = getCurrentStageData();
  const currentStageInfo = getCurrentStageInfo();

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Stage Image */}
        <View style={styles.imageContainer}>
          <Animated.Image
            source={{ uri: currentStageData?.image || displayPlant.mainImage }}
            style={[styles.stageImage, { opacity: imageOpacity }]}
            resizeMode="cover"
          />
          <View style={styles.imageOverlay}>
            <Text style={styles.stageTitle}>{currentStageInfo?.name}</Text>
            {currentStageData?.duration && (
              <Text style={styles.stageDuration}>{currentStageData.duration}</Text>
            )}
          </View>
        </View>

        {/* Stage Indicator */}
        <StageIndicator
          currentStage={currentStage}
          onStageChange={handleStageChange}
        />

        {/* Plant Information */}
        <View style={styles.content}>
          {/* Basic Info */}
          <View style={styles.basicInfo}>
            <Text style={styles.plantName}>{displayPlant.name}</Text>
            <Text style={styles.latinName}>{displayPlant.latinName}</Text>
            <Text style={styles.description}>{displayPlant.description}</Text>
          </View>

          {/* General Information */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Genel Bilgiler</Text>
            <View style={styles.infoGrid}>
              <View style={styles.infoItem}>
                <Text style={styles.infoLabel}>Köken</Text>
                <Text style={styles.infoValue}>{displayPlant.origin}</Text>
              </View>
              <View style={styles.infoItem}>
                <Text style={styles.infoLabel}>Yaşam Süresi</Text>
                <Text style={styles.infoValue}>{displayPlant.lifespan}</Text>
              </View>
              <View style={styles.infoItem}>
                <Text style={styles.infoLabel}>Boyut</Text>
                <Text style={styles.infoValue}>{displayPlant.size}</Text>
              </View>
            </View>
          </View>

          {/* Care Instructions */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Bakım İhtiyaçları</Text>
            <View style={styles.careGrid}>
              <View style={styles.careItem}>
                <View style={styles.careIconContainer}>
                  <Ionicons name="water" size={24} color={COLORS.secondary} />
                </View>
                <Text style={styles.careLabel}>Sulama</Text>
                <Text style={styles.careValue}>{displayPlant.care.watering}</Text>
              </View>
              
              <View style={styles.careItem}>
                <View style={styles.careIconContainer}>
                  <Ionicons name="sunny" size={24} color={COLORS.accent} />
                </View>
                <Text style={styles.careLabel}>Işık</Text>
                <Text style={styles.careValue}>{displayPlant.care.light}</Text>
              </View>
              
              <View style={styles.careItem}>
                <View style={styles.careIconContainer}>
                  <Ionicons name="earth" size={24} color={COLORS.primary} />
                </View>
                <Text style={styles.careLabel}>Toprak</Text>
                <Text style={styles.careValue}>{displayPlant.care.soil}</Text>
              </View>
              
              <View style={styles.careItem}>
                <View style={styles.careIconContainer}>
                  <Ionicons name="thermometer" size={24} color={COLORS.accent} />
                </View>
                <Text style={styles.careLabel}>Sıcaklık</Text>
                <Text style={styles.careValue}>{displayPlant.care.temperature}</Text>
              </View>
            </View>
          </View>

          {/* Current Stage Details */}
          {currentStageInfo && (
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>
                {currentStageInfo.name} Aşaması
              </Text>
              <Text style={styles.stageDescription}>
                {currentStageInfo.description}
              </Text>
              {currentStageData?.duration && (
                <View style={styles.durationContainer}>
                  <Ionicons name="time" size={16} color={COLORS.darkGray} />
                  <Text style={styles.durationText}>
                    Süre: {currentStageData.duration}
                  </Text>
                </View>
              )}
            </View>
          )}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

export default PlantDetailPage;
