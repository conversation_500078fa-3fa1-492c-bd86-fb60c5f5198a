import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  Image,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  ActivityIndicator,
  StyleSheet,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

// Firebase hooks
import { usePlant } from '../../hooks/usePlants';
import { usePlantStages, useStageNavigation } from '../../hooks/usePlantStages';
import { useTreatments } from '../../hooks/useTreatments';
import { useFavorites } from '../../contexts/FavoritesContext';
import { COLORS } from '../../utils/constants';

const PlantDetailPage = ({ route, navigation }) => {
  const { plantId, plant: initialPlant } = route.params;
  const [activeTab, setActiveTab] = useState('stages'); // 'stages', 'general', or 'treatment'

  // Firebase hooks
  const { plant: firebasePlant, loading: plantLoading, error: plantError } = usePlant(plantId);
  const { stages, loading: stagesLoading, error: stagesError } = usePlantStages(plantId, true); // Real-time
  const { treatments, loading: treatmentsLoading } = useTreatments(plantId);
  const { favorites, addFavorite, removeFavorite } = useFavorites();

  // Stage navigation
  const {
    currentStage,
    currentStageOrder,
    isFirstStage,
    isLastStage,
    goToNextStage,
    goToPreviousStage,
    goToStage
  } = useStageNavigation(stages);

  // Use Firebase plant data if available, otherwise use initial plant
  const displayPlant = firebasePlant || initialPlant;

  // Favorite functions
  const isFavorite = favorites.some(fav => fav.id === plantId);
  const toggleFavorite = () => {
    if (isFavorite) {
      removeFavorite(plantId);
    } else {
      addFavorite(displayPlant);
    }
  };

  useEffect(() => {
    if (displayPlant && navigation.setOptions) {
      navigation.setOptions({
        title: displayPlant.name,
        headerRight: () => (
          <TouchableOpacity
            style={{ padding: 8 }}
            onPress={handleFavoritePress}
          >
            <Ionicons
              name={isFavorite ? 'heart' : 'heart-outline'}
              size={24}
              color={isFavorite ? COLORS.accent : COLORS.primary}
            />
          </TouchableOpacity>
        ),
      });
    }
  }, [displayPlant, isFavorite]);

  const handleFavoritePress = () => {
    if (displayPlant) {
      toggleFavorite();
    }
  };

  const handleStageChange = (stageOrder) => {
    goToStage(stageOrder);
  };

  // Current stage info from Firebase stages
  const currentStageInfo = currentStage;
  const currentStageData = currentStage;

  if ((plantLoading || stagesLoading) && !displayPlant) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={COLORS.primary} />
          <Text style={styles.loadingText}>🔥 Firebase'den bitki detayı yükleniyor...</Text>
        </View>
      </SafeAreaView>
    );
  }

  if (error && !displayPlant) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.errorContainer}>
          <Ionicons name="alert-circle" size={64} color={COLORS.accent} />
          <Text style={styles.errorTitle}>Hata Oluştu</Text>
          <Text style={styles.errorText}>{error}</Text>
          <TouchableOpacity
            style={styles.retryButton}
            onPress={() => navigation.goBack()}
          >
            <Text style={styles.retryButtonText}>Geri Dön</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  if (!displayPlant) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.errorContainer}>
          <Ionicons name="leaf" size={64} color={COLORS.gray} />
          <Text style={styles.errorTitle}>Bitki Bulunamadı</Text>
          <Text style={styles.errorText}>
            Aradığınız bitki bulunamadı veya kaldırılmış olabilir.
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  // currentStageData and currentStageInfo already defined above

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Stage Image */}
        <View style={styles.imageContainer}>
          <Image
            source={{ uri: currentStageData?.image || displayPlant.mainImage }}
            style={styles.stageImage}
            resizeMode="cover"
          />
          <View style={styles.imageOverlay}>
            <Text style={styles.stageTitle}>{currentStageInfo?.name}</Text>
            {currentStageData?.duration && (
              <Text style={styles.stageDuration}>{currentStageData.duration}</Text>
            )}
          </View>
        </View>

        {/* Stage Indicator */}
        <View style={styles.stageIndicatorContainer}>
          {stages.map((stage, index) => (
            <TouchableOpacity
              key={stage.id}
              style={[
                styles.stageIndicator,
                currentStageOrder === stage.order && styles.activeStageIndicator
              ]}
              onPress={() => handleStageChange(stage.order)}
            >
              <Text style={[
                styles.stageNumber,
                currentStageOrder === stage.order && styles.activeStageNumber
              ]}>
                {stage.order}
              </Text>
            </TouchableOpacity>
          ))}
        </View>

        {/* Plant Information */}
        <View style={styles.content}>
          {/* Basic Info */}
          <View style={styles.basicInfo}>
            <Text style={styles.plantName}>{displayPlant.name}</Text>
            <Text style={styles.latinName}>{displayPlant.latinName}</Text>
            <Text style={styles.description}>{displayPlant.description}</Text>
          </View>

          {/* Tab Navigation */}
          <View style={styles.tabContainer}>
            <TouchableOpacity
              style={[styles.tab, activeTab === 'stages' && styles.activeTab]}
              onPress={() => setActiveTab('stages')}
            >
              <Text style={[styles.tabText, activeTab === 'stages' && styles.activeTabText]}>
                Aşama Bilgilendirmesi
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.tab, activeTab === 'general' && styles.activeTab]}
              onPress={() => setActiveTab('general')}
            >
              <Text style={[styles.tabText, activeTab === 'general' && styles.activeTabText]}>
                Genel Bilgiler
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.tab, activeTab === 'treatment' && styles.activeTab]}
              onPress={() => setActiveTab('treatment')}
            >
              <Text style={[styles.tabText, activeTab === 'treatment' && styles.activeTabText]}>
                İlaçlama Önerileri
              </Text>
            </TouchableOpacity>
          </View>

          {/* Tab Content */}
          {activeTab === 'stages' ? (
            /* Current Stage Details */
            currentStageInfo && (
              <View style={styles.section}>
                <Text style={styles.sectionTitle}>
                  {currentStageInfo.name} Aşaması
                </Text>
                <Text style={styles.stageDescription}>
                  {currentStageInfo.description}
                </Text>
                {currentStageData?.duration && (
                  <View style={styles.durationContainer}>
                    <Ionicons name="time" size={16} color={COLORS.darkGray} />
                    <Text style={styles.durationText}>
                      Süre: {currentStageData.duration}
                    </Text>
                  </View>
                )}
              </View>
            )
          ) : activeTab === 'general' ? (
            /* General Information Tab */
            <>
              {/* General Information */}
              <View style={styles.section}>
                <Text style={styles.sectionTitle}>Genel Bilgiler</Text>
                <View style={styles.infoGrid}>
                  <View style={styles.infoItem}>
                    <Text style={styles.infoLabel}>Köken</Text>
                    <Text style={styles.infoValue}>{displayPlant.origin}</Text>
                  </View>
                  <View style={styles.infoItem}>
                    <Text style={styles.infoLabel}>Yaşam Süresi</Text>
                    <Text style={styles.infoValue}>{displayPlant.lifespan}</Text>
                  </View>
                  <View style={styles.infoItem}>
                    <Text style={styles.infoLabel}>Boyut</Text>
                    <Text style={styles.infoValue}>{displayPlant.size}</Text>
                  </View>
                </View>
              </View>

              {/* Care Instructions */}
              <View style={styles.section}>
                <Text style={styles.sectionTitle}>Bakım İhtiyaçları</Text>
                <View style={styles.careGrid}>
                  <View style={styles.careItem}>
                    <View style={styles.careIconContainer}>
                      <Ionicons name="water" size={24} color={COLORS.secondary} />
                    </View>
                    <Text style={styles.careLabel}>Sulama</Text>
                    <Text style={styles.careValue}>{displayPlant.care.watering}</Text>
                  </View>

                  <View style={styles.careItem}>
                    <View style={styles.careIconContainer}>
                      <Ionicons name="sunny" size={24} color={COLORS.accent} />
                    </View>
                    <Text style={styles.careLabel}>Işık</Text>
                    <Text style={styles.careValue}>{displayPlant.care.light}</Text>
                  </View>

                  <View style={styles.careItem}>
                    <View style={styles.careIconContainer}>
                      <Ionicons name="earth" size={24} color={COLORS.primary} />
                    </View>
                    <Text style={styles.careLabel}>Toprak</Text>
                    <Text style={styles.careValue}>{displayPlant.care.soil}</Text>
                  </View>

                  <View style={styles.careItem}>
                    <View style={styles.careIconContainer}>
                      <Ionicons name="thermometer" size={24} color={COLORS.accent} />
                    </View>
                    <Text style={styles.careLabel}>Sıcaklık</Text>
                    <Text style={styles.careValue}>{displayPlant.care.temperature}</Text>
                  </View>
                </View>
              </View>
            </>
          ) : (
            /* Treatment Recommendations Tab */
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>İlaçlama Önerileri</Text>

              {displayPlant.treatment?.recommendations?.map((recommendation, index) => (
                <View key={index} style={styles.treatmentItem}>
                  <View style={styles.treatmentHeader}>
                    <Ionicons name="medical" size={20} color="#FF6B6B" />
                    <Text style={styles.treatmentTitle}>{recommendation.problem}</Text>
                  </View>
                  <Text style={styles.treatmentDescription}>{recommendation.description}</Text>

                  <View style={styles.treatmentSolution}>
                    <Text style={styles.solutionTitle}>Çözüm:</Text>
                    <Text style={styles.solutionText}>{recommendation.solution}</Text>
                  </View>

                  {recommendation.products && (
                    <View style={styles.productsContainer}>
                      <Text style={styles.productsTitle}>Önerilen Ürünler:</Text>
                      {recommendation.products.map((product, productIndex) => (
                        <View key={productIndex} style={styles.productItem}>
                          <Text style={styles.productName}>• {product.name}</Text>
                          <Text style={styles.productDosage}>{product.dosage}</Text>
                        </View>
                      ))}
                    </View>
                  )}

                  {recommendation.timing && (
                    <View style={styles.timingContainer}>
                      <Ionicons name="calendar" size={16} color={COLORS.primary} />
                      <Text style={styles.timingText}>Uygulama Zamanı: {recommendation.timing}</Text>
                    </View>
                  )}
                </View>
              )) || (
                <View style={styles.noTreatmentContainer}>
                  <Ionicons name="checkmark-circle" size={48} color={COLORS.secondary} />
                  <Text style={styles.noTreatmentText}>Bu bitki için özel ilaçlama önerisi bulunmamaktadır.</Text>
                  <Text style={styles.noTreatmentSubtext}>Genel bakım kurallarını takip etmeniz yeterlidir.</Text>
                </View>
              )}
            </View>
          )}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

// Styles
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: COLORS.darkGray,
  },
  scrollView: {
    flex: 1,
  },
  imageContainer: {
    height: 250,
    position: 'relative',
  },
  stageImage: {
    width: '100%',
    height: '100%',
  },
  imageOverlay: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: 'rgba(0,0,0,0.5)',
    padding: 16,
  },
  stageTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  stageDuration: {
    fontSize: 14,
    color: '#FFFFFF',
    opacity: 0.9,
    marginTop: 4,
  },
  stageIndicatorContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    paddingVertical: 16,
    backgroundColor: '#FFFFFF',
  },
  stageIndicator: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#F5F5F5',
    justifyContent: 'center',
    alignItems: 'center',
    marginHorizontal: 4,
  },
  activeStageIndicator: {
    backgroundColor: COLORS.primary,
  },
  stageNumber: {
    fontSize: 16,
    fontWeight: 'bold',
    color: COLORS.gray,
  },
  activeStageNumber: {
    color: '#FFFFFF',
  },
  content: {
    padding: 16,
  },
  basicInfo: {
    backgroundColor: '#FFFFFF',
    padding: 16,
    borderRadius: 12,
    marginBottom: 16,
  },
  plantName: {
    fontSize: 24,
    fontWeight: 'bold',
    color: COLORS.darkGray,
    marginBottom: 4,
  },
  latinName: {
    fontSize: 16,
    fontStyle: 'italic',
    color: COLORS.gray,
    marginBottom: 8,
  },
  description: {
    fontSize: 16,
    color: COLORS.darkGray,
    lineHeight: 24,
  },
  tabContainer: {
    flexDirection: 'row',
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    marginBottom: 16,
    overflow: 'hidden',
  },
  tab: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 8,
    alignItems: 'center',
  },
  activeTab: {
    backgroundColor: COLORS.primary,
  },
  tabText: {
    fontSize: 12,
    color: COLORS.darkGray,
    textAlign: 'center',
  },
  activeTabText: {
    color: '#FFFFFF',
    fontWeight: 'bold',
  },
  section: {
    backgroundColor: '#FFFFFF',
    padding: 16,
    borderRadius: 12,
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: COLORS.darkGray,
    marginBottom: 12,
  },
  stageDescription: {
    fontSize: 16,
    color: COLORS.darkGray,
    lineHeight: 24,
    marginBottom: 12,
  },
  durationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  durationText: {
    fontSize: 14,
    color: COLORS.darkGray,
    marginLeft: 8,
  },
  infoGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  infoItem: {
    width: '48%',
    marginBottom: 12,
  },
  infoLabel: {
    fontSize: 14,
    color: COLORS.gray,
    marginBottom: 4,
  },
  infoValue: {
    fontSize: 16,
    color: COLORS.darkGray,
    fontWeight: '600',
  },
  careGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  careItem: {
    width: '48%',
    alignItems: 'center',
    marginBottom: 16,
  },
  careIconContainer: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: '#F5F5F5',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  careLabel: {
    fontSize: 14,
    color: COLORS.gray,
    marginBottom: 4,
  },
  careValue: {
    fontSize: 14,
    color: COLORS.darkGray,
    fontWeight: '600',
    textAlign: 'center',
  },
  treatmentItem: {
    backgroundColor: '#F9F9F9',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
  },
  treatmentHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  treatmentTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: COLORS.darkGray,
    marginLeft: 8,
  },
  treatmentDescription: {
    fontSize: 14,
    color: COLORS.darkGray,
    lineHeight: 20,
    marginBottom: 12,
  },
  treatmentSolution: {
    marginBottom: 12,
  },
  solutionTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    color: COLORS.primary,
    marginBottom: 4,
  },
  solutionText: {
    fontSize: 14,
    color: COLORS.darkGray,
    lineHeight: 20,
  },
  productsContainer: {
    marginBottom: 12,
  },
  productsTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    color: COLORS.primary,
    marginBottom: 8,
  },
  productItem: {
    marginBottom: 4,
  },
  productName: {
    fontSize: 14,
    color: COLORS.darkGray,
    fontWeight: '600',
  },
  productDosage: {
    fontSize: 12,
    color: COLORS.gray,
    marginLeft: 16,
  },
  timingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  timingText: {
    fontSize: 12,
    color: COLORS.darkGray,
    marginLeft: 8,
  },
  noTreatmentContainer: {
    alignItems: 'center',
    padding: 20,
  },
  noTreatmentText: {
    fontSize: 16,
    color: COLORS.darkGray,
    textAlign: 'center',
    marginTop: 12,
  },
  noTreatmentSubtext: {
    fontSize: 14,
    color: COLORS.gray,
    textAlign: 'center',
    marginTop: 8,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
  },
  errorTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: COLORS.accent,
    marginTop: 16,
    marginBottom: 8,
  },
  errorText: {
    fontSize: 14,
    color: COLORS.gray,
    textAlign: 'center',
    marginBottom: 16,
  },
  retryButton: {
    backgroundColor: COLORS.primary,
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 8,
  },
  retryButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: 'bold',
  },
});

export default PlantDetailPage;
