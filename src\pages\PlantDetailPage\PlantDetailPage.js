import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  Image,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  Animated,
  ActivityIndicator,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

import StageIndicator from '../../components/StageIndicator/StageIndicator';
import LoadingSpinner from '../../components/common/LoadingSpinner/LoadingSpinner';
import { usePlantDetail } from '../../hooks/usePlantData';
import apiService from '../../services/apiService';
import { useFavoriteToggle } from '../../hooks/useFavorites';
import { COLORS, PLANT_STAGES } from '../../utils/constants';
import styles from './PlantDetailPage.styles.js';

const PlantDetailPage = ({ route, navigation }) => {
  const { plantId, plant: initialPlant } = route.params;
  const [currentStage, setCurrentStage] = useState(1);
  const [imageOpacity] = useState(new Animated.Value(1));
  const [activeTab, setActiveTab] = useState('stages'); // 'stages', 'general', or 'treatment'
  const [plantStages, setPlantStages] = useState([]);
  const [stagesLoading, setStagesLoading] = useState(false);
  
  const { plant, loading, error } = usePlantDetail(plantId);
  const { toggleFavorite, isFavorite, loading: favoriteLoading } = useFavoriteToggle();

  // Use initial plant data if available, otherwise use loaded plant
  const displayPlant = plant || initialPlant;

  // Load plant stages from plant data
  useEffect(() => {
    if (displayPlant && displayPlant.plantstages) {
      setPlantStages(displayPlant.plantstages);
      console.log(`📊 ${displayPlant.name} için ${displayPlant.plantstages.length} aşama yüklendi`);
      setStagesLoading(false);
    } else if (displayPlant) {
      // Fallback: try to load from separate collection
      const loadPlantStages = async () => {
        setStagesLoading(true);
        try {
          const stages = await apiService.getPlantStagesByName(displayPlant.name);
          setPlantStages(stages);
          console.log(`📊 ${displayPlant.name} için ${stages.length} aşama yüklendi (fallback)`);
        } catch (error) {
          console.error('Aşama yükleme hatası:', error);
        } finally {
          setStagesLoading(false);
        }
      };
      loadPlantStages();
    }
  }, [displayPlant]);

  useEffect(() => {
    if (displayPlant) {
      navigation.setOptions({
        title: displayPlant.name,
        headerRight: () => (
          <TouchableOpacity
            style={styles.headerFavoriteButton}
            onPress={handleFavoritePress}
            disabled={favoriteLoading}
          >
            <Ionicons
              name={isFavorite(displayPlant.id) ? 'heart' : 'heart-outline'}
              size={24}
              color={isFavorite(displayPlant.id) ? COLORS.accent : COLORS.primary}
            />
          </TouchableOpacity>
        ),
      });
    }
  }, [displayPlant, isFavorite, favoriteLoading]);

  const handleFavoritePress = () => {
    if (displayPlant) {
      toggleFavorite(displayPlant.id);
    }
  };

  const handleStageChange = (stageId) => {
    if (stageId !== currentStage) {
      // Fade out current image
      Animated.timing(imageOpacity, {
        toValue: 0,
        duration: 150,
        useNativeDriver: true,
      }).start(() => {
        setCurrentStage(stageId);
        // Fade in new image
        Animated.timing(imageOpacity, {
          toValue: 1,
          duration: 150,
          useNativeDriver: true,
        }).start();
      });
    }
  };

  const getCurrentStageData = () => {
    if (plantStages.length > 0) {
      return plantStages.find(stage => stage.id === currentStage);
    }
    // Fallback to old data structure
    if (!displayPlant || !displayPlant.stages) return null;
    return displayPlant.stages.find(stage => stage.stageId === currentStage);
  };

  const getCurrentStageInfo = () => {
    const firebaseStage = plantStages.find(stage => stage.id === currentStage);
    if (firebaseStage) return firebaseStage;

    // Fallback to constants
    return PLANT_STAGES.find(stage => stage.id === currentStage);
  };

  if (loading && !displayPlant) {
    return (
      <SafeAreaView style={styles.container}>
        <LoadingSpinner text="Bitki detayı yükleniyor..." />
      </SafeAreaView>
    );
  }

  if (error && !displayPlant) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.errorContainer}>
          <Ionicons name="alert-circle" size={64} color={COLORS.accent} />
          <Text style={styles.errorTitle}>Hata Oluştu</Text>
          <Text style={styles.errorText}>{error}</Text>
          <TouchableOpacity
            style={styles.retryButton}
            onPress={() => navigation.goBack()}
          >
            <Text style={styles.retryButtonText}>Geri Dön</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  if (!displayPlant) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.errorContainer}>
          <Ionicons name="leaf" size={64} color={COLORS.gray} />
          <Text style={styles.errorTitle}>Bitki Bulunamadı</Text>
          <Text style={styles.errorText}>
            Aradığınız bitki bulunamadı veya kaldırılmış olabilir.
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  const currentStageData = getCurrentStageData();
  const currentStageInfo = getCurrentStageInfo();

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Stage Image */}
        <View style={styles.imageContainer}>
          <Animated.Image
            source={{ uri: currentStageData?.image || displayPlant.mainImage }}
            style={[styles.stageImage, { opacity: imageOpacity }]}
            resizeMode="cover"
          />
          <View style={styles.imageOverlay}>
            <Text style={styles.stageTitle}>{currentStageInfo?.name}</Text>
            {currentStageData?.duration && (
              <Text style={styles.stageDuration}>{currentStageData.duration}</Text>
            )}
          </View>
        </View>

        {/* Stage Indicator */}
        <StageIndicator
          currentStage={currentStage}
          onStageChange={handleStageChange}
          totalStages={plantStages.length || 10}
          stages={plantStages}
        />

        {/* Plant Information */}
        <View style={styles.content}>
          {/* Basic Info */}
          <View style={styles.basicInfo}>
            <Text style={styles.plantName}>{displayPlant.name}</Text>
            <Text style={styles.latinName}>{displayPlant.latinName}</Text>
            <Text style={styles.description}>{displayPlant.description}</Text>
          </View>

          {/* Tab Navigation */}
          <View style={styles.tabContainer}>
            <TouchableOpacity
              style={[styles.tab, activeTab === 'stages' && styles.activeTab]}
              onPress={() => setActiveTab('stages')}
            >
              <Text style={[styles.tabText, activeTab === 'stages' && styles.activeTabText]}>
                Aşama Bilgilendirmesi
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.tab, activeTab === 'general' && styles.activeTab]}
              onPress={() => setActiveTab('general')}
            >
              <Text style={[styles.tabText, activeTab === 'general' && styles.activeTabText]}>
                Genel Bilgiler
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.tab, activeTab === 'treatment' && styles.activeTab]}
              onPress={() => setActiveTab('treatment')}
            >
              <Text style={[styles.tabText, activeTab === 'treatment' && styles.activeTabText]}>
                İlaçlama Önerileri
              </Text>
            </TouchableOpacity>
          </View>

          {/* Tab Content */}
          {activeTab === 'stages' ? (
            stagesLoading ? (
              <View style={styles.section}>
                <ActivityIndicator size="large" color={COLORS.primary} />
                <Text style={styles.description}>Aşama bilgileri yükleniyor...</Text>
              </View>
            ) : currentStageInfo ? (
              <View style={styles.section}>
                <Text style={styles.sectionTitle}>
                  {currentStageInfo.title || currentStageInfo.name}
                </Text>
                <Text style={styles.stageDescription}>
                  {currentStageInfo.description}
                </Text>

                <View style={styles.infoGrid}>
                  <View style={styles.infoItem}>
                    <Ionicons name="time-outline" size={20} color={COLORS.primary} />
                    <Text style={styles.infoValue}>
                      Süre: {currentStageInfo.duration}
                    </Text>
                  </View>

                  {currentStageInfo.careInstructions && (
                    <>
                      <View style={styles.infoItem}>
                        <Ionicons name="thermometer-outline" size={20} color={COLORS.primary} />
                        <Text style={styles.infoValue}>
                          Sıcaklık: {currentStageInfo.careInstructions.temperature}
                        </Text>
                      </View>

                      <View style={styles.infoItem}>
                        <Ionicons name="water-outline" size={20} color={COLORS.primary} />
                        <Text style={styles.infoValue}>
                          Sulama: {currentStageInfo.careInstructions.watering}
                        </Text>
                      </View>

                      <View style={styles.infoItem}>
                        <Ionicons name="sunny-outline" size={20} color={COLORS.primary} />
                        <Text style={styles.infoValue}>
                          Işık: {currentStageInfo.careInstructions.light}
                        </Text>
                      </View>
                    </>
                  )}
                </View>

                {/* Stage Tips */}
                {currentStageInfo.tips && currentStageInfo.tips.length > 0 && (
                  <View style={styles.section}>
                    <Text style={styles.sectionTitle}>💡 İpuçları</Text>
                    {currentStageInfo.tips.map((tip, index) => (
                      <Text key={index} style={styles.description}>
                        • {tip}
                      </Text>
                    ))}
                  </View>
                )}
              </View>
            ) : (
              <View style={styles.errorContainer}>
                <Text style={styles.errorText}>Bu aşama için bilgi bulunamadı</Text>
              </View>
            )
          ) : activeTab === 'general' ? (
            /* General Information Tab */
            <>
              {/* General Information */}
              <View style={styles.section}>
                <Text style={styles.sectionTitle}>Genel Bilgiler</Text>
                <View style={styles.infoGrid}>
                  <View style={styles.infoItem}>
                    <Text style={styles.infoLabel}>Köken</Text>
                    <Text style={styles.infoValue}>{displayPlant.origin}</Text>
                  </View>
                  <View style={styles.infoItem}>
                    <Text style={styles.infoLabel}>Yaşam Süresi</Text>
                    <Text style={styles.infoValue}>{displayPlant.lifespan}</Text>
                  </View>
                  <View style={styles.infoItem}>
                    <Text style={styles.infoLabel}>Boyut</Text>
                    <Text style={styles.infoValue}>{displayPlant.size}</Text>
                  </View>
                </View>
              </View>

              {/* Care Instructions */}
              <View style={styles.section}>
                <Text style={styles.sectionTitle}>Bakım İhtiyaçları</Text>
                <View style={styles.careGrid}>
                  <View style={styles.careItem}>
                    <View style={styles.careIconContainer}>
                      <Ionicons name="water" size={24} color={COLORS.secondary} />
                    </View>
                    <Text style={styles.careLabel}>Sulama</Text>
                    <Text style={styles.careValue}>{displayPlant.care.watering}</Text>
                  </View>

                  <View style={styles.careItem}>
                    <View style={styles.careIconContainer}>
                      <Ionicons name="sunny" size={24} color={COLORS.accent} />
                    </View>
                    <Text style={styles.careLabel}>Işık</Text>
                    <Text style={styles.careValue}>{displayPlant.care.light}</Text>
                  </View>

                  <View style={styles.careItem}>
                    <View style={styles.careIconContainer}>
                      <Ionicons name="earth" size={24} color={COLORS.primary} />
                    </View>
                    <Text style={styles.careLabel}>Toprak</Text>
                    <Text style={styles.careValue}>{displayPlant.care.soil}</Text>
                  </View>

                  <View style={styles.careItem}>
                    <View style={styles.careIconContainer}>
                      <Ionicons name="thermometer" size={24} color={COLORS.accent} />
                    </View>
                    <Text style={styles.careLabel}>Sıcaklık</Text>
                    <Text style={styles.careValue}>{displayPlant.care.temperature}</Text>
                  </View>
                </View>
              </View>
            </>
          ) : (
            /* Treatment Recommendations Tab */
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>İlaçlama Önerileri</Text>

              {displayPlant.treatment?.recommendations?.map((recommendation, index) => (
                <View key={index} style={styles.treatmentItem}>
                  <View style={styles.treatmentHeader}>
                    <Ionicons name="medical" size={20} color="#FF6B6B" />
                    <Text style={styles.treatmentTitle}>{recommendation.problem}</Text>
                  </View>
                  <Text style={styles.treatmentDescription}>{recommendation.description}</Text>

                  <View style={styles.treatmentSolution}>
                    <Text style={styles.solutionTitle}>Çözüm:</Text>
                    <Text style={styles.solutionText}>{recommendation.solution}</Text>
                  </View>

                  {recommendation.products && (
                    <View style={styles.productsContainer}>
                      <Text style={styles.productsTitle}>Önerilen Ürünler:</Text>
                      {recommendation.products.map((product, productIndex) => (
                        <View key={productIndex} style={styles.productItem}>
                          <Text style={styles.productName}>• {product.name}</Text>
                          <Text style={styles.productDosage}>{product.dosage}</Text>
                        </View>
                      ))}
                    </View>
                  )}

                  {recommendation.timing && (
                    <View style={styles.timingContainer}>
                      <Ionicons name="calendar" size={16} color={COLORS.primary} />
                      <Text style={styles.timingText}>Uygulama Zamanı: {recommendation.timing}</Text>
                    </View>
                  )}
                </View>
              )) || (
                <View style={styles.noTreatmentContainer}>
                  <Ionicons name="checkmark-circle" size={48} color={COLORS.secondary} />
                  <Text style={styles.noTreatmentText}>Bu bitki için özel ilaçlama önerisi bulunmamaktadır.</Text>
                  <Text style={styles.noTreatmentSubtext}>Genel bakım kurallarını takip etmeniz yeterlidir.</Text>
                </View>
              )}
            </View>
          )}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

export default PlantDetailPage;
