import { useState, useEffect } from 'react';
import { plantsService, realtimeService } from '../services/firebaseService';

// Mock data fallback
const mockPlants = [
  {
    id: 'mock-1',
    name: '<PERSON><PERSON><PERSON>',
    latinName: '<PERSON> damascena',
    category: '<PERSON>i<PERSON><PERSON>',
    description: '<PERSON><PERSON><PERSON> kokulu ve renkli çiçeklere sahip popüler bir bitki.',
    mainImage: 'https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=400&h=400&fit=crop',
    origin: '<PERSON>ta Doğu',
    lifespan: 'Çok yıllık',
    size: '1-2 metre',
    care: {
      watering: '<PERSON><PERSON><PERSON><PERSON>',
      light: '<PERSON>ü<PERSON><PERSON>',
      soil: 'İyi drene',
      temperature: '15-25°C'
    }
  },
  {
    id: 'mock-2',
    name: '<PERSON><PERSON><PERSON>',
    latinName: 'Lavandula angustifolia',
    category: 'Aromatik',
    description: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> kokusu ile bilinen mor çiçekli bitki.',
    mainImage: 'https://images.unsplash.com/photo-1611909023032-2d6b3134ecba?w=400&h=400&fit=crop',
    origin: '<PERSON>k<PERSON><PERSON>',
    lifespan: 'Çok yıllık',
    size: '30-60 cm',
    care: {
      watering: 'Az',
      light: 'Güneş',
      soil: 'Kuru',
      temperature: '10-30°C'
    }
  },
  {
    id: 'mock-3',
    name: 'Nane',
    latinName: 'Mentha spicata',
    category: 'Aromatik',
    description: 'Ferahlatıcı kokusu ve tadı olan yeşil yapraklı bitki.',
    mainImage: 'https://images.unsplash.com/photo-1628556270448-4d4e4148e1b1?w=400&h=400&fit=crop',
    origin: 'Avrupa',
    lifespan: 'Çok yıllık',
    size: '20-40 cm',
    care: {
      watering: 'Bol',
      light: 'Yarı gölge',
      soil: 'Nemli',
      temperature: '15-25°C'
    }
  }
];

// Tüm bitkileri getiren hook
export const usePlants = (useRealtime = false) => {
  const [plants, setPlants] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    if (useRealtime) {
      // Real-time listener kullan
      console.log('🔄 Firebase: Real-time plants listener başlatılıyor...');
      setLoading(true);

      const unsubscribe = realtimeService.subscribePlants((plantsData) => {
        if (plantsData.length > 0) {
          setPlants(plantsData);
          console.log('✅ Firebase: Real-time bitkiler güncellendi:', plantsData.length);
        } else {
          setPlants(mockPlants);
          console.log('⚠️ Firebase: Real-time veri bulunamadı, mock data kullanılıyor');
        }
        setLoading(false);
        setError(null);
      });

      return () => {
        console.log('🔄 Firebase: Real-time plants listener kapatılıyor...');
        unsubscribe();
      };
    } else {
      // Tek seferlik veri çekme
      loadPlants();
    }
  }, [useRealtime]);

  const loadPlants = async () => {
    try {
      setLoading(true);
      setError(null);

      console.log('🔥 Firebase: Bitkiler yükleniyor...');
      const plantsData = await plantsService.getAllPlants();

      if (plantsData.length > 0) {
        setPlants(plantsData);
        console.log('✅ Firebase: Bitkiler yüklendi:', plantsData.length);
      } else {
        // Firebase'de veri yoksa mock data kullan
        setPlants(mockPlants);
        console.log('⚠️ Firebase: Veri bulunamadı, mock data kullanılıyor');
      }
    } catch (err) {
      console.error('❌ Plants yüklenemedi, mock data kullanılıyor:', err);
      setPlants(mockPlants);
      setError('Firebase bağlantı hatası, örnek veriler gösteriliyor');
    } finally {
      setLoading(false);
    }
  };

  const refreshPlants = () => {
    if (!useRealtime) {
      loadPlants();
    }
  };

  return {
    plants,
    loading,
    error,
    refreshPlants
  };
};

// Tek bitki getiren hook
export const usePlant = (plantId) => {
  const [plant, setPlant] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    if (plantId) {
      loadPlant();
    }
  }, [plantId]);

  const loadPlant = async () => {
    try {
      setLoading(true);
      setError(null);
      
      // Mock Firebase service call
      const plantData = null;
      
      if (plantData) {
        setPlant(plantData);
        console.log('✅ Firebase plant yüklendi:', plantData.name);
      } else {
        // Mock data'dan bul
        const mockPlant = mockPlants.find(p => p.id === plantId);
        if (mockPlant) {
          setPlant(mockPlant);
          console.log('✅ Mock plant kullanılıyor:', mockPlant.name);
        } else {
          setError('Bitki bulunamadı');
        }
      }
    } catch (err) {
      console.error('❌ Plant yüklenemedi:', err);
      // Mock data'dan bul
      const mockPlant = mockPlants.find(p => p.id === plantId);
      if (mockPlant) {
        setPlant(mockPlant);
        console.log('✅ Mock plant kullanılıyor (hata durumu):', mockPlant.name);
      } else {
        setError('Bitki yüklenemedi');
      }
    } finally {
      setLoading(false);
    }
  };

  return {
    plant,
    loading,
    error,
    refreshPlant: loadPlant
  };
};

// Kategoriye göre bitkiler getiren hook
export const usePlantsByCategory = (category) => {
  const [plants, setPlants] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    if (category) {
      loadPlantsByCategory();
    }
  }, [category]);

  const loadPlantsByCategory = async () => {
    try {
      setLoading(true);
      setError(null);
      
      // Mock Firebase service call
      const plantsData = [];
      
      if (plantsData.length > 0) {
        setPlants(plantsData);
        console.log('✅ Firebase category plants yüklendi:', plantsData.length);
      } else {
        // Mock data'dan filtrele
        const mockCategoryPlants = mockPlants.filter(p => p.category === category);
        setPlants(mockCategoryPlants);
        console.log('✅ Mock category plants kullanılıyor:', mockCategoryPlants.length);
      }
    } catch (err) {
      console.error('❌ Category plants yüklenemedi:', err);
      // Mock data'dan filtrele
      const mockCategoryPlants = mockPlants.filter(p => p.category === category);
      setPlants(mockCategoryPlants);
      setError('Firebase bağlantı hatası, örnek veriler gösteriliyor');
    } finally {
      setLoading(false);
    }
  };

  return {
    plants,
    loading,
    error,
    refreshPlants: loadPlantsByCategory
  };
};

// Arama hook'u
export const useSearchPlants = () => {
  const [searchResults, setSearchResults] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const searchPlants = async (searchTerm) => {
    if (!searchTerm.trim()) {
      setSearchResults([]);
      return;
    }

    try {
      setLoading(true);
      setError(null);
      
      // Mock Firebase search
      const results = [];
      const filteredResults = results.filter(plant => 
        plant.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        plant.latinName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        plant.description.toLowerCase().includes(searchTerm.toLowerCase())
      );
      
      if (filteredResults.length > 0) {
        setSearchResults(filteredResults);
        console.log('✅ Firebase search sonuçları:', filteredResults.length);
      } else {
        // Mock data'da ara
        const mockResults = mockPlants.filter(plant => 
          plant.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
          plant.latinName.toLowerCase().includes(searchTerm.toLowerCase()) ||
          plant.description.toLowerCase().includes(searchTerm.toLowerCase())
        );
        setSearchResults(mockResults);
        console.log('✅ Mock search sonuçları:', mockResults.length);
      }
    } catch (err) {
      console.error('❌ Search yapılamadı:', err);
      // Mock data'da ara
      const mockResults = mockPlants.filter(plant => 
        plant.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        plant.latinName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        plant.description.toLowerCase().includes(searchTerm.toLowerCase())
      );
      setSearchResults(mockResults);
      setError('Arama yapılamadı, örnek veriler gösteriliyor');
    } finally {
      setLoading(false);
    }
  };

  const clearSearch = () => {
    setSearchResults([]);
    setError(null);
  };

  return {
    searchResults,
    loading,
    error,
    searchPlants,
    clearSearch
  };
};
