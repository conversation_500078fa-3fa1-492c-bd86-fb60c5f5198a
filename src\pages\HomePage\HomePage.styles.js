import { StyleSheet } from 'react-native';
import { COLORS, FONT_SIZES, SPACING } from '../../utils/constants';

export default StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FC7138', // Turuncu status bar için
  },

  scrollView: {
    flex: 1,
  },

  scrollContent: {
    backgroundColor: COLORS.background,
    paddingBottom: SPACING.tabBar, // Extra space for floating tab bar
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    marginTop: -20, // Header ile overlap
  },

  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: SPACING.lg,
    paddingVertical: SPACING.lg,
    paddingTop: SPACING.xxl,
    backgroundColor: '#FC7138', // Turuncu arka plan
    marginBottom: SPACING.lg,
    borderBottomLeftRadius: 20,
    borderBottomRightRadius: 20,
    shadowColor: COLORS.shadow,
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 6,
  },

  logoSection: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },

  logo: {
    width: 40,
    height: 40,
    marginRight: SPACING.md,
  },

  titleSection: {
    flex: 1,
  },

  title: {
    fontSize: FONT_SIZES.header,
    fontWeight: '800',
    color: COLORS.white,
    marginBottom: 2,
    letterSpacing: 0.5,
  },

  subtitle: {
    fontSize: FONT_SIZES.medium,
    color: COLORS.white,
    fontWeight: '500',
    opacity: 0.9,
    letterSpacing: 0.3,
  },

  userButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.3)',
  },
  
  searchContainer: {
    paddingHorizontal: SPACING.lg,
    marginBottom: SPACING.lg,
  },

  searchInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.white,
    borderRadius: 16,
    paddingHorizontal: SPACING.lg,
    paddingVertical: SPACING.md,
    shadowColor: COLORS.shadow,
    shadowOffset: {
      width: 0,
      height: 3,
    },
    shadowOpacity: 0.12,
    shadowRadius: 6,
    elevation: 4,
    gap: SPACING.sm,
    borderWidth: 1,
    borderColor: COLORS.lightGray,
  },
  
  searchInput: {
    flex: 1,
    fontSize: FONT_SIZES.medium,
    color: COLORS.primary,
  },
  
  resultsInfo: {
    paddingHorizontal: SPACING.md,
    marginBottom: SPACING.sm,
  },
  
  resultsText: {
    fontSize: FONT_SIZES.small,
    color: COLORS.darkGray,
    fontStyle: 'italic',
  },
  
  errorContainer: {
    margin: SPACING.md,
    padding: SPACING.md,
    backgroundColor: '#ffebee',
    borderRadius: 8,
    alignItems: 'center',
  },
  
  errorText: {
    fontSize: FONT_SIZES.medium,
    color: '#c62828',
    textAlign: 'center',
    marginBottom: SPACING.sm,
  },
  
  retryButton: {
    backgroundColor: COLORS.accent,
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.sm,
    borderRadius: 6,
  },
  
  retryButtonText: {
    color: COLORS.white,
    fontSize: FONT_SIZES.medium,
    fontWeight: '600',
  },
  
  listContent: {
    paddingHorizontal: SPACING.lg,
    paddingBottom: SPACING.tabBar, // Extra space for floating tab bar
  },
  
  row: {
    justifyContent: 'space-between',
  },
  
  plantCard: {
    // PlantCard component'inin kendi stilleri kullanılacak
  },
  
  emptyState: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: SPACING.xxl,
  },
  
  emptyStateTitle: {
    fontSize: FONT_SIZES.xlarge,
    fontWeight: '600',
    color: COLORS.primary,
    marginTop: SPACING.md,
    marginBottom: SPACING.sm,
  },
  
  emptyStateText: {
    fontSize: FONT_SIZES.medium,
    color: COLORS.darkGray,
    textAlign: 'center',
    paddingHorizontal: SPACING.xl,
    lineHeight: 20,
  },
});
