import React from 'react';
import { StatusBar } from 'expo-status-bar';
import { NavigationContainer } from '@react-navigation/native';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { createStackNavigator } from '@react-navigation/stack';
import { Ionicons } from '@expo/vector-icons';

// Sayfalar
import HomePage from './src/pages/HomePage/HomePage';
import FavoritesPage from './src/pages/FavoritesPage/FavoritesPage';
import PlantDetailPage from './src/pages/PlantDetailPage/PlantDetailPageNew';

// Context
import { FavoritesProvider } from './src/contexts/FavoritesContext';

const Tab = createBottomTabNavigator();
const Stack = createStackNavigator();

// Tab Navigator
function TabNavigator() {
  return (
    <Tab.Navigator
      screenOptions={({ route }) => ({
        tabBarIcon: ({ focused, color, size }) => {
          let iconName;
          if (route.name === 'Home') {
            iconName = focused ? 'leaf' : 'leaf-outline';
          } else if (route.name === 'Favorites') {
            iconName = focused ? 'heart' : 'heart-outline';
          }
          return <Ionicons name={iconName} size={size} color={color} />;
        },
        tabBarActiveTintColor: '#7C4D25',
        tabBarInactiveTintColor: 'gray',
        headerShown: false,
      })}
    >
      <Tab.Screen name="Home" component={HomePage} options={{ title: 'Ana Sayfa' }} />
      <Tab.Screen name="Favorites" component={FavoritesPage} options={{ title: 'Favoriler' }} />
    </Tab.Navigator>
  );
}

export default function App() {
  return (
    <FavoritesProvider>
      <NavigationContainer>
        <Stack.Navigator screenOptions={{ headerShown: false }}>
          <Stack.Screen name="Main" component={TabNavigator} />
          <Stack.Screen name="PlantDetail" component={PlantDetailPage} />
        </Stack.Navigator>
      </NavigationContainer>
      <StatusBar style="dark" />
    </FavoritesProvider>
  );
}
