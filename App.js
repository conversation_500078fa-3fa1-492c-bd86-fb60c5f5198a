import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, SafeAreaView, ScrollView, TouchableOpacity, ActivityIndicator } from 'react-native';
import { StatusBar } from 'expo-status-bar';

// Mock data
const mockPlants = [
  {
    id: '1',
    name: '<PERSON><PERSON><PERSON>',
    latinName: '<PERSON> damascena',
    category: '<PERSON>i<PERSON><PERSON>',
    description: '<PERSON><PERSON><PERSON> kokulu ve renkli çiçeklere sahip popüler bir bitki.'
  },
  {
    id: '2',
    name: '<PERSON><PERSON><PERSON>',
    latinName: 'Lavandula angustifolia',
    category: 'Aromatik',
    description: 'Ra<PERSON>latıcı kokusu ile bilinen mor çiçekli bitki.'
  },
  {
    id: '3',
    name: '<PERSON><PERSON>',
    latinName: 'Mentha spicata',
    category: 'Aromatik',
    description: '<PERSON><PERSON>latıcı kokusu ve tadı olan yeşil yapraklı bitki.'
  }
];

export default function App() {
  const [plants, setPlants] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Mock veri yükle
  const loadPlants = async () => {
    try {
      console.log('📱 Mock veri yükleniyor...');
      setLoading(true);
      setError(null);

      // Simulate loading delay
      await new Promise(resolve => setTimeout(resolve, 1000));

      setPlants(mockPlants);
      console.log('✅ Mock plants loaded:', mockPlants.length);
    } catch (err) {
      console.error('❌ Error:', err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadPlants();
  }, []);

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar style="dark" />

      <View style={styles.header}>
        <Text style={styles.title}>WikiPlant Test</Text>
        <Text style={styles.subtitle}>Bitki Ansiklopedisi</Text>
      </View>

      <ScrollView style={styles.content}>
        {loading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color="#7C4D25" />
            <Text style={styles.loadingText}>Veriler yükleniyor...</Text>
          </View>
        ) : error ? (
          <View style={styles.errorContainer}>
            <Text style={styles.errorTitle}>❌ Hata Oluştu</Text>
            <Text style={styles.errorText}>{error}</Text>
            <TouchableOpacity style={styles.retryButton} onPress={loadPlants}>
              <Text style={styles.retryButtonText}>Tekrar Dene</Text>
            </TouchableOpacity>
          </View>
        ) : (
          <View style={styles.successContainer}>
            <Text style={styles.successTitle}>✅ WikiPlant Çalışıyor!</Text>
            <Text style={styles.successText}>{plants.length} bitki bulundu</Text>

            {plants.map((plant, index) => (
              <View key={plant.id} style={styles.plantItem}>
                <Text style={styles.plantName}>{plant.name || 'İsimsiz Bitki'}</Text>
                <Text style={styles.plantLatin}>{plant.latinName || 'Latin adı yok'}</Text>
                <Text style={styles.plantCategory}>{plant.category || 'Kategori yok'}</Text>
              </View>
            ))}

            {plants.length === 0 && (
              <Text style={styles.emptyText}>Henüz bitki eklenmemiş</Text>
            )}
          </View>
        )}
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    backgroundColor: '#FC7138',
    padding: 20,
    paddingTop: 40,
    alignItems: 'center',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  subtitle: {
    fontSize: 16,
    color: '#FFFFFF',
    opacity: 0.9,
    marginTop: 4,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  loadingContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 40,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#666',
  },
  errorContainer: {
    alignItems: 'center',
    padding: 20,
  },
  errorTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#FF6B6B',
    marginBottom: 8,
  },
  errorText: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    marginBottom: 16,
  },
  retryButton: {
    backgroundColor: '#7C4D25',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 8,
  },
  retryButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: 'bold',
  },
  successContainer: {
    alignItems: 'center',
  },
  successTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#4CAF50',
    marginBottom: 8,
  },
  successText: {
    fontSize: 16,
    color: '#666',
    marginBottom: 20,
  },
  plantItem: {
    backgroundColor: '#FFFFFF',
    padding: 16,
    marginVertical: 8,
    borderRadius: 12,
    width: '100%',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  plantName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  plantLatin: {
    fontSize: 14,
    fontStyle: 'italic',
    color: '#666',
    marginTop: 4,
  },
  plantCategory: {
    fontSize: 12,
    color: '#7C4D25',
    fontWeight: '600',
    marginTop: 4,
  },
  emptyText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginTop: 20,
  },
});
