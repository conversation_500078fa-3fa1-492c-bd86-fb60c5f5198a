import 'react-native-url-polyfill/auto';
import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  Image,
  ActivityIndicator,
  StyleSheet,
} from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { Ionicons } from '@expo/vector-icons';

// Firebase
import { collection, getDocs } from 'firebase/firestore';
import { db } from './src/config/firebase';

// Mock data - Firebase fallback için
const mockPlants = [
  {
    id: '1',
    name: '<PERSON>ü<PERSON>',
    latinName: 'Rosa damascena',
    category: 'Çiçek',
    description: '<PERSON><PERSON>zel kokulu ve renkli çiçeklere sahip popüler bir bitki.',
    mainImage: 'https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=400&h=400&fit=crop',
  },
  {
    id: '2',
    name: '<PERSON><PERSON><PERSON>',
    latinName: '<PERSON><PERSON><PERSON><PERSON> angustifolia',
    category: 'Aromatik',
    description: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> kokusu ile bilinen mor çiçekli bitki.',
    mainImage: 'https://images.unsplash.com/photo-1611909023032-2d6b3134ecba?w=400&h=400&fit=crop',
  },
  {
    id: '3',
    name: 'Nane',
    latinName: 'Mentha spicata',
    category: 'Aromatik',
    description: 'Ferahlatıcı kokusu ve tadı olan yeşil yapraklı bitki.',
    mainImage: 'https://images.unsplash.com/photo-1628556270448-4d4e4148e1b1?w=400&h=400&fit=crop',
  },
];

export default function App() {
  const [plants, setPlants] = useState([]);
  const [loading, setLoading] = useState(true);

  // Firebase'den veri yükle
  const loadPlants = async () => {
    try {
      console.log('🔥 Firebase\'den bitkiler yükleniyor...');
      setLoading(true);

      // Firebase'den plants collection'ını çek
      const plantsCollection = collection(db, 'plants');
      const plantsSnapshot = await getDocs(plantsCollection);
      const plantsData = plantsSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));

      if (plantsData.length > 0) {
        setPlants(plantsData);
        console.log('✅ Firebase plants loaded:', plantsData.length);
      } else {
        // Firebase'de veri yoksa mock data kullan
        setPlants(mockPlants);
        console.log('✅ Mock plants loaded (Firebase empty):', mockPlants.length);
      }
    } catch (err) {
      console.error('❌ Firebase error, using mock data:', err);
      // Firebase hatası durumunda mock data kullan
      setPlants(mockPlants);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadPlants();
  }, []);

  // Loading state
  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <StatusBar style="dark" />
        <View style={styles.header}>
          <Text style={styles.title}>Dallardan Bilgi</Text>
          <Text style={styles.subtitle}>Bitki Ansiklopedisi</Text>
        </View>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#7C4D25" />
          <Text style={styles.loadingText}>Bitkiler yükleniyor...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar style="dark" />
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Header */}
        <View style={styles.header}>
          <Text style={styles.title}>Dallardan Bilgi</Text>
          <Text style={styles.subtitle}>Bitki Ansiklopedisi</Text>
        </View>

        {/* Plant Count */}
        <View style={styles.countContainer}>
          <Text style={styles.countText}>{plants.length} Bitki Bulundu</Text>
        </View>

        {/* Plant List */}
        {plants.map((plant) => (
          <TouchableOpacity
            key={plant.id}
            style={styles.plantCard}
            activeOpacity={0.8}
          >
            <Image
              source={{ uri: plant.mainImage }}
              style={styles.plantImage}
              resizeMode="cover"
            />
            <View style={styles.plantInfo}>
              <Text style={styles.plantName}>{plant.name}</Text>
              <Text style={styles.plantLatin}>{plant.latinName}</Text>
              <Text style={styles.plantCategory}>{plant.category}</Text>
              <Text style={styles.plantDescription} numberOfLines={2}>
                {plant.description}
              </Text>
              <View style={styles.detailButton}>
                <Text style={styles.detailButtonText}>Detay</Text>
                <Ionicons name="chevron-forward" size={16} color="#FFFFFF" />
              </View>
            </View>
          </TouchableOpacity>
        ))}
      </ScrollView>
    </SafeAreaView>
  );
}

// Stiller
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  scrollView: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#333333',
  },
  header: {
    backgroundColor: '#FC7138',
    padding: 20,
    paddingTop: 40,
    alignItems: 'center',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  subtitle: {
    fontSize: 16,
    color: '#FFFFFF',
    opacity: 0.9,
    marginTop: 4,
  },
  countContainer: {
    padding: 16,
    backgroundColor: '#FFFFFF',
  },
  countText: {
    fontSize: 16,
    color: '#333333',
    fontWeight: '600',
  },
  plantCard: {
    backgroundColor: '#FFFFFF',
    marginHorizontal: 16,
    marginVertical: 8,
    borderRadius: 12,
    flexDirection: 'row',
    overflow: 'hidden',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  plantImage: {
    width: 100,
    height: 120,
  },
  plantInfo: {
    flex: 1,
    padding: 12,
    justifyContent: 'space-between',
  },
  plantName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333333',
  },
  plantLatin: {
    fontSize: 14,
    fontStyle: 'italic',
    color: '#CCCCCC',
    marginTop: 2,
  },
  plantCategory: {
    fontSize: 12,
    color: '#7C4D25',
    fontWeight: '600',
    marginTop: 4,
  },
  plantDescription: {
    fontSize: 14,
    color: '#333333',
    marginTop: 8,
    lineHeight: 20,
  },
  detailButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#7C4D25',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 6,
    alignSelf: 'flex-start',
    marginTop: 8,
  },
  detailButtonText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: '600',
    marginRight: 4,
  },
});


