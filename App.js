// Basit WikiPlant App - Sadece Firebase verisi
import 'react-native-url-polyfill/auto';
import React from 'react';
import { StatusBar } from 'expo-status-bar';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import { NavigationContainer } from '@react-navigation/native';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { createStackNavigator } from '@react-navigation/stack';
import { Ionicons } from '@expo/vector-icons';

// Basit sayfalar
import HomePage from './src/pages/HomePage/HomePage';
import FavoritesPage from './src/pages/FavoritesPage/FavoritesPage';
import PlantDetailPage from './src/pages/PlantDetailPage/PlantDetailPageSimple';

// Context'ler
import { FavoritesProvider } from './src/contexts/FavoritesContext';

// Constants
import { COLORS } from './src/utils/constants';

const Tab = createBottomTabNavigator();
const Stack = createStackNavigator();

// Ana Tab Navigator
function TabNavigator() {
  return (
    <Tab.Navigator
      screenOptions={({ route }) => ({
        tabBarIcon: ({ focused, color, size }) => {
          let iconName;
          if (route.name === 'Home') {
            iconName = focused ? 'leaf' : 'leaf-outline';
          } else if (route.name === 'Favorites') {
            iconName = focused ? 'heart' : 'heart-outline';
          }
          return <Ionicons name={iconName} size={size} color={color} />;
        },
        tabBarActiveTintColor: COLORS.primary,
        tabBarInactiveTintColor: 'gray',
        headerShown: false,
      })}
    >
      <Tab.Screen name="Home" component={HomePage} options={{ title: 'Ana Sayfa' }} />
      <Tab.Screen name="Favorites" component={FavoritesPage} options={{ title: 'Favoriler' }} />
    </Tab.Navigator>
  );
}

// Ana Stack Navigator
function AppNavigator() {
  return (
    <NavigationContainer>
      <Stack.Navigator screenOptions={{ headerShown: false }}>
        <Stack.Screen name="Main" component={TabNavigator} />
        <Stack.Screen name="PlantDetail" component={PlantDetailPage} />
      </Stack.Navigator>
    </NavigationContainer>
  );
}

export default function App() {
  return (
    <GestureHandlerRootView style={{ flex: 1 }}>
      <FavoritesProvider>
        <AppNavigator />
        <StatusBar style="dark" />
      </FavoritesProvider>
    </GestureHandlerRootView>
  );
}
