// Firebase imports disabled - Firebase package not installed yet
// import {
//   collection,
//   doc,
//   getDocs,
//   getDoc,
//   addDoc,
//   updateDoc,
//   deleteDoc,
//   query,
//   where,
//   orderBy,
//   limit,
//   serverTimestamp
// } from 'firebase/firestore';
// import { db } from '../config/firebase';
import { COLLECTIONS, TREATMENT_CATEGORIES, TREATMENT_SEVERITY } from '../database/schema';

class PlantTreatmentsService {
  constructor() {
    this.collectionRef = collection(db, COLLECTIONS.PLANT_TREATMENTS);
  }

  // Get all treatments for a specific plant
  async getPlantTreatments(plantId) {
    try {
      const q = query(
        this.collectionRef,
        where('plantId', '==', plantId),
        orderBy('severity', 'desc'),
        orderBy('category', 'asc')
      );

      const querySnapshot = await getDocs(q);
      const treatments = [];
      
      querySnapshot.forEach((doc) => {
        treatments.push({
          id: doc.id,
          ...doc.data()
        });
      });

      return treatments;
    } catch (error) {
      console.error('Error getting plant treatments:', error);
      throw error;
    }
  }

  // Get a specific treatment by ID
  async getTreatmentById(treatmentId) {
    try {
      const docRef = doc(db, COLLECTIONS.PLANT_TREATMENTS, treatmentId);
      const docSnap = await getDoc(docRef);

      if (docSnap.exists()) {
        return {
          id: docSnap.id,
          ...docSnap.data()
        };
      } else {
        throw new Error('Treatment not found');
      }
    } catch (error) {
      console.error('Error getting treatment:', error);
      throw error;
    }
  }

  // Get treatments by category
  async getTreatmentsByCategory(plantId, category) {
    try {
      const q = query(
        this.collectionRef,
        where('plantId', '==', plantId),
        where('category', '==', category),
        orderBy('severity', 'desc')
      );

      const querySnapshot = await getDocs(q);
      const treatments = [];
      
      querySnapshot.forEach((doc) => {
        treatments.push({
          id: doc.id,
          ...doc.data()
        });
      });

      return treatments;
    } catch (error) {
      console.error('Error getting treatments by category:', error);
      throw error;
    }
  }

  // Get treatments by severity
  async getTreatmentsBySeverity(plantId, severity) {
    try {
      const q = query(
        this.collectionRef,
        where('plantId', '==', plantId),
        where('severity', '==', severity),
        orderBy('category', 'asc')
      );

      const querySnapshot = await getDocs(q);
      const treatments = [];
      
      querySnapshot.forEach((doc) => {
        treatments.push({
          id: doc.id,
          ...doc.data()
        });
      });

      return treatments;
    } catch (error) {
      console.error('Error getting treatments by severity:', error);
      throw error;
    }
  }

  // Search treatments by problem or symptoms
  async searchTreatments(searchTerm, plantId = null) {
    try {
      let q = query(this.collectionRef);
      
      if (plantId) {
        q = query(q, where('plantId', '==', plantId));
      }

      const querySnapshot = await getDocs(q);
      const treatments = [];
      
      querySnapshot.forEach((doc) => {
        const data = doc.data();
        const problem = data.problem.toLowerCase();
        const description = data.description.toLowerCase();
        const symptoms = (data.symptoms || []).join(' ').toLowerCase();
        const search = searchTerm.toLowerCase();

        if (problem.includes(search) || 
            description.includes(search) || 
            symptoms.includes(search)) {
          treatments.push({
            id: doc.id,
            ...data
          });
        }
      });

      return treatments;
    } catch (error) {
      console.error('Error searching treatments:', error);
      throw error;
    }
  }

  // Add a new treatment (admin only)
  async addTreatment(treatmentData) {
    try {
      const docRef = await addDoc(this.collectionRef, {
        ...treatmentData,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp()
      });

      return docRef.id;
    } catch (error) {
      console.error('Error adding treatment:', error);
      throw error;
    }
  }

  // Update a treatment (admin only)
  async updateTreatment(treatmentId, treatmentData) {
    try {
      const docRef = doc(db, COLLECTIONS.PLANT_TREATMENTS, treatmentId);
      await updateDoc(docRef, {
        ...treatmentData,
        updatedAt: serverTimestamp()
      });

      return true;
    } catch (error) {
      console.error('Error updating treatment:', error);
      throw error;
    }
  }

  // Delete a treatment (admin only)
  async deleteTreatment(treatmentId) {
    try {
      const docRef = doc(db, COLLECTIONS.PLANT_TREATMENTS, treatmentId);
      await deleteDoc(docRef);
      return true;
    } catch (error) {
      console.error('Error deleting treatment:', error);
      throw error;
    }
  }

  // Add multiple treatments for a plant
  async addPlantTreatments(plantId, treatmentsData) {
    try {
      const treatmentIds = [];
      
      for (const treatmentData of treatmentsData) {
        const data = {
          ...treatmentData,
          plantId: plantId,
          createdAt: serverTimestamp(),
          updatedAt: serverTimestamp()
        };

        const docRef = await addDoc(this.collectionRef, data);
        treatmentIds.push(docRef.id);
      }

      return treatmentIds;
    } catch (error) {
      console.error('Error adding plant treatments:', error);
      throw error;
    }
  }

  // Get treatment statistics
  async getTreatmentStats(plantId) {
    try {
      const treatments = await this.getPlantTreatments(plantId);
      
      const stats = {
        totalTreatments: treatments.length,
        byCategory: {},
        bySeverity: {},
        averageEffectiveness: 0,
        organicOptions: 0,
        chemicalOptions: 0
      };

      let totalEffectiveness = 0;
      
      treatments.forEach(treatment => {
        // Count by category
        const category = treatment.category;
        stats.byCategory[category] = (stats.byCategory[category] || 0) + 1;
        
        // Count by severity
        const severity = treatment.severity;
        stats.bySeverity[severity] = (stats.bySeverity[severity] || 0) + 1;
        
        // Calculate effectiveness
        totalEffectiveness += treatment.effectiveness || 0;
        
        // Count organic vs chemical options
        if (treatment.treatment?.organic?.length > 0) {
          stats.organicOptions++;
        }
        if (treatment.treatment?.chemical?.length > 0) {
          stats.chemicalOptions++;
        }
      });

      stats.averageEffectiveness = treatments.length > 0 ? 
        totalEffectiveness / treatments.length : 0;

      return stats;
    } catch (error) {
      console.error('Error getting treatment stats:', error);
      throw error;
    }
  }

  // Get emergency treatments (high/critical severity)
  async getEmergencyTreatments(plantId) {
    try {
      const q = query(
        this.collectionRef,
        where('plantId', '==', plantId),
        where('severity', 'in', ['Yüksek', 'Kritik']),
        orderBy('severity', 'desc')
      );

      const querySnapshot = await getDocs(q);
      const treatments = [];
      
      querySnapshot.forEach((doc) => {
        treatments.push({
          id: doc.id,
          ...doc.data()
        });
      });

      return treatments;
    } catch (error) {
      console.error('Error getting emergency treatments:', error);
      throw error;
    }
  }

  // Get organic treatments only
  async getOrganicTreatments(plantId) {
    try {
      const treatments = await this.getPlantTreatments(plantId);
      
      return treatments.filter(treatment => 
        treatment.treatment?.organic && 
        treatment.treatment.organic.length > 0
      );
    } catch (error) {
      console.error('Error getting organic treatments:', error);
      throw error;
    }
  }

  // Get preventive treatments
  async getPreventiveTreatments(plantId) {
    try {
      const treatments = await this.getPlantTreatments(plantId);
      
      return treatments.filter(treatment => 
        treatment.prevention?.methods && 
        treatment.prevention.methods.length > 0
      );
    } catch (error) {
      console.error('Error getting preventive treatments:', error);
      throw error;
    }
  }

  // Update treatment effectiveness rating
  async updateEffectiveness(treatmentId, effectiveness) {
    try {
      const docRef = doc(db, COLLECTIONS.PLANT_TREATMENTS, treatmentId);
      await updateDoc(docRef, {
        effectiveness: effectiveness,
        updatedAt: serverTimestamp()
      });

      return true;
    } catch (error) {
      console.error('Error updating effectiveness:', error);
      throw error;
    }
  }

  // Add product to treatment
  async addProduct(treatmentId, product) {
    try {
      const treatment = await this.getTreatmentById(treatmentId);
      const currentProducts = treatment.products || [];
      
      const newProduct = {
        id: Date.now().toString(),
        ...product,
        addedAt: new Date().toISOString()
      };

      const updatedProducts = [...currentProducts, newProduct];
      
      const docRef = doc(db, COLLECTIONS.PLANT_TREATMENTS, treatmentId);
      await updateDoc(docRef, {
        products: updatedProducts,
        updatedAt: serverTimestamp()
      });

      return newProduct.id;
    } catch (error) {
      console.error('Error adding product:', error);
      throw error;
    }
  }

  // Remove product from treatment
  async removeProduct(treatmentId, productId) {
    try {
      const treatment = await this.getTreatmentById(treatmentId);
      const currentProducts = treatment.products || [];
      
      const updatedProducts = currentProducts.filter(product => product.id !== productId);
      
      const docRef = doc(db, COLLECTIONS.PLANT_TREATMENTS, treatmentId);
      await updateDoc(docRef, {
        products: updatedProducts,
        updatedAt: serverTimestamp()
      });

      return true;
    } catch (error) {
      console.error('Error removing product:', error);
      throw error;
    }
  }
}

export default new PlantTreatmentsService();
