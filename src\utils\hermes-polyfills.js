// Hermes JS Engine polyfills for missing Web APIs
import 'react-native-url-polyfill/auto';

// URL.protocol polyfill for Her<PERSON>
if (typeof global !== 'undefined' && !global.URL) {
  global.URL = require('react-native-url-polyfill').URL;
}

// Additional URL polyfills
if (typeof global !== 'undefined' && global.URL && !global.URL.prototype.protocol) {
  // Ensure URL.protocol is available
  const OriginalURL = global.URL;
  global.URL = class extends OriginalURL {
    constructor(url, base) {
      super(url, base);
    }
    
    get protocol() {
      const href = this.href;
      const colonIndex = href.indexOf(':');
      return colonIndex !== -1 ? href.substring(0, colonIndex + 1) : '';
    }
    
    set protocol(value) {
      // Basic protocol setter
      const href = this.href;
      const colonIndex = href.indexOf(':');
      if (colonIndex !== -1) {
        const newHref = value + href.substring(colonIndex + 1);
        try {
          const newUrl = new OriginalURL(newHref);
          Object.assign(this, newUrl);
        } catch (e) {
          // Ignore invalid URLs
        }
      }
    }
  };
}

// URLSearchParams polyfill
if (typeof global !== 'undefined' && !global.URLSearchParams) {
  global.URLSearchParams = require('react-native-url-polyfill').URLSearchParams;
}

// Fetch polyfill (if needed)
if (typeof global !== 'undefined' && !global.fetch) {
  global.fetch = require('react-native/Libraries/Network/fetch').fetch;
}

// Console polyfills for better debugging
if (typeof global !== 'undefined' && global.console) {
  // Ensure all console methods exist
  const consoleMethods = ['log', 'warn', 'error', 'info', 'debug', 'trace', 'group', 'groupEnd'];
  consoleMethods.forEach(method => {
    if (!global.console[method]) {
      global.console[method] = global.console.log || (() => {});
    }
  });
}

// Performance polyfill
if (typeof global !== 'undefined' && !global.performance) {
  global.performance = {
    now: () => Date.now(),
    mark: () => {},
    measure: () => {},
    getEntriesByName: () => [],
    getEntriesByType: () => []
  };
}

// RequestAnimationFrame polyfill
if (typeof global !== 'undefined' && !global.requestAnimationFrame) {
  global.requestAnimationFrame = (callback) => {
    return setTimeout(callback, 16); // ~60fps
  };
  
  global.cancelAnimationFrame = (id) => {
    clearTimeout(id);
  };
}

// Blob polyfill (basic)
if (typeof global !== 'undefined' && !global.Blob) {
  global.Blob = class Blob {
    constructor(parts = [], options = {}) {
      this.parts = parts;
      this.type = options.type || '';
      this.size = parts.reduce((size, part) => {
        return size + (typeof part === 'string' ? part.length : part.size || 0);
      }, 0);
    }
    
    slice(start = 0, end = this.size, contentType = '') {
      return new Blob(this.parts.slice(start, end), { type: contentType });
    }
    
    text() {
      return Promise.resolve(this.parts.join(''));
    }
    
    arrayBuffer() {
      return Promise.resolve(new ArrayBuffer(this.size));
    }
  };
}

// File polyfill (basic)
if (typeof global !== 'undefined' && !global.File) {
  global.File = class File extends global.Blob {
    constructor(parts, name, options = {}) {
      super(parts, options);
      this.name = name;
      this.lastModified = options.lastModified || Date.now();
    }
  };
}

// FormData polyfill (basic)
if (typeof global !== 'undefined' && !global.FormData) {
  global.FormData = class FormData {
    constructor() {
      this._data = new Map();
    }
    
    append(name, value, filename) {
      if (!this._data.has(name)) {
        this._data.set(name, []);
      }
      this._data.get(name).push({ value, filename });
    }
    
    delete(name) {
      this._data.delete(name);
    }
    
    get(name) {
      const values = this._data.get(name);
      return values ? values[0].value : null;
    }
    
    getAll(name) {
      const values = this._data.get(name);
      return values ? values.map(item => item.value) : [];
    }
    
    has(name) {
      return this._data.has(name);
    }
    
    set(name, value, filename) {
      this._data.set(name, [{ value, filename }]);
    }
    
    entries() {
      const entries = [];
      for (const [name, values] of this._data) {
        for (const { value } of values) {
          entries.push([name, value]);
        }
      }
      return entries[Symbol.iterator]();
    }
    
    keys() {
      return this._data.keys();
    }
    
    values() {
      const values = [];
      for (const [, valueArray] of this._data) {
        for (const { value } of valueArray) {
          values.push(value);
        }
      }
      return values[Symbol.iterator]();
    }
  };
}

console.log('🔧 Hermes polyfills loaded successfully');
