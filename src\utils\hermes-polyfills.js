// Hermes JS Engine polyfills for missing Web APIs
import 'react-native-url-polyfill/auto';

// Safe function binding helper
const safeBind = (fn, context) => {
  if (typeof fn !== 'function') return fn;
  try {
    return fn.bind(context);
  } catch (e) {
    return function(...args) {
      return fn.apply(context, args);
    };
  }
};

// URL polyfill with proper context binding
if (typeof global !== 'undefined') {
  const { URL: PolyfillURL, URLSearchParams: PolyfillURLSearchParams } = require('react-native-url-polyfill');

  // Ensure URL is properly bound
  if (!global.URL || typeof global.URL.prototype.protocol === 'undefined') {
    global.URL = class URL extends PolyfillURL {
      constructor(url, base) {
        super(url, base);
        // Bind methods to prevent illegal invocation
        this.toString = safeBind(this.toString, this);
        this.toJSON = safeBind(this.toJSON, this);
      }

      get protocol() {
        try {
          return super.protocol || '';
        } catch (e) {
          const href = this.href || '';
          const colonIndex = href.indexOf(':');
          return colonIndex !== -1 ? href.substring(0, colonIndex + 1) : '';
        }
      }

      set protocol(value) {
        try {
          super.protocol = value;
        } catch (e) {
          // Fallback for protocol setting
          console.warn('Protocol setting failed:', e.message);
        }
      }
    };
  }

  // Ensure URLSearchParams is properly bound
  if (!global.URLSearchParams) {
    global.URLSearchParams = PolyfillURLSearchParams;
  }
}

// URLSearchParams polyfill
if (typeof global !== 'undefined' && !global.URLSearchParams) {
  global.URLSearchParams = require('react-native-url-polyfill').URLSearchParams;
}

// Fetch polyfill with safe binding
if (typeof global !== 'undefined' && !global.fetch) {
  try {
    const { fetch: nativeFetch } = require('react-native/Libraries/Network/fetch');
    global.fetch = safeBind(nativeFetch, global);
  } catch (e) {
    // Fallback fetch implementation
    global.fetch = async (url, options = {}) => {
      throw new Error('Fetch not available in this environment');
    };
  }
}

// Console polyfills for better debugging with safe binding
if (typeof global !== 'undefined' && global.console) {
  const originalConsole = global.console;
  const consoleMethods = ['log', 'warn', 'error', 'info', 'debug', 'trace', 'group', 'groupEnd'];

  consoleMethods.forEach(method => {
    if (!originalConsole[method]) {
      originalConsole[method] = safeBind(originalConsole.log || (() => {}), originalConsole);
    } else {
      // Ensure existing methods are properly bound
      originalConsole[method] = safeBind(originalConsole[method], originalConsole);
    }
  });
}

// Performance polyfill with safe binding
if (typeof global !== 'undefined' && !global.performance) {
  const performanceNow = () => {
    try {
      return Date.now();
    } catch (e) {
      return 0;
    }
  };

  global.performance = {
    now: performanceNow,
    mark: () => {},
    measure: () => {},
    getEntriesByName: () => [],
    getEntriesByType: () => [],
    timing: {
      navigationStart: Date.now()
    }
  };
}

// RequestAnimationFrame polyfill
if (typeof global !== 'undefined' && !global.requestAnimationFrame) {
  global.requestAnimationFrame = (callback) => {
    return setTimeout(callback, 16); // ~60fps
  };
  
  global.cancelAnimationFrame = (id) => {
    clearTimeout(id);
  };
}

// Blob polyfill with safe binding
if (typeof global !== 'undefined' && !global.Blob) {
  global.Blob = class Blob {
    constructor(parts = [], options = {}) {
      this.parts = parts;
      this.type = options.type || '';
      this.size = parts.reduce((size, part) => {
        return size + (typeof part === 'string' ? part.length : part.size || 0);
      }, 0);

      // Bind methods to prevent illegal invocation
      this.slice = safeBind(this.slice, this);
      this.text = safeBind(this.text, this);
      this.arrayBuffer = safeBind(this.arrayBuffer, this);
    }

    slice(start = 0, end = this.size, contentType = '') {
      try {
        return new global.Blob(this.parts.slice(start, end), { type: contentType });
      } catch (e) {
        return new global.Blob([], { type: contentType });
      }
    }

    text() {
      try {
        return Promise.resolve(this.parts.join(''));
      } catch (e) {
        return Promise.resolve('');
      }
    }

    arrayBuffer() {
      try {
        return Promise.resolve(new ArrayBuffer(this.size));
      } catch (e) {
        return Promise.resolve(new ArrayBuffer(0));
      }
    }
  };
}

// File polyfill (basic)
if (typeof global !== 'undefined' && !global.File) {
  global.File = class File extends global.Blob {
    constructor(parts, name, options = {}) {
      super(parts, options);
      this.name = name;
      this.lastModified = options.lastModified || Date.now();
    }
  };
}

// FormData polyfill (basic)
if (typeof global !== 'undefined' && !global.FormData) {
  global.FormData = class FormData {
    constructor() {
      this._data = new Map();
    }
    
    append(name, value, filename) {
      if (!this._data.has(name)) {
        this._data.set(name, []);
      }
      this._data.get(name).push({ value, filename });
    }
    
    delete(name) {
      this._data.delete(name);
    }
    
    get(name) {
      const values = this._data.get(name);
      return values ? values[0].value : null;
    }
    
    getAll(name) {
      const values = this._data.get(name);
      return values ? values.map(item => item.value) : [];
    }
    
    has(name) {
      return this._data.has(name);
    }
    
    set(name, value, filename) {
      this._data.set(name, [{ value, filename }]);
    }
    
    entries() {
      const entries = [];
      for (const [name, values] of this._data) {
        for (const { value } of values) {
          entries.push([name, value]);
        }
      }
      return entries[Symbol.iterator]();
    }
    
    keys() {
      return this._data.keys();
    }
    
    values() {
      const values = [];
      for (const [, valueArray] of this._data) {
        for (const { value } of valueArray) {
          values.push(value);
        }
      }
      return values[Symbol.iterator]();
    }
  };
}

console.log('🔧 Hermes polyfills loaded successfully');
