// Firebase configuration
import { initializeApp } from 'firebase/app';
import { getFirestore } from 'firebase/firestore';
import { getAuth } from 'firebase/auth';

const firebaseConfig = {
  apiKey: "AIzaSyBvOkBX6A0Q2g0n2Z0JGeuHnBdWJ0_M5Qg",
  authDomain: "wikiplant-f1af1.firebaseapp.com",
  projectId: "wikiplant-f1af1",
  storageBucket: "wikiplant-f1af1.firebasestorage.app",
  messagingSenderId: "334334334334",
  appId: "1:334334334334:web:1234567890abcdef"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);

// Initialize Firebase services
export const db = getFirestore(app);
export const auth = getAuth(app);
export { app };

console.log('🔥 Firebase initialized successfully');
