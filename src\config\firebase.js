import { initializeApp } from 'firebase/app';
import { getFirestore } from 'firebase/firestore';
import { getStorage } from 'firebase/storage';
// Auth devre dışı - Hermes uyumsuzluğu nedeniyle
// Analytics devre dışı - Hermes uyumsuzluğu nedeniyle

// Firebase configuration
const firebaseConfig = {
  apiKey: "AIzaSyCT9Tn0Z7MVjKeonhNuovpdbXVz7syQwJ8",
  authDomain: "wikiplant-aebaf.firebaseapp.com",
  projectId: "wikiplant-aebaf",
  storageBucket: "wikiplant-aebaf.firebasestorage.app",
  messagingSenderId: "847754813681",
  appId: "1:847754813681:web:22559d4547eca2ab594950",
  measurementId: "G-27R9QPVXF6"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);

// Initialize Firebase services
export const db = getFirestore(app);

// Auth devre dışı - Hermes uyumsuzluğu nedeniyle
export const auth = null;

export const storage = getStorage(app);

// Analytics devre dışı - Hermes uyumsuzluğu nedeniyle
export const analytics = null;
export default app;
