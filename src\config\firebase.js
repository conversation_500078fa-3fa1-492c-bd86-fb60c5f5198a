// Firebase configuration - Mock mode for testing
console.log('📦 Mock Firebase Config loaded (Auth disabled)');

// Mock Firebase services to prevent auth errors
export const db = {
  isAvailable: false,
  error: 'Firebase in mock mode'
};

export const auth = {
  isAvailable: false,
  error: 'Firebase Auth in mock mode'
};

export const app = {
  isAvailable: false,
  error: 'Firebase in mock mode'
};

// Real Firebase config (commented out for now)
/*
import { initializeApp } from 'firebase/app';
import { getFirestore } from 'firebase/firestore';
import { getAuth } from 'firebase/auth';

const firebaseConfig = {
  apiKey: "your-real-api-key-here",
  authDomain: "your-project.firebaseapp.com",
  projectId: "your-project-id",
  storageBucket: "your-project.appspot.com",
  messagingSenderId: "123456789",
  appId: "1:123456789:web:abcdef123456789"
};

const app = initializeApp(firebaseConfig);
export const db = getFirestore(app);
export const auth = getAuth(app);
export { app };
*/
