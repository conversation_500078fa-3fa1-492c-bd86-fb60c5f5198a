// Firebase imports disabled - Firebase package not installed yet

// Firebase configuration
const firebaseConfig = {
  apiKey: "your-api-key-here",
  authDomain: "dallardan-bilgi.firebaseapp.com",
  projectId: "dallardan-bilgi",
  storageBucket: "dallardan-bilgi.appspot.com",
  messagingSenderId: "123456789",
  appId: "1:123456789:web:abcdef123456789"
};

// Mock Firebase for now
const mockApp = {
  isAvailable: false,
  config: firebaseConfig
};

// Mock Firebase services
export const db = {
  isAvailable: false,
  error: 'Firebase package not installed yet'
};

export const auth = {
  isAvailable: false,
  error: 'Firebase package not installed yet'
};

export const storage = {
  isAvailable: false,
  error: 'Firebase package not installed yet'
};

export default mockApp;

// When Firebase is installed, uncomment these:
// const app = initializeApp(firebaseConfig);
// export const db = getFirestore(app);
// export const auth = getAuth(app);
// export const storage = getStorage(app);
// export default app;
