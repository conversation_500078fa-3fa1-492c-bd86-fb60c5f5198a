import React, { useState, useCallback } from 'react';
import {
  View,
  Text,
  ScrollView,
  TextInput,
  TouchableOpacity,
  RefreshControl,
  SafeAreaView,
  Image,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useFocusEffect, useNavigation } from '@react-navigation/native';

import UniversalCard from '../../components/UniversalCard/UniversalCard';
import LoadingSpinner from '../../components/common/LoadingSpinner/LoadingSpinner';
import { usePlantData, useSearchPlants } from '../../hooks/usePlantData';
import { COLORS } from '../../utils/constants';
import { debounce } from '../../utils/helpers';
import styles from './HomePage.styles.js';

const HomePage = () => {
  const navigation = useNavigation();
  const [searchQuery, setSearchQuery] = useState('');
  const [isSearching, setIsSearching] = useState(false);
  
  const { plants, loading, error, refreshing, refreshPlants } = usePlantData();
  const { searchResults, loading: searchLoading, searchPlants, clearSearch } = useSearchPlants();

  // Debounced search function
  const debouncedSearch = useCallback(
    debounce((query) => {
      if (query.trim()) {
        setIsSearching(true);
        searchPlants(query);
      } else {
        setIsSearching(false);
        clearSearch();
      }
    }, 300),
    []
  );

  // Search input handler
  const handleSearchChange = (text) => {
    setSearchQuery(text);
    debouncedSearch(text);
  };

  // Clear search
  const handleClearSearch = () => {
    setSearchQuery('');
    setIsSearching(false);
    clearSearch();
  };

  // Navigate to plant detail
  const handlePlantPress = (plant) => {
    navigation.navigate('PlantDetail', { plantId: plant.id, plant });
  };

  // Refresh handler
  const handleRefresh = () => {
    refreshPlants();
    if (searchQuery) {
      handleClearSearch();
    }
  };

  // Get display data
  const getDisplayData = () => {
    if (isSearching) {
      return searchResults;
    }
    return plants;
  };

  // Render plant item
  const renderPlantItem = ({ item }) => (
    <PlantCard
      plant={item}
      onPress={handlePlantPress}
      style={styles.plantCard}
    />
  );

  // Render empty state
  const renderEmptyState = () => {
    if (loading || searchLoading) return null;

    return (
      <View style={styles.emptyState}>
        <Ionicons
          name={isSearching ? 'search' : 'leaf'}
          size={64}
          color={COLORS.gray}
        />
        <Text style={styles.emptyStateTitle}>
          {isSearching ? 'Sonuç Bulunamadı' : 'Henüz Bitki Yok'}
        </Text>
        <Text style={styles.emptyStateText}>
          {isSearching
            ? 'Arama kriterlerinizi değiştirip tekrar deneyin'
            : 'Bitkiler yüklenirken bir sorun oluştu'
          }
        </Text>
      </View>
    );
  };

  // Focus effect to refresh data when screen comes into focus
  useFocusEffect(
    useCallback(() => {
      // Refresh data if needed
    }, [])
  );

  if (loading && !refreshing) {
    return (
      <SafeAreaView style={styles.container}>
        <LoadingSpinner text="Bitkiler yükleniyor..." />
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={[COLORS.primary]}
            tintColor={COLORS.primary}
          />
        }
      >
        {/* Header */}
        <View style={styles.header}>
          <View style={styles.logoSection}>
            <Image
              source={require('../../../assets/wikiplantlogo.png')}
              style={styles.logo}
              resizeMode="contain"
            />
            <View style={styles.titleSection}>
              <Text style={styles.title}>WikiPlant</Text>
              <Text style={styles.subtitle}>Bitki Ansiklopedisi</Text>
            </View>
          </View>

          <TouchableOpacity
            style={styles.userButton}
            onPress={() => navigation.navigate('Auth')}
            activeOpacity={0.8}
          >
            <Ionicons name="person-outline" size={20} color={COLORS.white} />
          </TouchableOpacity>
        </View>

        {/* Search Bar */}
        <View style={styles.searchContainer}>
          <View style={styles.searchInputContainer}>
            <Ionicons name="search" size={20} color={COLORS.darkGray} />
            <TextInput
              style={styles.searchInput}
              placeholder="Bitki ara..."
              placeholderTextColor={COLORS.darkGray}
              value={searchQuery}
              onChangeText={handleSearchChange}
              returnKeyType="search"
            />
            {searchQuery.length > 0 && (
              <TouchableOpacity onPress={handleClearSearch}>
                <Ionicons name="close-circle" size={20} color={COLORS.darkGray} />
              </TouchableOpacity>
            )}
          </View>

          {/* Filter Button */}
          <TouchableOpacity style={styles.filterButton}>
            <Ionicons name="options-outline" size={20} color={COLORS.white} />
            <Text style={styles.filterButtonText}>Filtre</Text>
          </TouchableOpacity>
        </View>

        {/* Results Info */}
        {isSearching && (
          <View style={styles.resultsInfo}>
            <Text style={styles.resultsText}>
              {searchLoading
                ? 'Aranıyor...'
                : `${searchResults.length} sonuç bulundu`
              }
            </Text>
          </View>
        )}

        {/* Error State */}
        {error && (
          <View style={styles.errorContainer}>
            <Text style={styles.errorText}>{error}</Text>
            <TouchableOpacity style={styles.retryButton} onPress={refreshPlants}>
              <Text style={styles.retryButtonText}>Tekrar Dene</Text>
            </TouchableOpacity>
          </View>
        )}

        {/* Plant List */}
        {getDisplayData().length === 0 ? (
          renderEmptyState()
        ) : (
          getDisplayData().map((plant) => (
            <UniversalCard
              key={plant.id}
              item={plant}
              onPress={handlePlantPress}
              imageSource={{
                uri: plant.mainImage || 'https://images.unsplash.com/photo-1416879595882-3373a0480b5b?w=400&h=400&fit=crop&crop=center'
              }}
              title={plant.name}
              subtitle={plant.latinName}
              description={plant.description}
              category={plant.category}
              isNew={plant.isNew}
              isPopular={plant.isPopular}
              careInfo={plant.care}
              buttonText="Detay Oku"
            />
          ))
        )}
      </ScrollView>
    </SafeAreaView>
  );
};

export default HomePage;
