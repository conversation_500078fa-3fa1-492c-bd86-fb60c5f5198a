import React, { useState, useCallback } from 'react';
import {
  View,
  Text,
  ScrollView,
  TextInput,
  TouchableOpacity,
  RefreshControl,
  SafeAreaView,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useFocusEffect } from '@react-navigation/native';

import PlantCard from '../../components/PlantCard/PlantCard';
import LoadingSpinner from '../../components/common/LoadingSpinner/LoadingSpinner';
import { usePlantData, useSearchPlants } from '../../hooks/usePlantData';
import { COLORS } from '../../utils/constants';
import { debounce } from '../../utils/helpers';
import styles from './HomePage.styles.js';

const HomePage = ({ navigation }) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [isSearching, setIsSearching] = useState(false);
  
  const { plants, loading, error, refreshing, refreshPlants } = usePlantData();
  const { searchResults, loading: searchLoading, searchPlants, clearSearch } = useSearchPlants();

  // Debounced search function
  const debouncedSearch = useCallback(
    debounce((query) => {
      if (query.trim()) {
        setIsSearching(true);
        searchPlants(query);
      } else {
        setIsSearching(false);
        clearSearch();
      }
    }, 300),
    []
  );

  // Search input handler
  const handleSearchChange = (text) => {
    setSearchQuery(text);
    debouncedSearch(text);
  };

  // Clear search
  const handleClearSearch = () => {
    setSearchQuery('');
    setIsSearching(false);
    clearSearch();
  };

  // Navigate to plant detail
  const handlePlantPress = (plant) => {
    navigation.navigate('PlantDetail', { plantId: plant.id, plant });
  };

  // Refresh handler
  const handleRefresh = () => {
    refreshPlants();
    if (searchQuery) {
      handleClearSearch();
    }
  };

  // Get display data
  const getDisplayData = () => {
    if (isSearching) {
      return searchResults;
    }
    return plants;
  };

  // Render plant item
  const renderPlantItem = ({ item }) => (
    <PlantCard
      plant={item}
      onPress={handlePlantPress}
      style={styles.plantCard}
    />
  );

  // Render empty state
  const renderEmptyState = () => {
    if (loading || searchLoading) return null;

    return (
      <View style={styles.emptyState}>
        <Ionicons
          name={isSearching ? 'search' : 'leaf'}
          size={64}
          color={COLORS.gray}
        />
        <Text style={styles.emptyStateTitle}>
          {isSearching ? 'Sonuç Bulunamadı' : 'Henüz Bitki Yok'}
        </Text>
        <Text style={styles.emptyStateText}>
          {isSearching
            ? 'Arama kriterlerinizi değiştirip tekrar deneyin'
            : 'Bitkiler yüklenirken bir sorun oluştu'
          }
        </Text>
      </View>
    );
  };

  // Focus effect to refresh data when screen comes into focus
  useFocusEffect(
    useCallback(() => {
      // Refresh data if needed
    }, [])
  );

  if (loading && !refreshing) {
    return (
      <SafeAreaView style={styles.container}>
        <LoadingSpinner text="Bitkiler yükleniyor..." />
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={[COLORS.primary]}
            tintColor={COLORS.primary}
          />
        }
      >
        {/* Header */}
        <View style={styles.header}>
          <Text style={styles.title}>WikiPlant</Text>
          <Text style={styles.subtitle}>Bitki Ansiklopedisi</Text>
        </View>

        {/* Search Bar */}
        <View style={styles.searchContainer}>
          <View style={styles.searchInputContainer}>
            <Ionicons name="search" size={20} color={COLORS.darkGray} />
            <TextInput
              style={styles.searchInput}
              placeholder="Bitki ara..."
              placeholderTextColor={COLORS.darkGray}
              value={searchQuery}
              onChangeText={handleSearchChange}
              returnKeyType="search"
            />
            {searchQuery.length > 0 && (
              <TouchableOpacity onPress={handleClearSearch}>
                <Ionicons name="close-circle" size={20} color={COLORS.darkGray} />
              </TouchableOpacity>
            )}
          </View>
        </View>

        {/* Results Info */}
        {isSearching && (
          <View style={styles.resultsInfo}>
            <Text style={styles.resultsText}>
              {searchLoading
                ? 'Aranıyor...'
                : `${searchResults.length} sonuç bulundu`
              }
            </Text>
          </View>
        )}

        {/* Error State */}
        {error && (
          <View style={styles.errorContainer}>
            <Text style={styles.errorText}>{error}</Text>
            <TouchableOpacity style={styles.retryButton} onPress={refreshPlants}>
              <Text style={styles.retryButtonText}>Tekrar Dene</Text>
            </TouchableOpacity>
          </View>
        )}

        {/* Plant List */}
        {getDisplayData().length === 0 ? (
          renderEmptyState()
        ) : (
          getDisplayData().map((plant) => (
            <PlantCard
              key={plant.id}
              plant={plant}
              onPress={handlePlantPress}
              style={styles.plantCard}
            />
          ))
        )}
      </ScrollView>
    </SafeAreaView>
  );
};

export default HomePage;
