// Daha fazla Firebase verisi ekleme
const { initializeApp } = require('firebase/app');
const { getFirestore, collection, addDoc } = require('firebase/firestore');

const firebaseConfig = {
  apiKey: "AIzaSyCT9Tn0Z7MVjKeonhNuovpdbXVz7syQwJ8",
  authDomain: "wikiplant-aebaf.firebaseapp.com",
  projectId: "wikiplant-aebaf",
  storageBucket: "wikiplant-aebaf.firebasestorage.app",
  messagingSenderId: "847754813681",
  appId: "1:847754813681:web:22559d4547eca2ab594950",
  measurementId: "G-27R9QPVXF6"
};

const app = initializeApp(firebaseConfig);
const db = getFirestore(app);

// Daha fazla bitki verisi
const morePlants = [
  {
    name: '<PERSON><PERSON><PERSON>',
    latinName: '<PERSON>van<PERSON>la angustifolia',
    category: 'Aromatik',
    description: '<PERSON><PERSON><PERSON><PERSON><PERSON> kokusu ve mor çiçekleri ile tanınan aromatik bitki.',
    origin: '<PERSON>k<PERSON><PERSON> Bölgesi',
    lifespan: 'Çok yıllık',
    size: '30-60 cm',
    difficulty: 'Kolay',
    isNew: false,
    isPopular: true,
    mainImage: 'https://images.unsplash.com/photo-1611909023032-2d6b3134ecba?w=400&h=400&fit=crop',
    care: {
      water: 'Az sulama',
      light: 'Tam güneş',
      soil: 'İyi drene toprak',
      temperature: '15-30°C'
    }
  },
  {
    name: 'Aloe Vera',
    latinName: 'Aloe barbadensis',
    category: 'Sukulent',
    description: 'Kalın etli yaprakları olan sukulent bitki.',
    origin: 'Arap Yarımadası',
    lifespan: 'Çok yıllık',
    size: '30-100 cm',
    difficulty: 'Çok Kolay',
    isNew: false,
    isPopular: true,
    mainImage: 'https://images.unsplash.com/photo-1509587584298-0f3b3a3a1797?w=400&h=400&fit=crop',
    care: {
      water: 'Çok az sulama',
      light: 'Parlak dolaylı ışık',
      soil: 'Kaktüs toprağı',
      temperature: '18-27°C'
    }
  },
  {
    name: 'Fesleğen',
    latinName: 'Ocimum basilicum',
    category: 'Aromatik',
    description: 'Güçlü aroması olan mutfak bitkisi.',
    origin: 'Hindistan',
    lifespan: 'Yıllık',
    size: '20-60 cm',
    difficulty: 'Kolay',
    isNew: true,
    isPopular: true,
    mainImage: 'https://images.unsplash.com/photo-1618164436241-4473940d1f5c?w=400&h=400&fit=crop',
    care: {
      water: 'Düzenli sulama',
      light: 'Güneş ışığı',
      soil: 'Organik zengin toprak',
      temperature: '20-30°C'
    }
  }
];

// Makale verileri
const articles = [
  {
    title: 'Bitki Bakımının Temelleri',
    category: 'Bakım',
    summary: 'Bitkilerinizi sağlıklı tutmak için temel bakım kuralları',
    content: 'Bitki bakımının temel prensipleri: sulama, ışık, toprak ve gübreleme konularında detaylı bilgiler.',
    author: 'Bahçıvan Ahmet',
    publishDate: '2024-01-15',
    readTime: 8,
    image: 'https://images.unsplash.com/photo-1416879595882-3373a0480b5b?w=400&h=200&fit=crop',
    tags: ['bakım', 'sulama', 'budama']
  },
  {
    title: 'Doğru Sulama Teknikleri',
    category: 'Sulama',
    summary: 'Bitkilerinizi doğru şekilde sulamak için ipuçları',
    content: 'Sulama zamanı, su kalitesi, sulama miktarı ve yöntemleri hakkında detaylı bilgiler.',
    author: 'Su Uzmanı Ayşe',
    publishDate: '2024-01-20',
    readTime: 6,
    image: 'https://images.unsplash.com/photo-1416879595882-3373a0480b5b?w=400&h=200&fit=crop',
    tags: ['sulama', 'su', 'teknik']
  },
  {
    title: 'Ev İçi Bitki Seçimi',
    category: 'Ev Bitkileri',
    summary: 'Eviniz için en uygun bitkileri seçme rehberi',
    content: 'Işık durumu, bakım seviyesi ve hava kalitesi iyileştiren bitkiler hakkında kapsamlı rehber.',
    author: 'İç Mekan Uzmanı Mehmet',
    publishDate: '2024-02-01',
    readTime: 10,
    image: 'https://images.unsplash.com/photo-1416879595882-3373a0480b5b?w=400&h=200&fit=crop',
    tags: ['ev bitkisi', 'seçim', 'iç mekan']
  }
];

// Kategori verileri
const categories = [
  {
    name: 'Çiçek',
    description: 'Güzel çiçekleri olan süs bitkileri',
    icon: 'flower',
    color: '#FF6B9D',
    plantCount: 1,
    careLevel: 'Orta'
  },
  {
    name: 'Sebze',
    description: 'Yenilebilir meyve ve yaprakları olan bitkiler',
    icon: 'nutrition',
    color: '#4ECDC4',
    plantCount: 1,
    careLevel: 'Kolay'
  },
  {
    name: 'Aromatik',
    description: 'Güçlü kokuya sahip mutfak ve tıbbi bitkiler',
    icon: 'leaf',
    color: '#45B7D1',
    plantCount: 2,
    careLevel: 'Kolay'
  },
  {
    name: 'Sukulent',
    description: 'Su depolayan kalın yapraklı bitkiler',
    icon: 'cactus',
    color: '#96CEB4',
    plantCount: 1,
    careLevel: 'Çok Kolay'
  }
];

async function addMoreData() {
  try {
    console.log('🚀 Daha fazla veri ekleniyor...');
    
    // Daha fazla bitki ekle
    console.log('🌱 Ek bitkiler ekleniyor...');
    for (const plant of morePlants) {
      const docRef = await addDoc(collection(db, 'plants'), {
        ...plant,
        createdAt: new Date(),
        updatedAt: new Date()
      });
      console.log(`✅ ${plant.name} eklendi (ID: ${docRef.id})`);
    }
    
    // Makaleleri ekle
    console.log('📚 Makaleler ekleniyor...');
    for (const article of articles) {
      const docRef = await addDoc(collection(db, 'articles'), {
        ...article,
        createdAt: new Date(),
        updatedAt: new Date()
      });
      console.log(`✅ ${article.title} eklendi (ID: ${docRef.id})`);
    }
    
    // Kategorileri ekle
    console.log('📂 Kategoriler ekleniyor...');
    for (const category of categories) {
      const docRef = await addDoc(collection(db, 'categories'), {
        ...category,
        createdAt: new Date(),
        updatedAt: new Date()
      });
      console.log(`✅ ${category.name} eklendi (ID: ${docRef.id})`);
    }
    
    console.log('🎉 Tüm ek veriler eklendi!');
    console.log(`📊 Toplam: ${morePlants.length} bitki, ${articles.length} makale, ${categories.length} kategori`);
    
  } catch (error) {
    console.error('❌ Hata:', error);
  }
}

addMoreData();
