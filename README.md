# WikiPlant - Dallardan Bilgi

React Native tabanlı bitki ansiklopedisi ve bakım rehberi uygulaması.

## 🚀 API Tabanlı Veri Yönetimi

Bu proje artık gelişmiş API servisi ve caching sistemi ile çalışmaktadır. Web admin paneli kaldırılmış, bunun yerine external API kullanılmaktadır.

## 📱 Uygulama Özellikleri

### 🌱 Bitki Ansiklopedisi
- Kapsamlı bitki veritabanı
- Çiçek, sebze, aromatik ve sukulent kategorileri
- Detaylı bakım bilgileri
- Latin isimleri ve köken bilgileri

### 📊 Gelişmiş API Servisi
- **Firebase Firestore** entegrasyonu
- **Akıllı Caching** sistemi (Memory + Persistent)
- **Real-time** veri senkronizasyonu
- **Batch Operations** toplu veri işleme
- **Error Handling** kapsamlı hata yönetimi

### 🔄 Caching Sistemi
- **Memory Cache**: <PERSON>ı<PERSON><PERSON><PERSON> eri<PERSON><PERSON> i<PERSON>in RAM'de saklama
- **Persistent Cache**: AsyncStorage ile kalıcı saklama
- **TTL (Time To Live)**: Otomatik cache süresi yönetimi
- **Smart Invalidation**: Akıllı cache temizleme
- **Preloading**: Önemli verilerin önyüklenmesi

## 🛠️ Teknolojiler

- **React Native** - Mobil uygulama framework
- **Expo** - Geliştirme platformu
- **Firebase Firestore** - NoSQL veritabanı
- **AsyncStorage** - Yerel veri saklama
- **React Navigation** - Sayfa yönlendirme

## 📦 Kurulum

```bash
# Proje dizinine git
cd WikiPlant

# Bağımlılıkları yükle
npm install

# Uygulamayı başlat
npm start

# Android için
npm run android

# iOS için
npm run ios
```

## 🔥 Firebase Yapılandırması

Firebase config dosyası (`src/config/firebase.js`) güncellenmiştir:

```javascript
const firebaseConfig = {
  apiKey: "AIzaSyCT9Tn0Z7MVjKeonhNuovpdbXVz7syQwJ8",
  authDomain: "wikiplant-aebaf.firebaseapp.com",
  projectId: "wikiplant-aebaf",
  storageBucket: "wikiplant-aebaf.firebasestorage.app",
  messagingSenderId: "847754813681",
  appId: "1:847754813681:web:22559d4547eca2ab594950",
  measurementId: "G-27R9QPVXF6"
};
```

## 📊 API Servisi Kullanımı

### Temel Kullanım

```javascript
import apiService from './src/services/apiService';

// Tüm bitkileri getir (cache ile)
const plants = await apiService.getPlants();

// Belirli bir bitkiyi getir
const plant = await apiService.getPlant(plantId);

// Kategoriye göre bitkileri getir
const categoryPlants = await apiService.getPlantsByCategory('Çiçek');

// Arama yap
const searchResults = await apiService.searchPlants('gül');
```

### Cache Yönetimi

```javascript
// Cache bilgilerini al
const cacheInfo = await apiService.getCacheInfo();

// Cache'i temizle
await apiService.clearCache();

// Önemli verileri önyükle
await apiService.preloadData();
```

## 🌐 External Admin API

Veri yükleme için ayrı bir REST API oluşturulmuştur.

### API Kurulumu

```bash
# API dizinine git
cd external-admin

# Package.json'u kopyala
cp ../external-admin-package.json package.json

# Bağımlılıkları yükle
npm install

# API'yi başlat
npm start
```

### API Endpoint'leri

```
GET  /api/health          - Sistem durumu
POST /api/load-plants     - Bitki verilerini yükle
POST /api/load-articles   - Makale verilerini yükle  
POST /api/load-categories - Kategori verilerini yükle
POST /api/load-all        - Tüm verileri yükle
GET  /api/plants          - Tüm bitkileri listele
POST /api/upload-plants   - Custom bitki verisi yükle
```

### Web Admin Panel

HTML tabanlı admin paneli (`admin-panel.html`):

1. **API Durumu**: Real-time API bağlantı kontrolü
2. **Veri Yükleme**: Tek tıkla veri yükleme butonları
3. **Log Sistemi**: İşlem geçmişi ve hata takibi
4. **Modern UI**: Responsive ve kullanıcı dostu tasarım

```bash
# Admin panelini aç
open admin-panel.html
# veya
python -m http.server 8000
```

## 📁 Proje Yapısı

```
WikiPlant/
├── src/
│   ├── services/
│   │   ├── apiService.js        # Ana API servisi
│   │   ├── cacheService.js      # Cache yönetimi
│   │   └── dataLoaderAPI.js     # Veri yükleme API'si
│   ├── config/
│   │   └── firebase.js          # Firebase yapılandırması
│   ├── hooks/
│   │   └── usePlantData.js      # API entegrasyonlu hooks
│   └── ...
├── external-admin-api.js        # External REST API
├── external-admin-package.json  # API dependencies
├── admin-panel.html             # Web admin paneli
└── README.md
```

## 🔧 Cache Yapılandırması

Cache TTL (Time To Live) ayarları:

```javascript
const cacheTTL = {
  SHORT: 2 * 60 * 1000,      // 2 dakika
  MEDIUM: 10 * 60 * 1000,    // 10 dakika  
  LONG: 30 * 60 * 1000,      // 30 dakika
  VERY_LONG: 60 * 60 * 1000  // 1 saat
};
```

## 🚀 Veri Yükleme Süreci

### 1. External API ile Yükleme

```bash
# API'yi başlat
node external-admin-api.js

# Admin panelini aç
open admin-panel.html

# Veri yükleme butonlarını kullan
```

### 2. Programatik Yükleme

```javascript
import dataLoaderAPI from './src/services/dataLoaderAPI';

// Tüm verileri yükle
await dataLoaderAPI.loadAllData();

// Sadece bitkileri yükle
await dataLoaderAPI.loadPlantsData();

// Cache'i temizle
await dataLoaderAPI.clearAllCache();
```

## 📊 Veritabanı Koleksiyonları

- **plants** - Bitki bilgileri
- **plant_stages** - Bitki gelişim aşamaları
- **plant_treatments** - Bitki bakım önerileri
- **articles** - Bilgi makaleleri
- **categories** - Bitki kategorileri
- **users** - Kullanıcı verileri

## 🔒 Güvenlik

- Firebase Security Rules
- API endpoint koruması
- Input validation
- Error handling
- Rate limiting (önerilir)

## 📄 Lisans

Bu proje MIT lisansı altında lisanslanmıştır.

## 👥 Katkıda Bulunma

1. Projeyi fork edin
2. Feature branch oluşturun
3. Değişikliklerinizi commit edin
4. Pull Request oluşturun

## 📞 İletişim

Proje Sahibi - [@tarikerdal](https://github.com/tarikerdal)
