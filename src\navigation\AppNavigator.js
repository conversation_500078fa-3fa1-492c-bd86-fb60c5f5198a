import React from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createStackNavigator } from '@react-navigation/stack';

import TabNavigator from './TabNavigator';
import PlantDetailPage from '../pages/PlantDetailPage/PlantDetailPage';
import AuthPage from '../pages/AuthPage/AuthPage';
import ArticleDetailPage from '../pages/ArticleDetailPage/ArticleDetailPage';
import { COLORS, FONT_SIZES } from '../utils/constants';

const Stack = createStackNavigator();

const AppNavigator = () => {
  return (
    <NavigationContainer>
      <Stack.Navigator
        screenOptions={{
          headerStyle: {
            backgroundColor: COLORS.white,
            shadowColor: COLORS.shadow,
            shadowOffset: {
              width: 0,
              height: 2,
            },
            shadowOpacity: 0.1,
            shadowRadius: 4,
            elevation: 3,
          },
          headerTitleStyle: {
            fontSize: FONT_SIZES.large,
            fontWeight: '600',
            color: COLORS.primary,
          },
          headerTintColor: COLORS.primary,
          headerBackTitleVisible: false,
          cardStyle: {
            backgroundColor: COLORS.background,
          },
        }}
      >
        <Stack.Screen
          name="Main"
          component={TabNavigator}
          options={{
            headerShown: false,
          }}
        />
        <Stack.Screen
          name="PlantDetail"
          component={PlantDetailPage}
          options={{
            title: 'Bitki Detayı',
            headerBackTitle: 'Geri',
          }}
        />
        <Stack.Screen
          name="Auth"
          component={AuthPage}
          options={{
            headerShown: false,
          }}
        />
        <Stack.Screen
          name="ArticleDetail"
          component={ArticleDetailPage}
          options={{
            headerShown: false,
          }}
        />
      </Stack.Navigator>
    </NavigationContainer>
  );
};

export default AppNavigator;
