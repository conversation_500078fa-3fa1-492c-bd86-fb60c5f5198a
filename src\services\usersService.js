// Firebase imports disabled - Firebase package not installed yet
// import {
//   collection,
//   doc,
//   getDoc,
//   setDoc,
//   updateDoc,
//   deleteDoc,
//   query,
//   where,
//   getDocs,
//   serverTimestamp,
//   arrayUnion,
//   arrayRemove
// } from 'firebase/firestore';
// import {
//   createUserWithEmailAndPassword,
//   signInWithEmailAndPassword,
//   signOut,
//   updateProfile,
//   sendPasswordResetEmail,
//   deleteUser
// } from 'firebase/auth';
// import { auth, db } from '../config/firebase';
import { COLLECTIONS } from '../database/schema';

class UsersService {
  constructor() {
    // Firebase disabled - mock service
    console.log('📦 Mock UsersService initialized');
  }

  // Register new user
  async registerUser(email, password, displayName) {
    try {
      // Create user with Firebase Auth
      const userCredential = await createUserWithEmailAndPassword(auth, email, password);
      const user = userCredential.user;

      // Update user profile
      await updateProfile(user, {
        displayName: displayName
      });

      // Create user document in Firestore
      await setDoc(doc(db, COLLECTIONS.USERS, user.uid), {
        id: user.uid,
        email: user.email,
        displayName: displayName,
        photoURL: user.photoURL || null,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp(),
        preferences: {
          language: 'tr',
          notifications: true,
          theme: 'light'
        },
        favorites: [],
        userPlants: []
      });

      return {
        uid: user.uid,
        email: user.email,
        displayName: displayName
      };
    } catch (error) {
      console.error('Error registering user:', error);
      throw error;
    }
  }

  // Sign in user
  async signInUser(email, password) {
    try {
      const userCredential = await signInWithEmailAndPassword(auth, email, password);
      const user = userCredential.user;

      // Update last login time
      await this.updateUserData(user.uid, {
        lastLoginAt: serverTimestamp()
      });

      return {
        uid: user.uid,
        email: user.email,
        displayName: user.displayName
      };
    } catch (error) {
      console.error('Error signing in user:', error);
      throw error;
    }
  }

  // Sign out user
  async signOutUser() {
    try {
      await signOut(auth);
    } catch (error) {
      console.error('Error signing out user:', error);
      throw error;
    }
  }

  // Get user data
  async getUserData(userId) {
    try {
      const docRef = doc(db, COLLECTIONS.USERS, userId);
      const docSnap = await getDoc(docRef);

      if (docSnap.exists()) {
        return {
          id: docSnap.id,
          ...docSnap.data()
        };
      } else {
        throw new Error('User not found');
      }
    } catch (error) {
      console.error('Error getting user data:', error);
      throw error;
    }
  }

  // Update user data
  async updateUserData(userId, userData) {
    try {
      const docRef = doc(db, COLLECTIONS.USERS, userId);
      await updateDoc(docRef, {
        ...userData,
        updatedAt: serverTimestamp()
      });
      return true;
    } catch (error) {
      console.error('Error updating user data:', error);
      throw error;
    }
  }

  // Update user preferences
  async updateUserPreferences(userId, preferences) {
    try {
      const docRef = doc(db, COLLECTIONS.USERS, userId);
      await updateDoc(docRef, {
        preferences: preferences,
        updatedAt: serverTimestamp()
      });
      return true;
    } catch (error) {
      console.error('Error updating user preferences:', error);
      throw error;
    }
  }

  // Add plant to favorites
  async addToFavorites(userId, plantId) {
    try {
      const docRef = doc(db, COLLECTIONS.USERS, userId);
      await updateDoc(docRef, {
        favorites: arrayUnion(plantId),
        updatedAt: serverTimestamp()
      });
      return true;
    } catch (error) {
      console.error('Error adding to favorites:', error);
      throw error;
    }
  }

  // Remove plant from favorites
  async removeFromFavorites(userId, plantId) {
    try {
      const docRef = doc(db, COLLECTIONS.USERS, userId);
      await updateDoc(docRef, {
        favorites: arrayRemove(plantId),
        updatedAt: serverTimestamp()
      });
      return true;
    } catch (error) {
      console.error('Error removing from favorites:', error);
      throw error;
    }
  }

  // Add user plant
  async addUserPlant(userId, plantData) {
    try {
      const docRef = doc(db, COLLECTIONS.USERS, userId);
      const userPlant = {
        id: Date.now().toString(),
        ...plantData,
        addedAt: serverTimestamp()
      };

      await updateDoc(docRef, {
        userPlants: arrayUnion(userPlant),
        updatedAt: serverTimestamp()
      });

      return userPlant.id;
    } catch (error) {
      console.error('Error adding user plant:', error);
      throw error;
    }
  }

  // Update user plant
  async updateUserPlant(userId, plantId, plantData) {
    try {
      const userData = await this.getUserData(userId);
      const userPlants = userData.userPlants || [];
      
      const updatedPlants = userPlants.map(plant => 
        plant.id === plantId 
          ? { ...plant, ...plantData, updatedAt: serverTimestamp() }
          : plant
      );

      const docRef = doc(db, COLLECTIONS.USERS, userId);
      await updateDoc(docRef, {
        userPlants: updatedPlants,
        updatedAt: serverTimestamp()
      });

      return true;
    } catch (error) {
      console.error('Error updating user plant:', error);
      throw error;
    }
  }

  // Remove user plant
  async removeUserPlant(userId, plantId) {
    try {
      const userData = await this.getUserData(userId);
      const userPlants = userData.userPlants || [];
      
      const updatedPlants = userPlants.filter(plant => plant.id !== plantId);

      const docRef = doc(db, COLLECTIONS.USERS, userId);
      await updateDoc(docRef, {
        userPlants: updatedPlants,
        updatedAt: serverTimestamp()
      });

      return true;
    } catch (error) {
      console.error('Error removing user plant:', error);
      throw error;
    }
  }

  // Get user's favorite plants
  async getUserFavorites(userId) {
    try {
      const userData = await this.getUserData(userId);
      return userData.favorites || [];
    } catch (error) {
      console.error('Error getting user favorites:', error);
      throw error;
    }
  }

  // Get user's plants
  async getUserPlants(userId) {
    try {
      const userData = await this.getUserData(userId);
      return userData.userPlants || [];
    } catch (error) {
      console.error('Error getting user plants:', error);
      throw error;
    }
  }

  // Send password reset email
  async sendPasswordReset(email) {
    try {
      await sendPasswordResetEmail(auth, email);
      return true;
    } catch (error) {
      console.error('Error sending password reset:', error);
      throw error;
    }
  }

  // Delete user account
  async deleteUserAccount(userId) {
    try {
      // Delete user document from Firestore
      const docRef = doc(db, COLLECTIONS.USERS, userId);
      await deleteDoc(docRef);

      // Delete user from Firebase Auth
      const user = auth.currentUser;
      if (user && user.uid === userId) {
        await deleteUser(user);
      }

      return true;
    } catch (error) {
      console.error('Error deleting user account:', error);
      throw error;
    }
  }

  // Check if user exists
  async checkUserExists(email) {
    try {
      const q = query(this.collectionRef, where('email', '==', email));
      const querySnapshot = await getDocs(q);
      return !querySnapshot.empty;
    } catch (error) {
      console.error('Error checking user exists:', error);
      throw error;
    }
  }

  // Get current user
  getCurrentUser() {
    return auth.currentUser;
  }

  // Listen to auth state changes
  onAuthStateChanged(callback) {
    return auth.onAuthStateChanged(callback);
  }
}

export default new UsersService();
