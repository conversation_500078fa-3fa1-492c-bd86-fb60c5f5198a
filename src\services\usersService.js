// Firebase Auth devre dışı - Hermes uyumsuzluğu nedeniyle
// Bu servis şimdilik kullanılmıyor

console.warn('UsersService: Firebase Auth devre dışı - AsyncStorage kullanın');

// Mock UsersService - Firebase Auth devre dışı
class UsersService {
  constructor() {
    console.warn('UsersService: Mock implementation - Firebase Auth disabled');
  }

  // Mock register user
  async registerUser(email, password, displayName) {
    console.warn('UsersService.registerUser: Mock implementation');
    return {
      uid: 'mock-uid',
      email: email,
      displayName: displayName
    };
  }

  // Mock sign in user
  async signInUser(email, password) {
    console.warn('UsersService.signInUser: Mock implementation');
    return {
      uid: 'mock-uid',
      email: email,
      displayName: 'Mock User'
    };
  }

  // Mock sign out user
  async signOutUser() {
    console.warn('UsersService.signOutUser: Mock implementation');
    return true;
  }

  // Mock get user data
  async getUserData(userId) {
    console.warn('UsersService.getUserData: Mock implementation');
    return {
      id: userId,
      email: '<EMAIL>',
      displayName: 'Mock User',
      favorites: [],
      userPlants: []
    };
  }

  // Mock methods - Firebase Auth devre dışı
  async updateUserData(userId, userData) { return true; }
  async updateUserPreferences(userId, preferences) { return true; }
  async addToFavorites(userId, plantId) { return true; }
  async removeFromFavorites(userId, plantId) { return true; }
  async addUserPlant(userId, plantData) { return 'mock-plant-id'; }
  async updateUserPlant(userId, plantId, plantData) { return true; }
  async removeUserPlant(userId, plantId) { return true; }
  async getUserFavorites(userId) { return []; }
  async getUserPlants(userId) { return []; }
  async sendPasswordReset(email) { return true; }
  async deleteUserAccount(userId) { return true; }
  async checkUserExists(email) { return false; }
  getCurrentUser() { return null; }
  onAuthStateChanged(callback) { return () => {}; }
}

export default new UsersService();
