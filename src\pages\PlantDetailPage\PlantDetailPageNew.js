import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  Image,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  ActivityIndicator,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

// Firebase
import { collection, getDocs, query, where } from 'firebase/firestore';
import { db } from '../../config/firebase';

// Context
import { useFavorites } from '../../contexts/FavoritesContext';

const PlantDetailPage = ({ route, navigation }) => {
  const { plantId, plant: initialPlant } = route.params;
  const [plant] = useState(initialPlant);
  const [plantStages, setPlantStages] = useState([]);
  const [currentStage, setCurrentStage] = useState(1);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const { favorites, addFavorite, removeFavorite } = useFavorites();
  const isFavorite = favorites.some(fav => fav.id === plantId);

  // Firebase'den bitki aşamalarını çek
  const loadPlantStages = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const stagesCollection = collection(db, 'plantstages');
      const stagesQuery = query(stagesCollection, where('plantId', '==', plantId));
      const stagesSnapshot = await getDocs(stagesQuery);
      const stagesData = stagesSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));
      
      setPlantStages(stagesData.sort((a, b) => a.order - b.order));
      console.log('✅ Plant stages loaded:', stagesData.length);
    } catch (err) {
      console.error('❌ Error loading plant stages:', err);
      setError('Aşama bilgileri yüklenirken hata oluştu');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (plantId) {
      loadPlantStages();
    }
  }, [plantId]);

  // Favori toggle
  const handleFavoritePress = () => {
    if (isFavorite) {
      removeFavorite(plantId);
    } else {
      addFavorite(plant);
    }
  };

  // Aşama değiştir
  const handleStageChange = (stageIndex) => {
    setCurrentStage(stageIndex + 1);
  };

  // Mevcut aşama bilgisi
  const getCurrentStageInfo = () => {
    return plantStages[currentStage - 1] || null;
  };

  // Geri git
  const handleGoBack = () => {
    navigation.goBack();
  };

  if (!plant) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.errorContainer}>
          <Ionicons name="leaf" size={64} color="#CCCCCC" />
          <Text style={styles.errorText}>Bitki bulunamadı</Text>
        </View>
      </SafeAreaView>
    );
  }

  const currentStageInfo = getCurrentStageInfo();

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity style={styles.backButton} onPress={handleGoBack}>
            <Ionicons name="arrow-back" size={24} color="#FFFFFF" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>{plant.name}</Text>
          <TouchableOpacity style={styles.favoriteButton} onPress={handleFavoritePress}>
            <Ionicons 
              name={isFavorite ? "heart" : "heart-outline"} 
              size={24} 
              color={isFavorite ? "#FF6B6B" : "#FFFFFF"} 
            />
          </TouchableOpacity>
        </View>

        {/* Plant Image */}
        <View style={styles.imageContainer}>
          <Image
            source={{
              uri: currentStageInfo?.image || plant.mainImage || 'https://images.unsplash.com/photo-1416879595882-3373a0480b5b?w=400&h=400&fit=crop'
            }}
            style={styles.plantImage}
            resizeMode="cover"
          />
        </View>

        {/* Plant Info */}
        <View style={styles.content}>
          <Text style={styles.plantName}>{plant.name}</Text>
          <Text style={styles.latinName}>{plant.latinName}</Text>
          <Text style={styles.category}>{plant.category}</Text>
          <Text style={styles.description}>{plant.description}</Text>

          {/* Stages */}
          {plantStages.length > 0 && (
            <View style={styles.stagesSection}>
              <Text style={styles.sectionTitle}>Büyüme Aşamaları</Text>
              
              {/* Stage Indicators */}
              <View style={styles.stageIndicators}>
                {plantStages.map((stage, index) => (
                  <TouchableOpacity
                    key={stage.id}
                    style={[
                      styles.stageIndicator,
                      currentStage === index + 1 && styles.activeStageIndicator
                    ]}
                    onPress={() => handleStageChange(index)}
                  >
                    <Text style={[
                      styles.stageNumber,
                      currentStage === index + 1 && styles.activeStageNumber
                    ]}>
                      {index + 1}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>

              {/* Current Stage Info */}
              {currentStageInfo && (
                <View style={styles.stageInfo}>
                  <Text style={styles.stageName}>{currentStageInfo.name}</Text>
                  <Text style={styles.stageDescription}>{currentStageInfo.description}</Text>
                </View>
              )}

              {loading && (
                <View style={styles.loadingContainer}>
                  <ActivityIndicator size="small" color="#7C4D25" />
                  <Text style={styles.loadingText}>Aşama bilgileri yükleniyor...</Text>
                </View>
              )}
            </View>
          )}

          {/* Care Info */}
          {plant.care && (
            <View style={styles.careSection}>
              <Text style={styles.sectionTitle}>Bakım Bilgileri</Text>
              <View style={styles.careGrid}>
                {plant.care.water && (
                  <View style={styles.careItem}>
                    <Ionicons name="water" size={20} color="#7C4D25" />
                    <Text style={styles.careLabel}>Sulama</Text>
                    <Text style={styles.careValue}>{plant.care.water}</Text>
                  </View>
                )}
                {plant.care.light && (
                  <View style={styles.careItem}>
                    <Ionicons name="sunny" size={20} color="#FC7138" />
                    <Text style={styles.careLabel}>Işık</Text>
                    <Text style={styles.careValue}>{plant.care.light}</Text>
                  </View>
                )}
                {plant.care.soil && (
                  <View style={styles.careItem}>
                    <Ionicons name="earth" size={20} color="#7C4D25" />
                    <Text style={styles.careLabel}>Toprak</Text>
                    <Text style={styles.careValue}>{plant.care.soil}</Text>
                  </View>
                )}
                {plant.care.temperature && (
                  <View style={styles.careItem}>
                    <Ionicons name="thermometer" size={20} color="#FF6B6B" />
                    <Text style={styles.careLabel}>Sıcaklık</Text>
                    <Text style={styles.careValue}>{plant.care.temperature}</Text>
                  </View>
                )}
              </View>
            </View>
          )}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

// Basit stiller
const styles = {
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  scrollView: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: '#FC7138',
    paddingHorizontal: 16,
    paddingVertical: 12,
    paddingTop: 40,
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    flex: 1,
    fontSize: 18,
    fontWeight: 'bold',
    color: '#FFFFFF',
    textAlign: 'center',
  },
  favoriteButton: {
    padding: 8,
  },
  imageContainer: {
    height: 250,
  },
  plantImage: {
    width: '100%',
    height: '100%',
  },
  content: {
    padding: 16,
  },
  plantName: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333333',
    marginBottom: 4,
  },
  latinName: {
    fontSize: 16,
    fontStyle: 'italic',
    color: '#CCCCCC',
    marginBottom: 8,
  },
  category: {
    fontSize: 14,
    color: '#7C4D25',
    fontWeight: '600',
    marginBottom: 12,
  },
  description: {
    fontSize: 16,
    color: '#333333',
    lineHeight: 24,
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333333',
    marginBottom: 16,
  },
  stagesSection: {
    marginBottom: 24,
  },
  stageIndicators: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginBottom: 16,
  },
  stageIndicator: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#F5F5F5',
    justifyContent: 'center',
    alignItems: 'center',
    marginHorizontal: 4,
  },
  activeStageIndicator: {
    backgroundColor: '#7C4D25',
  },
  stageNumber: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#CCCCCC',
  },
  activeStageNumber: {
    color: '#FFFFFF',
  },
  stageInfo: {
    backgroundColor: '#FFFFFF',
    padding: 16,
    borderRadius: 12,
  },
  stageName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333333',
    marginBottom: 8,
  },
  stageDescription: {
    fontSize: 14,
    color: '#333333',
    lineHeight: 20,
  },
  loadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
  },
  loadingText: {
    marginLeft: 8,
    color: '#CCCCCC',
  },
  careSection: {
    marginBottom: 24,
  },
  careGrid: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
  },
  careItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  careLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333333',
    marginLeft: 12,
    flex: 1,
  },
  careValue: {
    fontSize: 14,
    color: '#CCCCCC',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
  },
  errorText: {
    fontSize: 16,
    color: '#CCCCCC',
    marginTop: 16,
    textAlign: 'center',
  },
};

export default PlantDetailPage;
