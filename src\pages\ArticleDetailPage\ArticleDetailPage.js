import React from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  Image,
  Share,
  Linking,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation, useRoute } from '@react-navigation/native';
import styles from './ArticleDetailPage.styles';
import { COLORS } from '../../utils/constants';

const ArticleDetailPage = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const { article } = route.params || {};

  const handleWhatsAppShare = async () => {
    const message = `${article.title}\n\n${article.content}\n\nWikiPlant uygulamasından paylaşıldı.`;
    const whatsappUrl = `whatsapp://send?text=${encodeURIComponent(message)}`;
    
    try {
      const supported = await Linking.canOpenURL(whatsappUrl);
      if (supported) {
        await Linking.openURL(whatsappUrl);
      } else {
        // WhatsApp yüklü değilse normal paylaşım
        await Share.share({
          message: message,
          title: article.title,
        });
      }
    } catch (error) {
      console.error('Paylaşım hatası:', error);
    }
  };

  const handleGeneralShare = async () => {
    try {
      await Share.share({
        message: `${article.title}\n\n${article.content}\n\nWikiPlant uygulamasından paylaşıldı.`,
        title: article.title,
      });
    } catch (error) {
      console.error('Paylaşım hatası:', error);
    }
  };

  if (!article) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>Makale bulunamadı</Text>
          <TouchableOpacity 
            style={styles.backButton}
            onPress={() => navigation.goBack()}
          >
            <Text style={styles.backButtonText}>Geri Dön</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity 
          style={styles.headerButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color={COLORS.white} />
        </TouchableOpacity>
        
        <Text style={styles.headerTitle}>Makale Detayı</Text>
        
        <View style={styles.shareButtons}>
          <TouchableOpacity 
            style={styles.headerButton}
            onPress={handleWhatsAppShare}
          >
            <Ionicons name="logo-whatsapp" size={24} color={COLORS.white} />
          </TouchableOpacity>
          
          <TouchableOpacity 
            style={styles.headerButton}
            onPress={handleGeneralShare}
          >
            <Ionicons name="share-outline" size={24} color={COLORS.white} />
          </TouchableOpacity>
        </View>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Article Image (if exists) */}
        {article.image && (
          <View style={styles.imageContainer}>
            <Image
              source={{ uri: article.image }}
              style={styles.articleImage}
              resizeMode="cover"
            />
          </View>
        )}

        {/* Article Content */}
        <View style={styles.articleContainer}>
          <Text style={styles.articleTitle}>{article.title}</Text>
          
          {article.author && (
            <View style={styles.authorSection}>
              <Ionicons name="person-outline" size={16} color={COLORS.darkGray} />
              <Text style={styles.authorText}>Yazar: {article.author}</Text>
            </View>
          )}
          
          {article.date && (
            <View style={styles.dateSection}>
              <Ionicons name="calendar-outline" size={16} color={COLORS.darkGray} />
              <Text style={styles.dateText}>{article.date}</Text>
            </View>
          )}

          <Text style={styles.articleContent}>{article.content}</Text>

          {/* Tags (if exists) */}
          {article.tags && article.tags.length > 0 && (
            <View style={styles.tagsContainer}>
              <Text style={styles.tagsTitle}>Etiketler:</Text>
              <View style={styles.tagsWrapper}>
                {article.tags.map((tag, index) => (
                  <View key={index} style={styles.tag}>
                    <Text style={styles.tagText}>#{tag}</Text>
                  </View>
                ))}
              </View>
            </View>
          )}
        </View>
      </ScrollView>

      {/* Bottom Share Bar */}
      <View style={styles.bottomBar}>
        <TouchableOpacity 
          style={styles.whatsappButton}
          onPress={handleWhatsAppShare}
        >
          <Ionicons name="logo-whatsapp" size={20} color={COLORS.white} />
          <Text style={styles.whatsappButtonText}>WhatsApp'ta Paylaş</Text>
        </TouchableOpacity>
        
        <TouchableOpacity 
          style={styles.shareButton}
          onPress={handleGeneralShare}
        >
          <Ionicons name="share-outline" size={20} color={COLORS.white} />
          <Text style={styles.shareButtonText}>Paylaş</Text>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
};

export default ArticleDetailPage;
