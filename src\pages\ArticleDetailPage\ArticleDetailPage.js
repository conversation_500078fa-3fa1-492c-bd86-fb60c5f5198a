import React from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  Image,
  Share,
  Linking,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation, useRoute } from '@react-navigation/native';
import * as Print from 'expo-print';
import * as Sharing from 'expo-sharing';
import styles from './ArticleDetailPage.styles';
import { COLORS } from '../../utils/constants';

const ArticleDetailPage = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const { article } = route.params || {};

  const handleWhatsAppShare = async () => {
    const message = `${article.title}\n\n${article.content}\n\nWikiPlant uygulamasından paylaşıldı.`;
    const whatsappUrl = `whatsapp://send?text=${encodeURIComponent(message)}`;
    
    try {
      const supported = await Linking.canOpenURL(whatsappUrl);
      if (supported) {
        await Linking.openURL(whatsappUrl);
      } else {
        // WhatsApp yüklü değilse normal paylaşım
        await Share.share({
          message: message,
          title: article.title,
        });
      }
    } catch (error) {
      console.error('Paylaşım hatası:', error);
    }
  };

  const handleGeneralShare = async () => {
    try {
      await Share.share({
        message: `${article.title}\n\n${article.content}\n\nWikiPlant uygulamasından paylaşıldı.`,
        title: article.title,
      });
    } catch (error) {
      console.error('Paylaşım hatası:', error);
    }
  };

  const createPDFTemplate = (article) => {
    return `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <title>${article.title} - WikiPlant</title>
          <style>
            body {
              font-family: 'Arial', sans-serif;
              line-height: 1.6;
              color: #333;
              max-width: 800px;
              margin: 0 auto;
              padding: 20px;
            }
            .header {
              text-align: center;
              border-bottom: 3px solid #FC7138;
              padding-bottom: 20px;
              margin-bottom: 30px;
            }
            .logo {
              color: #FC7138;
              font-size: 28px;
              font-weight: bold;
              margin-bottom: 10px;
            }
            .subtitle {
              color: #666;
              font-size: 16px;
            }
            .article-title {
              font-size: 24px;
              font-weight: bold;
              color: #7C4D25;
              margin: 30px 0 20px 0;
              text-align: center;
            }
            .meta-info {
              background: #f5f5f5;
              padding: 15px;
              border-radius: 8px;
              margin-bottom: 25px;
            }
            .meta-item {
              margin-bottom: 5px;
              font-size: 14px;
              color: #666;
            }
            .content {
              font-size: 16px;
              line-height: 1.8;
              text-align: justify;
              margin-bottom: 30px;
            }
            .tags {
              background: #e8f5e8;
              padding: 15px;
              border-radius: 8px;
              margin-top: 25px;
            }
            .tags-title {
              font-weight: bold;
              color: #4CAF50;
              margin-bottom: 10px;
            }
            .tag {
              display: inline-block;
              background: #4CAF50;
              color: white;
              padding: 4px 8px;
              border-radius: 12px;
              font-size: 12px;
              margin-right: 8px;
              margin-bottom: 5px;
            }
            .footer {
              text-align: center;
              margin-top: 40px;
              padding-top: 20px;
              border-top: 1px solid #ddd;
              color: #666;
              font-size: 14px;
            }
          </style>
        </head>
        <body>
          <div class="header">
            <div class="logo">WikiPlant</div>
            <div class="subtitle">Bitki Ansiklopedisi</div>
          </div>

          <h1 class="article-title">${article.title}</h1>

          <div class="meta-info">
            ${article.author ? `<div class="meta-item"><strong>Yazar:</strong> ${article.author}</div>` : ''}
            ${article.date ? `<div class="meta-item"><strong>Tarih:</strong> ${article.date}</div>` : ''}
            <div class="meta-item"><strong>Kategori:</strong> ${article.category || 'Genel'}</div>
          </div>

          <div class="content">
            ${article.content}
          </div>

          ${article.tags && article.tags.length > 0 ? `
            <div class="tags">
              <div class="tags-title">Etiketler:</div>
              ${article.tags.map(tag => `<span class="tag">#${tag}</span>`).join('')}
            </div>
          ` : ''}

          <div class="footer">
            Bu makale WikiPlant uygulamasından oluşturulmuştur.<br>
            © ${new Date().getFullYear()} WikiPlant - Bitki Ansiklopedisi
          </div>
        </body>
      </html>
    `;
  };

  const handlePDFShare = async () => {
    try {
      const htmlContent = createPDFTemplate(article);
      const { uri } = await Print.printToFileAsync({
        html: htmlContent,
        base64: false
      });

      await Sharing.shareAsync(uri, {
        mimeType: 'application/pdf',
        dialogTitle: `${article.title} - WikiPlant PDF`
      });
    } catch (error) {
      console.error('PDF paylaşım hatası:', error);
    }
  };

  const handleDownloadPDF = async () => {
    try {
      const htmlContent = createPDFTemplate(article);
      const { uri } = await Print.printToFileAsync({
        html: htmlContent,
        base64: false
      });

      // PDF indirildi bildirimi
      alert('PDF başarıyla oluşturuldu ve cihazınıza kaydedildi!');
    } catch (error) {
      console.error('PDF indirme hatası:', error);
    }
  };

  if (!article) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>Makale bulunamadı</Text>
          <TouchableOpacity 
            style={styles.backButton}
            onPress={() => navigation.goBack()}
          >
            <Text style={styles.backButtonText}>Geri Dön</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color={COLORS.white} />
        </TouchableOpacity>

        <View style={styles.logoSection}>
          <Image
            source={require('../../../assets/wikiplantlogo.png')}
            style={styles.logo}
            resizeMode="contain"
          />
        </View>

        <TouchableOpacity
          style={styles.shareButton}
          onPress={handlePDFShare}
        >
          <Ionicons name="share-outline" size={20} color={COLORS.white} />
          <Text style={styles.shareButtonText}>Paylaş</Text>
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Article Image (if exists) */}
        {article.image && (
          <View style={styles.imageContainer}>
            <Image
              source={{ uri: article.image }}
              style={styles.articleImage}
              resizeMode="cover"
            />
          </View>
        )}

        {/* Article Content */}
        <View style={styles.articleContainer}>
          <Text style={styles.articleTitle}>{article.title}</Text>
          
          {article.author && (
            <View style={styles.authorSection}>
              <Ionicons name="person-outline" size={16} color={COLORS.darkGray} />
              <Text style={styles.authorText}>Yazar: {article.author}</Text>
            </View>
          )}
          
          {article.date && (
            <View style={styles.dateSection}>
              <Ionicons name="calendar-outline" size={16} color={COLORS.darkGray} />
              <Text style={styles.dateText}>{article.date}</Text>
            </View>
          )}

          <Text style={styles.articleContent}>{article.content}</Text>

          {/* Tags (if exists) */}
          {article.tags && article.tags.length > 0 && (
            <View style={styles.tagsContainer}>
              <Text style={styles.tagsTitle}>Etiketler:</Text>
              <View style={styles.tagsWrapper}>
                {article.tags.map((tag, index) => (
                  <View key={index} style={styles.tag}>
                    <Text style={styles.tagText}>#{tag}</Text>
                  </View>
                ))}
              </View>
            </View>
          )}
        </View>
      </ScrollView>


    </SafeAreaView>
  );
};

export default ArticleDetailPage;
