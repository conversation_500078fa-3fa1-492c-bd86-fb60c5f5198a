import { collection, addDoc, serverTimestamp } from 'firebase/firestore';
import { db } from '../config/firebase';
import { COLLECTIONS } from './schema';
import { SAMPLE_PLANTS, KNOWLEDGE_BASE_ARTICLES } from '../utils/constants';

// Migration script to populate Firebase with initial data
class DatabaseMigration {
  
  // Migrate sample plants to Firebase
  async migratePlants() {
    try {
      console.log('Starting plants migration...');
      const plantsCollection = collection(db, COLLECTIONS.PLANTS);
      
      for (const plant of SAMPLE_PLANTS) {
        const plantData = {
          ...plant,
          difficulty: this.mapDifficulty(plant.care),
          tags: this.generatePlantTags(plant),
          createdAt: serverTimestamp(),
          updatedAt: serverTimestamp(),
          createdBy: 'system',
          likes: Math.floor(Math.random() * 100),
          views: Math.floor(Math.random() * 1000)
        };

        const docRef = await addDoc(plantsCollection, plantData);
        console.log(`Plant "${plant.name}" migrated with ID: ${docRef.id}`);
      }
      
      console.log('Plants migration completed!');
    } catch (error) {
      console.error('Error migrating plants:', error);
      throw error;
    }
  }

  // Migrate sample articles to Firebase
  async migrateArticles() {
    try {
      console.log('Starting articles migration...');
      const articlesCollection = collection(db, COLLECTIONS.ARTICLES);
      
      for (const article of KNOWLEDGE_BASE_ARTICLES) {
        const articleData = {
          ...article,
          author: 'Dallardan Bilgi Editörü',
          authorId: 'system',
          isPublished: true,
          isFeatured: Math.random() > 0.7, // 30% chance of being featured
          readTime: this.calculateReadTime(article.content),
          publishedAt: serverTimestamp(),
          createdAt: serverTimestamp(),
          updatedAt: serverTimestamp(),
          likes: Math.floor(Math.random() * 50),
          views: Math.floor(Math.random() * 500),
          comments: []
        };

        const docRef = await addDoc(articlesCollection, articleData);
        console.log(`Article "${article.title}" migrated with ID: ${docRef.id}`);
      }
      
      console.log('Articles migration completed!');
    } catch (error) {
      console.error('Error migrating articles:', error);
      throw error;
    }
  }

  // Migrate categories to Firebase
  async migrateCategories() {
    try {
      console.log('Starting categories migration...');
      const categoriesCollection = collection(db, COLLECTIONS.CATEGORIES);
      
      const categories = [
        {
          name: 'Çiçek',
          description: 'Süs amaçlı çiçekli bitkiler',
          icon: 'flower',
          color: '#FF6B9D',
          order: 1,
          isActive: true
        },
        {
          name: 'Sebze',
          description: 'Yenilebilir sebze bitkileri',
          icon: 'nutrition',
          color: '#4CAF50',
          order: 2,
          isActive: true
        },
        {
          name: 'Aromatik',
          description: 'Kokulu ve şifalı bitkiler',
          icon: 'leaf',
          color: '#8BC34A',
          order: 3,
          isActive: true
        },
        {
          name: 'Sukulent',
          description: 'Az su isteyen etli bitkiler',
          icon: 'cactus',
          color: '#FF9800',
          order: 4,
          isActive: true
        },
        {
          name: 'İç Mekan',
          description: 'Ev içi yetiştirilen bitkiler',
          icon: 'home',
          color: '#9C27B0',
          order: 5,
          isActive: true
        },
        {
          name: 'Dış Mekan',
          description: 'Bahçe ve balkon bitkileri',
          icon: 'sunny',
          color: '#2196F3',
          order: 6,
          isActive: true
        }
      ];

      for (const category of categories) {
        const categoryData = {
          ...category,
          plantCount: 0,
          articleCount: 0,
          createdAt: serverTimestamp(),
          updatedAt: serverTimestamp()
        };

        const docRef = await addDoc(categoriesCollection, categoryData);
        console.log(`Category "${category.name}" migrated with ID: ${docRef.id}`);
      }
      
      console.log('Categories migration completed!');
    } catch (error) {
      console.error('Error migrating categories:', error);
      throw error;
    }
  }

  // Migrate app settings to Firebase
  async migrateAppSettings() {
    try {
      console.log('Starting app settings migration...');
      const appSettingsCollection = collection(db, COLLECTIONS.APP_SETTINGS);
      
      const settings = {
        version: '1.0.0',
        minVersion: '1.0.0',
        maintenanceMode: false,
        featuredPlants: [],
        featuredArticles: [],
        announcements: [
          {
            id: 'welcome',
            title: 'Dallardan Bilgi\'ye Hoş Geldiniz!',
            message: 'Bitki dünyasının kapılarını aralayın ve yeşil dostlarınızla dolu bir yolculuğa çıkın.',
            type: 'info',
            startDate: serverTimestamp(),
            endDate: null,
            isActive: true
          }
        ],
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp()
      };

      const docRef = await addDoc(appSettingsCollection, settings);
      console.log(`App settings migrated with ID: ${docRef.id}`);
      
      console.log('App settings migration completed!');
    } catch (error) {
      console.error('Error migrating app settings:', error);
      throw error;
    }
  }

  // Run complete migration
  async runMigration() {
    try {
      console.log('🚀 Starting complete database migration...');
      
      await this.migrateCategories();
      await this.migratePlants();
      await this.migrateArticles();
      await this.migrateAppSettings();
      
      console.log('✅ Complete database migration finished successfully!');
    } catch (error) {
      console.error('❌ Migration failed:', error);
      throw error;
    }
  }

  // Helper function to map care difficulty
  mapDifficulty(care) {
    const wateringFreq = care.watering.toLowerCase();
    const lightReq = care.light.toLowerCase();
    
    if (wateringFreq.includes('az') || wateringFreq.includes('çok az')) {
      return 'Kolay';
    } else if (wateringFreq.includes('günlük') || lightReq.includes('doğrudan')) {
      return 'Orta';
    } else {
      return 'Kolay';
    }
  }

  // Helper function to generate plant tags
  generatePlantTags(plant) {
    const tags = [plant.category.toLowerCase()];
    
    if (plant.isNew) tags.push('yeni');
    if (plant.isPopular) tags.push('popüler');
    
    // Add care-based tags
    if (plant.care.watering.toLowerCase().includes('az')) {
      tags.push('az-su');
    }
    if (plant.care.light.toLowerCase().includes('doğrudan')) {
      tags.push('güneş-sever');
    }
    if (plant.care.light.toLowerCase().includes('gölge')) {
      tags.push('gölge-sever');
    }
    
    return tags;
  }

  // Helper function to calculate read time
  calculateReadTime(content) {
    const wordsPerMinute = 200;
    const wordCount = content.split(' ').length;
    return Math.ceil(wordCount / wordsPerMinute);
  }
}

export default new DatabaseMigration();

// Usage example:
// import migration from './src/database/migration.js';
// migration.runMigration();
