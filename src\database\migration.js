// Firebase imports disabled - Firebase package not installed yet
// import { collection, addDoc, serverTimestamp } from 'firebase/firestore';
// import { db } from '../config/firebase';
import { COLLECTIONS, STAGE_NAMES, TREATMENT_CATEGORIES, TREATMENT_SEVERITY } from './schema';
import { SAMPLE_PLANTS, KNOWLEDGE_BASE_ARTICLES } from '../utils/constants';

// Migration script to populate Firebase with initial data
class DatabaseMigration {
  
  // Migrate sample plants to Firebase
  async migratePlants() {
    try {
      console.log('Starting plants migration...');
      // Firebase disabled - mock migration
      console.log('📦 Mock migration: plantsCollection');

      for (const plant of SAMPLE_PLANTS) {
        // Enhanced plant data structure
        const plantData = {
          ...plant,
          difficulty: this.mapDifficulty(plant.care),
          care: this.enhanceCareData(plant.care),
          tags: this.generatePlantTags(plant),
          stageIds: [], // Will be populated after stages migration
          treatmentIds: [], // Will be populated after treatments migration
          createdAt: serverTimestamp(),
          updatedAt: serverTimestamp(),
          createdBy: 'system',
          likes: Math.floor(Math.random() * 100),
          views: Math.floor(Math.random() * 1000),
          averageGrowthTime: this.calculateAverageGrowthTime(plant),
          successRate: Math.floor(Math.random() * 30) + 70 // 70-100%
        };

        const docRef = await addDoc(plantsCollection, plantData);
        console.log(`Plant "${plant.name}" migrated with ID: ${docRef.id}`);

        // Migrate stages and treatments for this plant
        await this.migratePlantStages(docRef.id, plant);
        await this.migratePlantTreatments(docRef.id, plant);
      }

      console.log('Plants migration completed!');
    } catch (error) {
      console.error('Error migrating plants:', error);
      throw error;
    }
  }

  // Migrate plant stages
  async migratePlantStages(plantId, plantData) {
    try {
      const stagesCollection = collection(db, COLLECTIONS.PLANT_STAGES);
      const stageIds = [];

      // Generate comprehensive stages for each plant
      const stages = this.generatePlantStages(plantData);

      for (let i = 0; i < stages.length; i++) {
        const stageData = {
          ...stages[i],
          plantId: plantId,
          stageNumber: i + 1,
          createdAt: serverTimestamp(),
          updatedAt: serverTimestamp()
        };

        const docRef = await addDoc(stagesCollection, stageData);
        stageIds.push(docRef.id);
      }

      // Update plant with stage IDs
      const plantsCollection = collection(db, COLLECTIONS.PLANTS);
      const plantDocRef = doc(plantsCollection, plantId);
      await updateDoc(plantDocRef, { stageIds: stageIds });

      console.log(`  - ${stages.length} stages migrated for plant ${plantData.name}`);
      return stageIds;
    } catch (error) {
      console.error('Error migrating plant stages:', error);
      throw error;
    }
  }

  // Migrate plant treatments
  async migratePlantTreatments(plantId, plantData) {
    try {
      const treatmentsCollection = collection(db, COLLECTIONS.PLANT_TREATMENTS);
      const treatmentIds = [];

      // Generate comprehensive treatments for each plant
      const treatments = this.generatePlantTreatments(plantData);

      for (const treatment of treatments) {
        const treatmentData = {
          ...treatment,
          plantId: plantId,
          createdAt: serverTimestamp(),
          updatedAt: serverTimestamp()
        };

        const docRef = await addDoc(treatmentsCollection, treatmentData);
        treatmentIds.push(docRef.id);
      }

      // Update plant with treatment IDs
      const plantsCollection = collection(db, COLLECTIONS.PLANTS);
      const plantDocRef = doc(plantsCollection, plantId);
      await updateDoc(plantDocRef, { treatmentIds: treatmentIds });

      console.log(`  - ${treatments.length} treatments migrated for plant ${plantData.name}`);
      return treatmentIds;
    } catch (error) {
      console.error('Error migrating plant treatments:', error);
      throw error;
    }
  }

  // Migrate sample articles to Firebase
  async migrateArticles() {
    try {
      console.log('Starting articles migration...');
      const articlesCollection = collection(db, COLLECTIONS.ARTICLES);
      
      for (const article of KNOWLEDGE_BASE_ARTICLES) {
        const articleData = {
          ...article,
          author: 'Dallardan Bilgi Editörü',
          authorId: 'system',
          isPublished: true,
          isFeatured: Math.random() > 0.7, // 30% chance of being featured
          readTime: this.calculateReadTime(article.content),
          publishedAt: serverTimestamp(),
          createdAt: serverTimestamp(),
          updatedAt: serverTimestamp(),
          likes: Math.floor(Math.random() * 50),
          views: Math.floor(Math.random() * 500),
          comments: []
        };

        const docRef = await addDoc(articlesCollection, articleData);
        console.log(`Article "${article.title}" migrated with ID: ${docRef.id}`);
      }
      
      console.log('Articles migration completed!');
    } catch (error) {
      console.error('Error migrating articles:', error);
      throw error;
    }
  }

  // Migrate categories to Firebase
  async migrateCategories() {
    try {
      console.log('Starting categories migration...');
      const categoriesCollection = collection(db, COLLECTIONS.CATEGORIES);
      
      const categories = [
        {
          name: 'Çiçek',
          description: 'Süs amaçlı çiçekli bitkiler',
          icon: 'flower',
          color: '#FF6B9D',
          order: 1,
          isActive: true
        },
        {
          name: 'Sebze',
          description: 'Yenilebilir sebze bitkileri',
          icon: 'nutrition',
          color: '#4CAF50',
          order: 2,
          isActive: true
        },
        {
          name: 'Aromatik',
          description: 'Kokulu ve şifalı bitkiler',
          icon: 'leaf',
          color: '#8BC34A',
          order: 3,
          isActive: true
        },
        {
          name: 'Sukulent',
          description: 'Az su isteyen etli bitkiler',
          icon: 'cactus',
          color: '#FF9800',
          order: 4,
          isActive: true
        },
        {
          name: 'İç Mekan',
          description: 'Ev içi yetiştirilen bitkiler',
          icon: 'home',
          color: '#9C27B0',
          order: 5,
          isActive: true
        },
        {
          name: 'Dış Mekan',
          description: 'Bahçe ve balkon bitkileri',
          icon: 'sunny',
          color: '#2196F3',
          order: 6,
          isActive: true
        }
      ];

      for (const category of categories) {
        const categoryData = {
          ...category,
          plantCount: 0,
          articleCount: 0,
          createdAt: serverTimestamp(),
          updatedAt: serverTimestamp()
        };

        const docRef = await addDoc(categoriesCollection, categoryData);
        console.log(`Category "${category.name}" migrated with ID: ${docRef.id}`);
      }
      
      console.log('Categories migration completed!');
    } catch (error) {
      console.error('Error migrating categories:', error);
      throw error;
    }
  }

  // Migrate app settings to Firebase
  async migrateAppSettings() {
    try {
      console.log('Starting app settings migration...');
      const appSettingsCollection = collection(db, COLLECTIONS.APP_SETTINGS);
      
      const settings = {
        version: '1.0.0',
        minVersion: '1.0.0',
        maintenanceMode: false,
        featuredPlants: [],
        featuredArticles: [],
        announcements: [
          {
            id: 'welcome',
            title: 'Dallardan Bilgi\'ye Hoş Geldiniz!',
            message: 'Bitki dünyasının kapılarını aralayın ve yeşil dostlarınızla dolu bir yolculuğa çıkın.',
            type: 'info',
            startDate: serverTimestamp(),
            endDate: null,
            isActive: true
          }
        ],
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp()
      };

      const docRef = await addDoc(appSettingsCollection, settings);
      console.log(`App settings migrated with ID: ${docRef.id}`);
      
      console.log('App settings migration completed!');
    } catch (error) {
      console.error('Error migrating app settings:', error);
      throw error;
    }
  }

  // Run complete migration
  async runMigration() {
    try {
      console.log('🚀 Starting complete database migration...');
      
      await this.migrateCategories();
      await this.migratePlants();
      await this.migrateArticles();
      await this.migrateAppSettings();
      
      console.log('✅ Complete database migration finished successfully!');
    } catch (error) {
      console.error('❌ Migration failed:', error);
      throw error;
    }
  }

  // Helper function to map care difficulty
  mapDifficulty(care) {
    const wateringFreq = care.watering.toLowerCase();
    const lightReq = care.light.toLowerCase();
    
    if (wateringFreq.includes('az') || wateringFreq.includes('çok az')) {
      return 'Kolay';
    } else if (wateringFreq.includes('günlük') || lightReq.includes('doğrudan')) {
      return 'Orta';
    } else {
      return 'Kolay';
    }
  }

  // Helper function to generate plant tags
  generatePlantTags(plant) {
    const tags = [plant.category.toLowerCase()];
    
    if (plant.isNew) tags.push('yeni');
    if (plant.isPopular) tags.push('popüler');
    
    // Add care-based tags
    if (plant.care.watering.toLowerCase().includes('az')) {
      tags.push('az-su');
    }
    if (plant.care.light.toLowerCase().includes('doğrudan')) {
      tags.push('güneş-sever');
    }
    if (plant.care.light.toLowerCase().includes('gölge')) {
      tags.push('gölge-sever');
    }
    
    return tags;
  }

  // Helper function to calculate read time
  calculateReadTime(content) {
    const wordsPerMinute = 200;
    const wordCount = content.split(' ').length;
    return Math.ceil(wordCount / wordsPerMinute);
  }

  // Enhanced care data structure
  enhanceCareData(basicCare) {
    return {
      watering: {
        frequency: basicCare.watering,
        amount: this.mapWateringAmount(basicCare.watering),
        method: 'Üstten',
        notes: `${basicCare.watering} sulama yapın`
      },
      light: {
        type: basicCare.light,
        duration: this.mapLightDuration(basicCare.light),
        direction: this.mapLightDirection(basicCare.light),
        notes: `${basicCare.light} ışık gereksinimi`
      },
      soil: {
        type: this.mapSoilType(basicCare.soil),
        ph: 'Nötr (6.5-7.5)',
        drainage: basicCare.soil.includes('drene') ? 'İyi drene' : 'Orta drene',
        nutrients: 'Organik madde açısından zengin'
      },
      temperature: {
        min: this.extractMinTemp(basicCare.temperature),
        max: this.extractMaxTemp(basicCare.temperature),
        optimal: basicCare.temperature,
        winter: 'Serin tutun',
        summer: 'Sıcaktan koruyun'
      },
      humidity: {
        level: 'Orta',
        percentage: '%40-60',
        methods: ['Püskürtme', 'Su kabı']
      },
      fertilizer: {
        type: 'Sıvı gübre',
        frequency: 'Ayda 1',
        season: 'İlkbahar-Yaz',
        npk: '10-10-10'
      }
    };
  }

  // Generate comprehensive plant stages
  generatePlantStages(plant) {
    const baseStages = [
      {
        name: STAGE_NAMES.SEED,
        description: 'Tohumun toprakta beklemesi ve çimlenmeye hazırlanması',
        duration: { min: 3, max: 14, average: 7, unit: 'gün' },
        images: [{
          url: `https://via.placeholder.com/400x300/7C4D25/FFFFFF?text=${plant.name}+Tohum`,
          caption: 'Tohum aşaması',
          isPrimary: true
        }],
        careInstructions: {
          watering: 'Nemli tutun ama aşırı sulamayın',
          light: 'Dolaylı ışık',
          temperature: '18-22°C',
          humidity: 'Yüksek nem',
          fertilizer: 'Gübre vermeyin',
          specialCare: ['Toprağı nemli tutun', 'Sıcaklığı sabit tutun']
        },
        tips: [
          {
            title: 'Tohum Kalitesi',
            description: 'Kaliteli ve taze tohum kullanın',
            importance: 'Yüksek'
          },
          {
            title: 'Toprak Hazırlığı',
            description: 'Tohum ekimi için uygun toprak karışımı hazırlayın',
            importance: 'Yüksek'
          }
        ],
        commonProblems: [
          {
            problem: 'Tohum çimlenmiyor',
            solution: 'Nem ve sıcaklık kontrolü yapın',
            prevention: 'Kaliteli tohum ve uygun koşullar sağlayın'
          }
        ],
        nextStageIndicators: ['İlk filizin görünmesi', 'Topraktan çıkış']
      },
      {
        name: STAGE_NAMES.GERMINATION,
        description: 'İlk filizin çıkması ve ilk yaprakların oluşması',
        duration: { min: 5, max: 10, average: 7, unit: 'gün' },
        images: [{
          url: `https://via.placeholder.com/400x300/C9E265/FFFFFF?text=${plant.name}+Cimlenme`,
          caption: 'Çimlenme aşaması',
          isPrimary: true
        }],
        careInstructions: {
          watering: 'Düzenli ama az miktarda',
          light: 'Parlak dolaylı ışık',
          temperature: '20-24°C',
          humidity: 'Orta-yüksek nem',
          fertilizer: 'Çok hafif sıvı gübre',
          specialCare: ['Ani sıcaklık değişimlerinden koruyun', 'Hava akımından uzak tutun']
        },
        tips: [
          {
            title: 'Işık Yönetimi',
            description: 'Doğrudan güneş ışığından koruyun',
            importance: 'Yüksek'
          }
        ],
        commonProblems: [
          {
            problem: 'Fide solgunlaşıyor',
            solution: 'Sulama miktarını ayarlayın',
            prevention: 'Düzenli nem kontrolü yapın'
          }
        ],
        nextStageIndicators: ['İlk gerçek yaprakların çıkması', 'Kök sisteminin gelişmesi']
      }
    ];

    // Add more stages based on plant type
    if (plant.category === 'Çiçek') {
      baseStages.push(
        {
          name: STAGE_NAMES.BUDDING,
          description: 'Çiçek tomurcuklarının oluşması',
          duration: { min: 14, max: 28, average: 21, unit: 'gün' },
          images: [{
            url: `https://via.placeholder.com/400x300/FC7138/FFFFFF?text=${plant.name}+Tomurcuk`,
            caption: 'Tomurcuklanma aşaması',
            isPrimary: true
          }],
          careInstructions: {
            watering: 'Düzenli sulama',
            light: 'Bol ışık',
            temperature: '18-25°C',
            humidity: 'Orta nem',
            fertilizer: 'Fosfor açısından zengin gübre',
            specialCare: ['Tomurcukları zarar görmekten koruyun']
          },
          tips: [
            {
              title: 'Çiçeklenme Desteği',
              description: 'Fosfor içeriği yüksek gübre kullanın',
              importance: 'Yüksek'
            }
          ],
          commonProblems: [
            {
              problem: 'Tomurcuklar dökülüyor',
              solution: 'Sulama ve ışık koşullarını kontrol edin',
              prevention: 'Stres faktörlerini minimize edin'
            }
          ],
          nextStageIndicators: ['Tomurcukların şişmesi', 'Renk değişimi']
        },
        {
          name: STAGE_NAMES.FLOWERING,
          description: 'Çiçeklerin açması ve tam çiçeklenme',
          duration: { min: 21, max: 60, average: 35, unit: 'gün' },
          images: [{
            url: `https://via.placeholder.com/400x300/FF69B4/FFFFFF?text=${plant.name}+Cicek`,
            caption: 'Çiçeklenme aşaması',
            isPrimary: true
          }],
          careInstructions: {
            watering: 'Düzenli ve bol sulama',
            light: 'Maksimum ışık',
            temperature: '20-26°C',
            humidity: 'Orta nem',
            fertilizer: 'Dengeli NPK gübresi',
            specialCare: ['Solmuş çiçekleri temizleyin', 'Destek çubukları kullanın']
          },
          tips: [
            {
              title: 'Çiçek Bakımı',
              description: 'Solmuş çiçekleri düzenli olarak temizleyin',
              importance: 'Orta'
            }
          ],
          commonProblems: [
            {
              problem: 'Çiçekler erken soluyor',
              solution: 'Su ve besin kontrolü yapın',
              prevention: 'Düzenli bakım ve uygun koşullar'
            }
          ],
          nextStageIndicators: ['Çiçeklerin solması', 'Tohum oluşumu']
        }
      );
    }

    return baseStages;
  }

  // Generate comprehensive plant treatments
  generatePlantTreatments(plant) {
    const treatments = [];

    // Common treatments for all plants
    treatments.push({
      problem: 'Yaprak Sararmasi',
      category: TREATMENT_CATEGORIES.NUTRITION,
      severity: TREATMENT_SEVERITY.MEDIUM,
      description: 'Yaprakların sarı renk alması, genellikle beslenme eksikliği veya aşırı sulamadan kaynaklanır.',
      symptoms: ['Yaprakların sarı renk alması', 'Yaprak dökümü', 'Büyümenin yavaşlaması'],
      causes: ['Azot eksikliği', 'Aşırı sulama', 'Kök çürümesi', 'Yaşlanma'],
      prevention: {
        methods: ['Düzenli gübreleme', 'Uygun sulama', 'İyi drenaj'],
        frequency: 'Aylık kontrol',
        seasonality: 'Tüm yıl'
      },
      treatment: {
        immediate: ['Sararmış yaprakları temizleyin', 'Sulama miktarını azaltın'],
        longTerm: ['Dengeli gübre uygulayın', 'Toprak kalitesini iyileştirin'],
        organic: ['Kompost kullanın', 'Solucan gübresi ekleyin'],
        chemical: ['NPK gübresi uygulayın', 'Demir şelatı verin']
      },
      products: [
        {
          name: 'Sıvı NPK Gübresi',
          type: 'Kimyasal',
          dosage: '10ml/1L su',
          application: 'Yapraktan ve kökten',
          frequency: '15 günde bir',
          price: { min: 15, max: 25, currency: 'TL' },
          availability: 'Yaygın',
          link: ''
        },
        {
          name: 'Organik Kompost',
          type: 'Organik',
          dosage: '2-3 cm kalınlığında',
          application: 'Toprak yüzeyine',
          frequency: 'Mevsimlik',
          price: { min: 20, max: 40, currency: 'TL' },
          availability: 'Yaygın',
          link: ''
        }
      ],
      timing: {
        bestTime: 'Sabah erken saatler',
        avoidTime: 'Öğlen güneşi',
        frequency: '2 haftada bir',
        duration: '4-6 hafta'
      },
      images: [
        {
          url: 'https://via.placeholder.com/400x300/FFD700/000000?text=Sari+Yaprak',
          caption: 'Sararmış yaprak örneği',
          type: 'problem'
        }
      ],
      effectiveness: 8,
      difficulty: 'Kolay'
    });

    // Plant-specific treatments
    if (plant.category === 'Çiçek') {
      treatments.push({
        problem: 'Yaprak Biti',
        category: TREATMENT_CATEGORIES.PEST,
        severity: TREATMENT_SEVERITY.MEDIUM,
        description: 'Küçük yeşil veya siyah böcekler yapraklarda koloniler oluşturur.',
        symptoms: ['Yapraklarda küçük böcekler', 'Yaprak deformasyonu', 'Bal maddesi'],
        causes: ['Kuru hava', 'Aşırı azotlu gübre', 'Hijyen eksikliği'],
        prevention: {
          methods: ['Düzenli kontrol', 'Nem seviyesi ayarı', 'Temiz çevre'],
          frequency: 'Haftalık',
          seasonality: 'İlkbahar-Yaz'
        },
        treatment: {
          immediate: ['Etkilenen kısımları yıkayın', 'İzole edin'],
          longTerm: ['Doğal düşmanları destekleyin', 'Çevre koşullarını iyileştirin'],
          organic: ['Sabunlu su', 'Neem yağı', 'Uğur böceği salımı'],
          chemical: ['İmidakloprid', 'Asetamiprid']
        },
        products: [
          {
            name: 'Neem Yağı',
            type: 'Organik',
            dosage: '5ml/1L su',
            application: 'Püskürtme',
            frequency: '3 günde bir',
            price: { min: 25, max: 35, currency: 'TL' },
            availability: 'Özel',
            link: ''
          }
        ],
        timing: {
          bestTime: 'Akşam saatleri',
          avoidTime: 'Güneşli saatler',
          frequency: 'Haftada 2-3 kez',
          duration: '2-3 hafta'
        },
        images: [],
        effectiveness: 7,
        difficulty: 'Orta'
      });
    }

    if (plant.category === 'Sukulent') {
      treatments.push({
        problem: 'Kök Çürümesi',
        category: TREATMENT_CATEGORIES.DISEASE,
        severity: TREATMENT_SEVERITY.HIGH,
        description: 'Aşırı sulama sonucu köklerin çürümesi ve bitkinin ölümü.',
        symptoms: ['Yumuşak gövde', 'Kötü koku', 'Yaprak dökümü'],
        causes: ['Aşırı sulama', 'Kötü drenaj', 'Soğuk hava'],
        prevention: {
          methods: ['Az sulama', 'İyi drenaj', 'Uygun saksı'],
          frequency: 'Sürekli dikkat',
          seasonality: 'Özellikle kış'
        },
        treatment: {
          immediate: ['Sulamayı durdurun', 'Çürük kısımları kesin'],
          longTerm: ['Yeni toprak', 'Drenaj iyileştirme'],
          organic: ['Tarçın tozu', 'Kömür tozu'],
          chemical: ['Fungisit uygulama']
        },
        products: [
          {
            name: 'Kaktüs Toprağı',
            type: 'Organik',
            dosage: 'Tam değişim',
            application: 'Yeniden dikim',
            frequency: 'Tek seferlik',
            price: { min: 10, max: 20, currency: 'TL' },
            availability: 'Yaygın',
            link: ''
          }
        ],
        timing: {
          bestTime: 'İlkbahar',
          avoidTime: 'Kış ayları',
          frequency: 'Acil müdahale',
          duration: 'Değişken'
        },
        images: [],
        effectiveness: 6,
        difficulty: 'Zor'
      });
    }

    return treatments;
  }

  // Helper functions for care data enhancement
  mapWateringAmount(frequency) {
    if (frequency.includes('az') || frequency.includes('çok az')) return 'Az';
    if (frequency.includes('bol') || frequency.includes('yoğun')) return 'Bol';
    return 'Orta';
  }

  mapLightDuration(lightType) {
    if (lightType.includes('doğrudan')) return '6-8 saat';
    if (lightType.includes('parlak')) return '4-6 saat';
    if (lightType.includes('gölge')) return '2-4 saat';
    return '4-6 saat';
  }

  mapLightDirection(lightType) {
    if (lightType.includes('doğrudan')) return 'Güney';
    if (lightType.includes('parlak')) return 'Doğu';
    if (lightType.includes('gölge')) return 'Kuzey';
    return 'Doğu';
  }

  mapSoilType(soilDesc) {
    if (soilDesc.includes('kaktüs')) return 'Kaktüs toprağı';
    if (soilDesc.includes('organik')) return 'Organik toprak';
    if (soilDesc.includes('kum')) return 'Kum karışımı';
    return 'Organik toprak';
  }

  extractMinTemp(tempRange) {
    const match = tempRange.match(/(\d+)-(\d+)/);
    return match ? parseInt(match[1]) : 15;
  }

  extractMaxTemp(tempRange) {
    const match = tempRange.match(/(\d+)-(\d+)/);
    return match ? parseInt(match[2]) : 25;
  }

  calculateAverageGrowthTime(plant) {
    // Calculate based on plant category and care requirements
    const baseTime = {
      'Çiçek': 120,
      'Sebze': 90,
      'Aromatik': 60,
      'Sukulent': 180
    };

    return baseTime[plant.category] || 100;
  }
}

export default new DatabaseMigration();

// Usage example:
// import migration from './src/database/migration.js';
// migration.runMigration();
