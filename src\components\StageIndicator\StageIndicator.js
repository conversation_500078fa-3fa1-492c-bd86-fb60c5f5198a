import React from 'react';
import { View, Text, TouchableOpacity, ScrollView } from 'react-native';
import { COLORS, PLANT_STAGES } from '../../utils/constants';
import styles from './StageIndicator.styles.js';

const StageIndicator = ({
  currentStage = 1,
  onStageChange,
  totalStages = 10,
  stages = [],
  style,
  ...props
}) => {
  const handleStagePress = (stageId) => {
    if (onStageChange) {
      onStageChange(stageId);
    }
  };

  return (
    <View style={[styles.container, style]} {...props}>
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
      >
        {(stages.length > 0 ? stages : PLANT_STAGES.slice(0, totalStages)).map((stage, index) => {
          const stageId = stage.stageOrder || stage.id || (index + 1);
          const stageName = stage.name;
          const isActive = stageId === currentStage;
          const isPassed = stageId < currentStage;
          
          return (
            <View key={stage.id || stageId} style={styles.stageContainer}>
              <TouchableOpacity
                style={[
                  styles.stageButton,
                  isActive && styles.activeStageButton,
                  isPassed && styles.passedStageButton,
                ]}
                onPress={() => handleStagePress(stageId)}
                activeOpacity={0.7}
              >
                <Text
                  style={[
                    styles.stageNumber,
                    isActive && styles.activeStageNumber,
                    isPassed && styles.passedStageNumber,
                  ]}
                >
                  {stageId}
                </Text>
              </TouchableOpacity>

              <Text
                style={[
                  styles.stageName,
                  isActive && styles.activeStageName,
                ]}
                numberOfLines={1}
              >
                {stageName}
              </Text>

              {/* Bağlantı çizgisi */}
              {index < (stages.length > 0 ? stages : PLANT_STAGES).length - 1 && (
                <View
                  style={[
                    styles.connector,
                    isPassed && styles.passedConnector,
                  ]}
                />
              )}
            </View>
          );
        })}
      </ScrollView>
    </View>
  );
};

export default StageIndicator;
