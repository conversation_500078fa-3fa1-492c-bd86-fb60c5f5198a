import React from 'react';
import { View, Text, TouchableOpacity, ScrollView } from 'react-native';
import { COLORS, PLANT_STAGES } from '../../utils/constants';
import styles from './StageIndicator.styles.js';

const StageIndicator = ({
  currentStage = 1,
  onStageChange,
  style,
  ...props
}) => {
  const handleStagePress = (stageId) => {
    if (onStageChange) {
      onStageChange(stageId);
    }
  };

  return (
    <View style={[styles.container, style]} {...props}>
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
      >
        {PLANT_STAGES.map((stage, index) => {
          const isActive = stage.id === currentStage;
          const isPassed = stage.id < currentStage;
          
          return (
            <View key={stage.id} style={styles.stageContainer}>
              <TouchableOpacity
                style={[
                  styles.stageButton,
                  isActive && styles.activeStageButton,
                  isPassed && styles.passedStageButton,
                ]}
                onPress={() => handleStagePress(stage.id)}
                activeOpacity={0.7}
              >
                <Text
                  style={[
                    styles.stageNumber,
                    isActive && styles.activeStageNumber,
                    isPassed && styles.passedStageNumber,
                  ]}
                >
                  {stage.id}
                </Text>
              </TouchableOpacity>
              
              <Text
                style={[
                  styles.stageName,
                  isActive && styles.activeStageName,
                ]}
                numberOfLines={1}
              >
                {stage.name}
              </Text>
              
              {/* Bağlantı çizgisi */}
              {index < PLANT_STAGES.length - 1 && (
                <View
                  style={[
                    styles.connector,
                    isPassed && styles.passedConnector,
                  ]}
                />
              )}
            </View>
          );
        })}
      </ScrollView>
    </View>
  );
};

export default StageIndicator;
