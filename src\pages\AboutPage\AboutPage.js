import React, { useState, useCallback } from 'react';
import {
  View,
  Text,
  FlatList,
  TextInput,
  TouchableOpacity,
  SafeAreaView,
  Image,
  ScrollView,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

import LoadingSpinner from '../../components/common/LoadingSpinner/LoadingSpinner';
import { COLORS, KNOWLEDGE_BASE_ARTICLES } from '../../utils/constants';
import { debounce } from '../../utils/helpers';
import styles from './AboutPage.styles.js';

const AboutPage = ({ navigation }) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredArticles, setFilteredArticles] = useState(KNOWLEDGE_BASE_ARTICLES);
  const [selectedCategory, setSelectedCategory] = useState('all');

  const categories = [
    { id: 'all', name: '<PERSON>ü<PERSON><PERSON>' },
    { id: '<PERSON><PERSON><PERSON><PERSON> İpuçları', name: '<PERSON><PERSON><PERSON><PERSON> İpuçları' },
    { id: '<PERSON>rak Bilgisi', name: '<PERSON>rak Bilgisi' },
    { id: '<PERSON><PERSON><PERSON>klar', name: '<PERSON>talıklar' },
    { id: 'Genel Bilgi', name: 'Genel Bilgi' },
  ];

  // Debounced search function
  const debouncedSearch = useCallback(
    debounce((query, category) => {
      filterArticles(query, category);
    }, 300),
    []
  );

  const filterArticles = (query, category) => {
    let filtered = KNOWLEDGE_BASE_ARTICLES;

    // Category filter
    if (category && category !== 'all') {
      filtered = filtered.filter(article => article.category === category);
    }

    // Search filter
    if (query.trim()) {
      const searchTerm = query.toLowerCase();
      filtered = filtered.filter(article =>
        article.title.toLowerCase().includes(searchTerm) ||
        article.summary.toLowerCase().includes(searchTerm) ||
        article.content.toLowerCase().includes(searchTerm)
      );
    }

    setFilteredArticles(filtered);
  };

  const handleSearchChange = (text) => {
    setSearchQuery(text);
    debouncedSearch(text, selectedCategory);
  };

  const handleCategoryChange = (categoryId) => {
    setSelectedCategory(categoryId);
    debouncedSearch(searchQuery, categoryId);
  };

  const handleClearSearch = () => {
    setSearchQuery('');
    debouncedSearch('', selectedCategory);
  };

  const handleArticlePress = (article) => {
    navigation.navigate('ArticleDetail', { article });
  };

  const renderCategoryItem = ({ item }) => (
    <TouchableOpacity
      style={[
        styles.categoryButton,
        selectedCategory === item.id && styles.activeCategoryButton,
      ]}
      onPress={() => handleCategoryChange(item.id)}
    >
      <Text
        style={[
          styles.categoryButtonText,
          selectedCategory === item.id && styles.activeCategoryButtonText,
        ]}
      >
        {item.name}
      </Text>
    </TouchableOpacity>
  );

  const renderArticleItem = ({ item }) => (
    <TouchableOpacity
      style={styles.articleCard}
      onPress={() => handleArticlePress(item)}
      activeOpacity={0.7}
    >
      <View style={styles.articleHeader}>
        <Text style={styles.articleTitle} numberOfLines={2}>
          {item.title}
        </Text>
        <View style={styles.categoryTag}>
          <Text style={styles.categoryTagText}>{item.category}</Text>
        </View>
      </View>
      <Text style={styles.articleSummary} numberOfLines={3}>
        {item.summary}
      </Text>
      <View style={styles.articleFooter}>
        <Ionicons name="chevron-forward" size={16} color={COLORS.darkGray} />
      </View>
    </TouchableOpacity>
  );

  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <Ionicons name="document-text-outline" size={64} color={COLORS.gray} />
      <Text style={styles.emptyStateTitle}>
        {searchQuery ? 'Sonuç Bulunamadı' : 'Makale Bulunamadı'}
      </Text>
      <Text style={styles.emptyStateText}>
        {searchQuery
          ? 'Arama kriterlerinizi değiştirip tekrar deneyin'
          : 'Bu kategoride henüz makale bulunmuyor'
        }
      </Text>
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <View style={styles.logoSection}>
          <Image
            source={require('../../../assets/wikiplantlogo.png')}
            style={styles.logo}
            resizeMode="contain"
          />
          <View>
            <Text style={styles.title}>Dallardan Bilgi</Text>
            <Text style={styles.subtitle}>Bitki Ansiklopedisi</Text>
          </View>
        </View>
      </View>

      {/* Articles List */}
      <FlatList
        data={filteredArticles}
        renderItem={renderArticleItem}
        keyExtractor={(item) => item.id.toString()}
        contentContainerStyle={[styles.listContent, styles.scrollContent]}
        showsVerticalScrollIndicator={false}
        ListEmptyComponent={renderEmptyState}
        ListHeaderComponent={() => (
          <View>
            {/* Search Bar */}
            <View style={styles.searchContainer}>
              <View style={styles.searchInputContainer}>
                <Ionicons name="search" size={20} color={COLORS.darkGray} />
                <TextInput
                  style={styles.searchInput}
                  placeholder="Makale ara..."
                  placeholderTextColor={COLORS.darkGray}
                  value={searchQuery}
                  onChangeText={handleSearchChange}
                  returnKeyType="search"
                />
                {searchQuery.length > 0 && (
                  <TouchableOpacity onPress={handleClearSearch}>
                    <Ionicons name="close-circle" size={20} color={COLORS.darkGray} />
                  </TouchableOpacity>
                )}
              </View>
            </View>

            {/* Categories */}
            <View style={styles.categoriesContainer}>
              <ScrollView
                horizontal
                showsHorizontalScrollIndicator={false}
                contentContainerStyle={styles.categoriesList}
              >
                {categories.map((category) => (
                  <View key={category.id}>
                    {renderCategoryItem({ item: category })}
                  </View>
                ))}
              </ScrollView>
            </View>

            {/* Results Info */}
            <View style={styles.resultsInfo}>
              <Text style={styles.resultsText}>
                {filteredArticles.length} makale bulundu
              </Text>
            </View>
          </View>
        )}
      />
    </SafeAreaView>
  );
};

export default AboutPage;
