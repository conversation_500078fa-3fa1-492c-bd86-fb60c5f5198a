// Firebase imports disabled - Firebase package not installed yet
// import {
//   collection,
//   doc,
//   getDocs,
//   getDoc,
//   addDoc,
//   updateDoc,
//   deleteDoc,
//   query,
//   where,
//   orderBy,
//   limit,
//   startAfter,
//   increment,
//   serverTimestamp
// } from 'firebase/firestore';
// import { db } from '../config/firebase';
import { COLLECTIONS, PLANT_CATEGORIES, PLANT_DIFFICULTIES } from '../database/schema';

class PlantsService {
  constructor() {
    // Firebase disabled - mock service
    console.log('📦 Mock PlantsService initialized');
  }

  // Get all plants with optional filters
  async getPlants(filters = {}) {
    try {
      let q = query(this.collectionRef);

      // Apply filters
      if (filters.category && filters.category !== 'Tümü') {
        q = query(q, where('category', '==', filters.category));
      }

      if (filters.difficulty && filters.difficulty !== 'Tümü') {
        q = query(q, where('difficulty', '==', filters.difficulty));
      }

      if (filters.isNew) {
        q = query(q, where('isNew', '==', true));
      }

      if (filters.isPopular) {
        q = query(q, where('isPopular', '==', true));
      }

      // Add ordering
      q = query(q, orderBy('createdAt', 'desc'));

      // Add pagination if specified
      if (filters.limitCount) {
        q = query(q, limit(filters.limitCount));
      }

      if (filters.lastDoc) {
        q = query(q, startAfter(filters.lastDoc));
      }

      const querySnapshot = await getDocs(q);
      const plants = [];
      
      querySnapshot.forEach((doc) => {
        plants.push({
          id: doc.id,
          ...doc.data()
        });
      });

      return {
        plants,
        lastDoc: querySnapshot.docs[querySnapshot.docs.length - 1],
        hasMore: querySnapshot.docs.length === (filters.limitCount || 20)
      };
    } catch (error) {
      console.error('Error getting plants:', error);
      throw error;
    }
  }

  // Get a single plant by ID
  async getPlantById(plantId) {
    try {
      const docRef = doc(db, COLLECTIONS.PLANTS, plantId);
      const docSnap = await getDoc(docRef);

      if (docSnap.exists()) {
        // Increment view count
        await this.incrementViews(plantId);
        
        return {
          id: docSnap.id,
          ...docSnap.data()
        };
      } else {
        throw new Error('Plant not found');
      }
    } catch (error) {
      console.error('Error getting plant:', error);
      throw error;
    }
  }

  // Search plants by name or latin name
  async searchPlants(searchTerm) {
    try {
      // Note: Firestore doesn't support full-text search natively
      // This is a basic implementation. For production, consider using Algolia or similar
      const q = query(
        this.collectionRef,
        orderBy('name'),
        limit(20)
      );

      const querySnapshot = await getDocs(q);
      const plants = [];
      
      querySnapshot.forEach((doc) => {
        const data = doc.data();
        const name = data.name.toLowerCase();
        const latinName = data.latinName.toLowerCase();
        const search = searchTerm.toLowerCase();

        if (name.includes(search) || latinName.includes(search)) {
          plants.push({
            id: doc.id,
            ...data
          });
        }
      });

      return plants;
    } catch (error) {
      console.error('Error searching plants:', error);
      throw error;
    }
  }

  // Get featured/popular plants
  async getFeaturedPlants(limitCount = 10) {
    try {
      const q = query(
        this.collectionRef,
        where('isPopular', '==', true),
        orderBy('views', 'desc'),
        limit(limitCount)
      );

      const querySnapshot = await getDocs(q);
      const plants = [];
      
      querySnapshot.forEach((doc) => {
        plants.push({
          id: doc.id,
          ...doc.data()
        });
      });

      return plants;
    } catch (error) {
      console.error('Error getting featured plants:', error);
      throw error;
    }
  }

  // Get new plants
  async getNewPlants(limitCount = 10) {
    try {
      const q = query(
        this.collectionRef,
        where('isNew', '==', true),
        orderBy('createdAt', 'desc'),
        limit(limitCount)
      );

      const querySnapshot = await getDocs(q);
      const plants = [];
      
      querySnapshot.forEach((doc) => {
        plants.push({
          id: doc.id,
          ...doc.data()
        });
      });

      return plants;
    } catch (error) {
      console.error('Error getting new plants:', error);
      throw error;
    }
  }

  // Get plants by category
  async getPlantsByCategory(category, limitCount = 20) {
    try {
      const q = query(
        this.collectionRef,
        where('category', '==', category),
        orderBy('name'),
        limit(limitCount)
      );

      const querySnapshot = await getDocs(q);
      const plants = [];
      
      querySnapshot.forEach((doc) => {
        plants.push({
          id: doc.id,
          ...doc.data()
        });
      });

      return plants;
    } catch (error) {
      console.error('Error getting plants by category:', error);
      throw error;
    }
  }

  // Add a new plant (admin only)
  async addPlant(plantData) {
    try {
      const docRef = await addDoc(this.collectionRef, {
        ...plantData,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp(),
        views: 0,
        likes: 0
      });

      return docRef.id;
    } catch (error) {
      console.error('Error adding plant:', error);
      throw error;
    }
  }

  // Update a plant (admin only)
  async updatePlant(plantId, plantData) {
    try {
      const docRef = doc(db, COLLECTIONS.PLANTS, plantId);
      await updateDoc(docRef, {
        ...plantData,
        updatedAt: serverTimestamp()
      });

      return true;
    } catch (error) {
      console.error('Error updating plant:', error);
      throw error;
    }
  }

  // Delete a plant (admin only)
  async deletePlant(plantId) {
    try {
      const docRef = doc(db, COLLECTIONS.PLANTS, plantId);
      await deleteDoc(docRef);
      return true;
    } catch (error) {
      console.error('Error deleting plant:', error);
      throw error;
    }
  }

  // Increment view count
  async incrementViews(plantId) {
    try {
      const docRef = doc(db, COLLECTIONS.PLANTS, plantId);
      await updateDoc(docRef, {
        views: increment(1)
      });
    } catch (error) {
      console.error('Error incrementing views:', error);
      // Don't throw error for view counting
    }
  }

  // Like/unlike a plant
  async toggleLike(plantId, isLiked) {
    try {
      const docRef = doc(db, COLLECTIONS.PLANTS, plantId);
      await updateDoc(docRef, {
        likes: increment(isLiked ? 1 : -1)
      });
    } catch (error) {
      console.error('Error toggling like:', error);
      throw error;
    }
  }

  // Get plant statistics
  async getPlantStats() {
    try {
      const querySnapshot = await getDocs(this.collectionRef);
      const stats = {
        total: 0,
        byCategory: {},
        byDifficulty: {},
        new: 0,
        popular: 0
      };

      querySnapshot.forEach((doc) => {
        const data = doc.data();
        stats.total++;

        // Count by category
        if (data.category) {
          stats.byCategory[data.category] = (stats.byCategory[data.category] || 0) + 1;
        }

        // Count by difficulty
        if (data.difficulty) {
          stats.byDifficulty[data.difficulty] = (stats.byDifficulty[data.difficulty] || 0) + 1;
        }

        // Count new and popular
        if (data.isNew) stats.new++;
        if (data.isPopular) stats.popular++;
      });

      return stats;
    } catch (error) {
      console.error('Error getting plant stats:', error);
      throw error;
    }
  }
}

export default new PlantsService();
