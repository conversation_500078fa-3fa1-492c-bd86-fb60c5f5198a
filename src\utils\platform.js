import { Platform } from 'react-native';

// Platform detection utilities
export const isWeb = Platform.OS === 'web';
export const isMobile = Platform.OS === 'ios' || Platform.OS === 'android';
export const isIOS = Platform.OS === 'ios';
export const isAndroid = Platform.OS === 'android';

// Web-specific utilities
export const getWebUserAgent = () => {
  if (isWeb && typeof navigator !== 'undefined') {
    return navigator.userAgent;
  }
  return '';
};

export const isMobileWeb = () => {
  if (!isWeb) return false;
  const userAgent = getWebUserAgent();
  return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent);
};

export const isDesktopWeb = () => {
  return isWeb && !isMobileWeb();
};

// Screen size utilities for web
export const getWebScreenSize = () => {
  if (isWeb && typeof window !== 'undefined') {
    return {
      width: window.innerWidth,
      height: window.innerHeight
    };
  }
  return { width: 0, height: 0 };
};

export const isLargeScreen = () => {
  if (!isWeb) return false;
  const { width } = getWebScreenSize();
  return width >= 1024;
};

export const isMediumScreen = () => {
  if (!isWeb) return false;
  const { width } = getWebScreenSize();
  return width >= 768 && width < 1024;
};

export const isSmallScreen = () => {
  if (!isWeb) return false;
  const { width } = getWebScreenSize();
  return width < 768;
};
