import { StyleSheet, Dimensions } from 'react-native';
import { COLORS, FONT_SIZES, SPACING } from '../../utils/constants';

const { width } = Dimensions.get('window');
const cardWidth = (width - SPACING.lg * 3) / 2;

export default StyleSheet.create({
  card: {
    width: cardWidth,
    marginBottom: SPACING.lg,
    borderRadius: 20,
    overflow: 'hidden',
    shadowColor: COLORS.shadow,
    shadowOffset: {
      width: 0,
      height: 8,
    },
    shadowOpacity: 0.15,
    shadowRadius: 16,
    elevation: 8,
  },

  cardTouchable: {
    flex: 1,
  },

  gradientBackground: {
    flex: 1,
    borderRadius: 20,
    overflow: 'hidden',
    position: 'relative',
  },

  imageContainer: {
    height: 140,
    position: 'relative',
    margin: SPACING.sm,
    borderRadius: 16,
    overflow: 'hidden',
    backgroundColor: COLORS.lightGray,
  },

  image: {
    width: '100%',
    height: '100%',
  },

  favoriteButton: {
    position: 'absolute',
    top: SPACING.sm,
    right: SPACING.sm,
    zIndex: 10,
  },

  favoriteButtonBackground: {
    width: 32,
    height: 32,
    borderRadius: 16,
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: COLORS.shadow,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 4,
  },

  badges: {
    position: 'absolute',
    top: SPACING.sm,
    left: SPACING.sm,
    gap: SPACING.xs,
  },

  badge: {
    paddingHorizontal: SPACING.sm,
    paddingVertical: 4,
    borderRadius: 12,
    alignSelf: 'flex-start',
  },

  badgeText: {
    fontSize: FONT_SIZES.small - 2,
    fontWeight: '700',
    color: COLORS.white,
    letterSpacing: 0.5,
  },

  content: {
    padding: SPACING.md,
    paddingTop: SPACING.sm,
    flex: 1,
  },

  plantName: {
    fontSize: FONT_SIZES.large,
    fontWeight: '700',
    color: COLORS.primary,
    marginBottom: 2,
    letterSpacing: 0.3,
  },

  latinName: {
    fontSize: FONT_SIZES.small,
    fontStyle: 'italic',
    color: COLORS.darkGray,
    marginBottom: SPACING.sm,
  },

  description: {
    fontSize: FONT_SIZES.small,
    color: COLORS.darkGray,
    lineHeight: 18,
    marginBottom: SPACING.md,
    flex: 1,
  },

  footer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },

  careInfo: {
    flexDirection: 'row',
    gap: SPACING.sm,
  },

  careItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(124, 77, 37, 0.08)',
    paddingHorizontal: SPACING.xs,
    paddingVertical: 4,
    borderRadius: 8,
    gap: 4,
  },

  careText: {
    fontSize: FONT_SIZES.small - 2,
    color: COLORS.primary,
    fontWeight: '500',
  },

  detailButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: COLORS.primary,
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: COLORS.primary,
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 6,
  },

  // Dekoratif Elementler
  decorativeElement1: {
    position: 'absolute',
    top: -20,
    right: -20,
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: 'rgba(124, 77, 37, 0.03)',
    zIndex: 1,
  },

  decorativeElement2: {
    position: 'absolute',
    bottom: -15,
    left: -15,
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(201, 226, 101, 0.05)',
    zIndex: 1,
  },
});
