import { StyleSheet } from 'react-native';
import { COLORS, FONT_SIZES, SPACING } from '../../utils/constants';

export default StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },

  keyboardView: {
    flex: 1,
  },

  scrollContent: {
    flexGrow: 1,
  },

  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: SPACING.lg,
    paddingVertical: SPACING.xl,
    backgroundColor: '#FC7138', // Turuncu arka plan
    paddingTop: SPACING.xxl,
  },

  backButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: SPACING.lg,
  },

  logoSection: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    justifyContent: 'center',
    marginRight: 44, // backButton geni<PERSON><PERSON><PERSON><PERSON> kadar offset
  },

  logo: {
    width: 50,
    height: 50,
    marginRight: SPACING.md,
  },

  logoText: {
    fontSize: FONT_SIZES.header + 2,
    fontWeight: '800',
    color: COLORS.white,
    letterSpacing: 0.5,
  },

  formContainer: {
    flex: 1,
    paddingHorizontal: SPACING.xl,
    paddingTop: SPACING.xxl,
    paddingBottom: SPACING.xl,
  },

  formTitle: {
    fontSize: FONT_SIZES.header + 4,
    fontWeight: '800',
    color: COLORS.primary,
    textAlign: 'center',
    marginBottom: SPACING.sm,
  },

  formSubtitle: {
    fontSize: FONT_SIZES.medium,
    color: COLORS.darkGray,
    textAlign: 'center',
    marginBottom: SPACING.xxl,
  },

  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.white,
    borderRadius: 16,
    paddingHorizontal: SPACING.lg,
    paddingVertical: SPACING.md,
    marginBottom: SPACING.lg,
    borderWidth: 1,
    borderColor: COLORS.lightGray,
    shadowColor: COLORS.shadow,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },

  input: {
    flex: 1,
    fontSize: FONT_SIZES.medium,
    color: COLORS.primary,
    marginLeft: SPACING.md,
  },

  submitButton: {
    backgroundColor: '#FC7138', // Turuncu
    borderRadius: 16,
    paddingVertical: SPACING.lg,
    alignItems: 'center',
    marginTop: SPACING.lg,
    marginBottom: SPACING.xl,
    shadowColor: COLORS.shadow,
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 6,
  },

  submitButtonText: {
    fontSize: FONT_SIZES.large,
    fontWeight: '700',
    color: COLORS.white,
    letterSpacing: 0.5,
  },

  toggleContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    gap: SPACING.sm,
  },

  toggleText: {
    fontSize: FONT_SIZES.medium,
    color: COLORS.darkGray,
  },

  toggleButton: {
    fontSize: FONT_SIZES.medium,
    color: '#FC7138', // Turuncu
    fontWeight: '600',
  },
});
