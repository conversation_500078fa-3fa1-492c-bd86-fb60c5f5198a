// Plant stages'ı bitkiler içinde plantstages field olarak güncelleme
const { initializeApp } = require('firebase/app');
const { getFirestore, collection, getDocs, doc, updateDoc } = require('firebase/firestore');

const firebaseConfig = {
  apiKey: "AIzaSyCT9Tn0Z7MVjKeonhNuovpdbXVz7syQwJ8",
  authDomain: "wikiplant-aebaf.firebaseapp.com",
  projectId: "wikiplant-aebaf",
  storageBucket: "wikiplant-aebaf.firebasestorage.app",
  messagingSenderId: "847754813681",
  appId: "1:847754813681:web:22559d4547eca2ab594950",
  measurementId: "G-27R9QPVXF6"
};

const app = initializeApp(firebaseConfig);
const db = getFirestore(app);

// Bitki aşamaları verisi - her bitki için
const plantStagesData = {
  'Gül': [
    {
      id: 1,
      name: '<PERSON><PERSON>',
      image: 'https://images.unsplash.com/photo-1416879595882-3373a0480b5b?w=300&h=200&fit=crop',
      description: 'Gül tohumları soğuk stratifikasyon işleminden sonra nemli toprakta ekilir. Tohumlar çimlenmeye başlamadan önce 2-4 hafta soğukta bekletilmelidir.'
    },
    {
      id: 2,
      name: 'Çimlenme',
      image: 'https://images.unsplash.com/photo-1416879595882-3373a0480b5b?w=300&h=200&fit=crop',
      description: 'Tohumlar çimlenir ve ilk kotiledon yapraklar görünür. Bu aşamada bitki çok hassastır ve dikkatli bakım gerektirir.'
    },
    {
      id: 3,
      name: 'Fide Gelişimi',
      image: 'https://images.unsplash.com/photo-1416879595882-3373a0480b5b?w=300&h=200&fit=crop',
      description: 'İlk gerçek yapraklar çıkar ve kök sistemi güçlenir. Bitki daha dayanıklı hale gelir ve büyüme hızlanır.'
    },
    {
      id: 4,
      name: 'Genç Bitki',
      image: 'https://images.unsplash.com/photo-1416879595882-3373a0480b5b?w=300&h=200&fit=crop',
      description: 'Bitki dallanmaya başlar ve daha güçlü bir yapıya kavuşur. Bu aşamada şekil verme budaması yapılabilir.'
    },
    {
      id: 5,
      name: 'Çiçeklenme',
      image: 'https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=300&h=200&fit=crop',
      description: 'Bitki olgunlaşır ve ilk çiçeklerini açar. Bu aşamada düzenli bakım ve gübreleme çok önemlidir.'
    },
    {
      id: 6,
      name: 'Olgun Bitki',
      image: 'https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=300&h=200&fit=crop',
      description: 'Bitki tam olgunluğa ulaşır ve düzenli çiçek verir. Yıllık bakım rutini oluşturulmalıdır.'
    }
  ],
  
  'Domates': [
    {
      id: 1,
      name: 'Tohum Çimlenmesi',
      image: 'https://images.unsplash.com/photo-1416879595882-3373a0480b5b?w=300&h=200&fit=crop',
      description: 'Domates tohumları sıcak ve nemli ortamda hızla çimlenmeye başlar. İdeal çimlenme sıcaklığı 20-25°C\'dir.'
    },
    {
      id: 2,
      name: 'Fide Dönemi',
      image: 'https://images.unsplash.com/photo-1416879595882-3373a0480b5b?w=300&h=200&fit=crop',
      description: 'Kotiledon yapraklardan sonra gerçek yapraklar çıkar. Bu aşamada ışık ihtiyacı artar.'
    },
    {
      id: 3,
      name: 'Dikim ve Büyüme',
      image: 'https://images.unsplash.com/photo-1546470427-e26264be0b0d?w=300&h=200&fit=crop',
      description: 'Fideler ana yere dikilir ve hızla büyümeye başlar. Destek sistemi kurulmalıdır.'
    },
    {
      id: 4,
      name: 'Çiçeklenme',
      image: 'https://images.unsplash.com/photo-1546470427-e26264be0b0d?w=300&h=200&fit=crop',
      description: 'Sarı çiçekler açar ve tozlaşma gerçekleşir. Bu aşamada beslenme çok önemlidir.'
    },
    {
      id: 5,
      name: 'Meyve Gelişimi',
      image: 'https://images.unsplash.com/photo-1546470427-e26264be0b0d?w=300&h=200&fit=crop',
      description: 'Tozlaşmadan sonra yeşil meyveler oluşur ve büyümeye başlar. Düzenli sulama kritiktir.'
    },
    {
      id: 6,
      name: 'Olgunlaşma ve Hasat',
      image: 'https://images.unsplash.com/photo-1546470427-e26264be0b0d?w=300&h=200&fit=crop',
      description: 'Meyveler kırmızıya döner ve hasat edilmeye hazır hale gelir. Düzenli hasat önemlidir.'
    }
  ],

  'Lavanta': [
    {
      id: 1,
      name: 'Tohum Ekim',
      image: 'https://images.unsplash.com/photo-1611909023032-2d6b3134ecba?w=300&h=200&fit=crop',
      description: 'Lavanta tohumları soğuk stratifikasyon sonrası ekilir. Çimlenme süreci yavaştır.'
    },
    {
      id: 2,
      name: 'Çimlenme',
      image: 'https://images.unsplash.com/photo-1611909023032-2d6b3134ecba?w=300&h=200&fit=crop',
      description: 'İlk filizler çıkar. Lavanta çimlenmesi 2-4 hafta sürebilir.'
    },
    {
      id: 3,
      name: 'Fide Gelişimi',
      image: 'https://images.unsplash.com/photo-1611909023032-2d6b3134ecba?w=300&h=200&fit=crop',
      description: 'Karakteristik lavanta yaprakları oluşur. Güneş ışığı ihtiyacı artar.'
    },
    {
      id: 4,
      name: 'Genç Bitki',
      image: 'https://images.unsplash.com/photo-1611909023032-2d6b3134ecba?w=300&h=200&fit=crop',
      description: 'Bitki dallanır ve güçlenir. İlk budama yapılabilir.'
    },
    {
      id: 5,
      name: 'Çiçeklenme',
      image: 'https://images.unsplash.com/photo-1611909023032-2d6b3134ecba?w=300&h=200&fit=crop',
      description: 'Mor çiçek başakları oluşur. Güçlü lavanta kokusu yayılır.'
    },
    {
      id: 6,
      name: 'Olgun Bitki',
      image: 'https://images.unsplash.com/photo-1611909023032-2d6b3134ecba?w=300&h=200&fit=crop',
      description: 'Tam gelişmiş lavanta bitkisi. Yıllık hasat ve budama yapılır.'
    }
  ],

  'Aloe Vera': [
    {
      id: 1,
      name: 'Yaprak Çelikleme',
      image: 'https://images.unsplash.com/photo-1509587584298-0f3b3a3a1797?w=300&h=200&fit=crop',
      description: 'Aloe vera yaprak çeliği ile çoğaltılır. Çelik kurutma işlemi önemlidir.'
    },
    {
      id: 2,
      name: 'Kök Oluşumu',
      image: 'https://images.unsplash.com/photo-1509587584298-0f3b3a3a1797?w=300&h=200&fit=crop',
      description: 'Çelikten kökler çıkmaya başlar. Nemli ama ıslak olmayan ortam gerekir.'
    },
    {
      id: 3,
      name: 'Genç Bitki',
      image: 'https://images.unsplash.com/photo-1509587584298-0f3b3a3a1797?w=300&h=200&fit=crop',
      description: 'İlk yapraklar oluşur. Yavaş büyüme karakteristiktir.'
    },
    {
      id: 4,
      name: 'Gelişim',
      image: 'https://images.unsplash.com/photo-1509587584298-0f3b3a3a1797?w=300&h=200&fit=crop',
      description: 'Yapraklar kalınlaşır ve et yapısı oluşur. Su depolama başlar.'
    },
    {
      id: 5,
      name: 'Olgun Bitki',
      image: 'https://images.unsplash.com/photo-1509587584298-0f3b3a3a1797?w=300&h=200&fit=crop',
      description: 'Tam gelişmiş aloe vera. Yapraklar hasat edilebilir.'
    }
  ],

  'Fesleğen': [
    {
      id: 1,
      name: 'Tohum Ekim',
      image: 'https://images.unsplash.com/photo-1618164436241-4473940d1f5c?w=300&h=200&fit=crop',
      description: 'Fesleğen tohumları sıcak ortamda ekilir. Hızlı çimlenme özelliği vardır.'
    },
    {
      id: 2,
      name: 'Çimlenme',
      image: 'https://images.unsplash.com/photo-1618164436241-4473940d1f5c?w=300&h=200&fit=crop',
      description: 'İlk filizler 5-10 gün içinde çıkar. Nemli ortam korunmalıdır.'
    },
    {
      id: 3,
      name: 'Fide Gelişimi',
      image: 'https://images.unsplash.com/photo-1618164436241-4473940d1f5c?w=300&h=200&fit=crop',
      description: 'Gerçek yapraklar oluşur. Karakteristik fesleğen kokusu başlar.'
    },
    {
      id: 4,
      name: 'Büyüme',
      image: 'https://images.unsplash.com/photo-1618164436241-4473940d1f5c?w=300&h=200&fit=crop',
      description: 'Bitki hızla büyür ve dallanır. İlk hasat yapılabilir.'
    },
    {
      id: 5,
      name: 'Olgun Bitki',
      image: 'https://images.unsplash.com/photo-1618164436241-4473940d1f5c?w=300&h=200&fit=crop',
      description: 'Tam gelişmiş fesleğen. Düzenli hasat ve budama yapılır.'
    }
  ]
};

async function updatePlantsWithStages() {
  try {
    console.log('🚀 Bitkilere plantstages field\'ı ekleniyor...');
    
    // Tüm bitkileri al
    const plantsSnapshot = await getDocs(collection(db, 'plants'));
    
    for (const plantDoc of plantsSnapshot.docs) {
      const plantData = plantDoc.data();
      const plantName = plantData.name;
      
      if (plantStagesData[plantName]) {
        // Bitkiye plantstages field'ını ekle
        await updateDoc(doc(db, 'plants', plantDoc.id), {
          plantstages: plantStagesData[plantName],
          updatedAt: new Date()
        });
        
        console.log(`✅ ${plantName} için ${plantStagesData[plantName].length} aşama eklendi`);
      } else {
        console.log(`⚠️ ${plantName} için aşama verisi bulunamadı`);
      }
    }
    
    console.log('🎉 Tüm bitkiler plantstages ile güncellendi!');
    
  } catch (error) {
    console.error('❌ Hata:', error);
  }
}

updatePlantsWithStages();
