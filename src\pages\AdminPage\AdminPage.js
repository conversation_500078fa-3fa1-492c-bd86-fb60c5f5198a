import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { isWeb } from '../../utils/platform';
import migration from '../../database/migration';
import { COLORS } from '../../utils/constants';
import styles from './AdminPage.styles';

const AdminPage = () => {
  const [loading, setLoading] = useState(false);
  const [logs, setLogs] = useState([]);

  // Only show on web platform
  if (!isWeb) {
    return null;
  }

  const addLog = (message, type = 'info') => {
    const timestamp = new Date().toLocaleTimeString();
    setLogs(prev => [...prev, { message, type, timestamp }]);
  };

  const clearLogs = () => {
    setLogs([]);
  };

  const handleMigratePlants = async () => {
    setLoading(true);
    addLog('🌱 Bitki verilerini yükleme başlatıldı...', 'info');
    
    try {
      await migration.migratePlants();
      addLog('✅ Bitki verileri başarıyla yüklendi!', 'success');
    } catch (error) {
      addLog(`❌ Bitki verisi yükleme hatası: ${error.message}`, 'error');
    }
    
    setLoading(false);
  };

  const handleMigrateArticles = async () => {
    setLoading(true);
    addLog('📚 Makale verilerini yükleme başlatıldı...', 'info');
    
    try {
      await migration.migrateArticles();
      addLog('✅ Makale verileri başarıyla yüklendi!', 'success');
    } catch (error) {
      addLog(`❌ Makale verisi yükleme hatası: ${error.message}`, 'error');
    }
    
    setLoading(false);
  };

  const handleMigrateCategories = async () => {
    setLoading(true);
    addLog('📂 Kategori verilerini yükleme başlatıldı...', 'info');
    
    try {
      await migration.migrateCategories();
      addLog('✅ Kategori verileri başarıyla yüklendi!', 'success');
    } catch (error) {
      addLog(`❌ Kategori verisi yükleme hatası: ${error.message}`, 'error');
    }
    
    setLoading(false);
  };

  const handleFullMigration = async () => {
    setLoading(true);
    addLog('🚀 Tam veri yükleme başlatıldı...', 'info');
    
    try {
      await migration.runMigration();
      addLog('✅ Tüm veriler başarıyla yüklendi!', 'success');
    } catch (error) {
      addLog(`❌ Tam veri yükleme hatası: ${error.message}`, 'error');
    }
    
    setLoading(false);
  };

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <View style={styles.headerContent}>
          <Ionicons name="settings" size={32} color={COLORS.white} />
          <View style={styles.headerText}>
            <Text style={styles.headerTitle}>Admin Panel</Text>
            <Text style={styles.headerSubtitle}>Dallardan Bilgi - Veri Yönetimi</Text>
          </View>
        </View>
      </View>

      <ScrollView style={styles.content}>
        {/* Migration Controls */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Veri Yükleme İşlemleri</Text>
          
          <View style={styles.buttonGrid}>
            <TouchableOpacity
              style={[styles.actionButton, styles.plantButton]}
              onPress={handleMigratePlants}
              disabled={loading}
            >
              <Ionicons name="leaf" size={24} color={COLORS.white} />
              <Text style={styles.buttonText}>Bitki Verilerini Yükle</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.actionButton, styles.articleButton]}
              onPress={handleMigrateArticles}
              disabled={loading}
            >
              <Ionicons name="book" size={24} color={COLORS.white} />
              <Text style={styles.buttonText}>Makale Verilerini Yükle</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.actionButton, styles.categoryButton]}
              onPress={handleMigrateCategories}
              disabled={loading}
            >
              <Ionicons name="grid" size={24} color={COLORS.white} />
              <Text style={styles.buttonText}>Kategori Verilerini Yükle</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.actionButton, styles.fullButton]}
              onPress={handleFullMigration}
              disabled={loading}
            >
              <Ionicons name="cloud-upload" size={24} color={COLORS.white} />
              <Text style={styles.buttonText}>Tüm Verileri Yükle</Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Database Info */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Veritabanı Bilgileri</Text>
          <View style={styles.infoCard}>
            <View style={styles.infoRow}>
              <Ionicons name="server" size={20} color={COLORS.primary} />
              <Text style={styles.infoText}>Firebase Firestore</Text>
            </View>
            <View style={styles.infoRow}>
              <Ionicons name="leaf" size={20} color={COLORS.secondary} />
              <Text style={styles.infoText}>Bitki Koleksiyonu: plants</Text>
            </View>
            <View style={styles.infoRow}>
              <Ionicons name="layers" size={20} color={COLORS.accent} />
              <Text style={styles.infoText}>Aşama Koleksiyonu: plant_stages</Text>
            </View>
            <View style={styles.infoRow}>
              <Ionicons name="medical" size={20} color="#FF6B6B" />
              <Text style={styles.infoText}>İlaçlama Koleksiyonu: plant_treatments</Text>
            </View>
            <View style={styles.infoRow}>
              <Ionicons name="book" size={20} color={COLORS.primary} />
              <Text style={styles.infoText}>Makale Koleksiyonu: articles</Text>
            </View>
          </View>
        </View>

        {/* Logs Section */}
        <View style={styles.section}>
          <View style={styles.logsHeader}>
            <Text style={styles.sectionTitle}>İşlem Logları</Text>
            <TouchableOpacity style={styles.clearButton} onPress={clearLogs}>
              <Ionicons name="trash" size={16} color={COLORS.darkGray} />
              <Text style={styles.clearButtonText}>Temizle</Text>
            </TouchableOpacity>
          </View>

          <View style={styles.logsContainer}>
            {logs.length === 0 ? (
              <Text style={styles.noLogsText}>Henüz işlem yapılmadı</Text>
            ) : (
              logs.map((log, index) => (
                <View key={index} style={[styles.logItem, styles[`log${log.type.charAt(0).toUpperCase() + log.type.slice(1)}`]]}>
                  <Text style={styles.logTimestamp}>{log.timestamp}</Text>
                  <Text style={styles.logMessage}>{log.message}</Text>
                </View>
              ))
            )}
          </View>
        </View>

        {/* Loading Indicator */}
        {loading && (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={COLORS.primary} />
            <Text style={styles.loadingText}>İşlem devam ediyor...</Text>
          </View>
        )}
      </ScrollView>
    </View>
  );
};

export default AdminPage;
