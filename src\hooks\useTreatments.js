import { useState, useEffect } from 'react';
import { treatmentsService } from '../services/firebaseService';

// Mock treatments data
const mockTreatments = [
  {
    id: 'treatment-1',
    plantId: 'mock-1',
    problem: 'Yaprak Biti',
    description: 'Yapra<PERSON><PERSON> küçük yeşil böcekler görülür. Yaprakların altında koloniler halinde bulunurlar.',
    solution: 'Sabunlu su ile püskürtün veya doğal predatörler kullanın. Düzenli kontrol yapın.',
    timing: 'İlkbahar ve yaz ayları',
    severity: 'medium',
    products: [
      { name: '<PERSON><PERSON><PERSON>', dosage: '10ml/1L su', frequency: 'Haftada 2 kez' },
      { name: 'Sabunlu Su', dosage: '1 çorba kaşığı/1L su', frequency: 'Günlük' }
    ],
    symptoms: ['Yapra<PERSON>rda sarı lekeler', 'Yaprak altında böcekler', '<PERSON><PERSON><PERSON> kıvrılma<PERSON>'],
    prevention: ['<PERSON><PERSON><PERSON><PERSON> kontrol', '<PERSON><PERSON><PERSON> çev<PERSON>', '<PERSON><PERSON><PERSON> sulama']
  },
  {
    id: 'treatment-2',
    plantId: 'mock-1',
    problem: 'Külleme Hastalığı',
    description: 'Yapraklarda beyaz pudra görünümünde mantari hastalık.',
    solution: 'Fungisit uygulayın ve havalandırmayı artırın.',
    timing: 'Nemli havalarda',
    severity: 'high',
    products: [
      { name: 'Bakır Sülfat', dosage: '5g/1L su', frequency: '10 günde bir' }
    ],
    symptoms: ['Beyaz pudra görünümü', 'Yaprak sararması'],
    prevention: ['İyi havalandırma', 'Aşırı sulama yapmama']
  },
  {
    id: 'treatment-3',
    plantId: 'mock-2',
    problem: 'Kök Çürüklüğü',
    description: 'Aşırı sulama nedeniyle köklerde çürüme.',
    solution: 'Sulamayı azaltın ve drene toprağa geçin.',
    timing: 'Yıl boyunca',
    severity: 'high',
    products: [
      { name: 'Fungisit', dosage: 'Etiket talimatına göre', frequency: 'Tek seferlik' }
    ],
    symptoms: ['Yaprak sararması', 'Solgunluk', 'Kötü koku'],
    prevention: ['Uygun sulama', 'Drene toprak', 'Saksı deliği']
  }
];

// Bitki tedavilerini getiren hook
export const useTreatments = (plantId) => {
  const [treatments, setTreatments] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    if (plantId) {
      loadTreatments();
    }
  }, [plantId]);

  const loadTreatments = async () => {
    try {
      setLoading(true);
      setError(null);
      
      // Mock Firebase service call
      const treatmentsData = [];
      
      if (treatmentsData.length > 0) {
        setTreatments(treatmentsData);
        console.log('✅ Firebase treatments yüklendi:', treatmentsData.length);
      } else {
        // Firebase'de veri yoksa mock data kullan
        const mockPlantTreatments = mockTreatments.filter(treatment => treatment.plantId === plantId);
        setTreatments(mockPlantTreatments);
        console.log('✅ Mock treatments kullanılıyor (Firebase boş):', mockPlantTreatments.length);
      }
    } catch (err) {
      console.error('❌ Treatments yüklenemedi, mock data kullanılıyor:', err);
      const mockPlantTreatments = mockTreatments.filter(treatment => treatment.plantId === plantId);
      setTreatments(mockPlantTreatments);
      setError('Firebase bağlantı hatası, örnek veriler gösteriliyor');
    } finally {
      setLoading(false);
    }
  };

  const refreshTreatments = () => {
    loadTreatments();
  };

  // Severity'ye göre filtrele
  const getTreatmentsBySeverity = (severity) => {
    return treatments.filter(treatment => treatment.severity === severity);
  };

  // Problem adına göre ara
  const searchTreatments = (searchTerm) => {
    return treatments.filter(treatment => 
      treatment.problem.toLowerCase().includes(searchTerm.toLowerCase()) ||
      treatment.description.toLowerCase().includes(searchTerm.toLowerCase())
    );
  };

  return {
    treatments,
    loading,
    error,
    refreshTreatments,
    getTreatmentsBySeverity,
    searchTreatments
  };
};

// Yeni tedavi ekleme hook'u
export const useAddTreatment = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(false);

  const addTreatment = async (treatmentData) => {
    try {
      setLoading(true);
      setError(null);
      setSuccess(false);
      
      // Mock Firebase service call
      const treatmentId = 'mock-treatment-id';
      
      setSuccess(true);
      console.log('✅ Yeni treatment eklendi:', treatmentId);
      
      return treatmentId;
    } catch (err) {
      console.error('❌ Treatment eklenemedi:', err);
      setError('Tedavi eklenemedi');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const resetState = () => {
    setError(null);
    setSuccess(false);
  };

  return {
    addTreatment,
    loading,
    error,
    success,
    resetState
  };
};

// Tedavi önerileri hook'u
export const useTreatmentRecommendations = (plantId, symptoms = []) => {
  const { treatments } = useTreatments(plantId);
  const [recommendations, setRecommendations] = useState([]);

  useEffect(() => {
    if (symptoms.length > 0 && treatments.length > 0) {
      // Semptomlara göre tedavi önerileri
      const matchingTreatments = treatments.filter(treatment => 
        treatment.symptoms.some(symptom => 
          symptoms.some(userSymptom => 
            symptom.toLowerCase().includes(userSymptom.toLowerCase())
          )
        )
      );

      // Severity'ye göre sırala (high -> medium -> low)
      const sortedRecommendations = matchingTreatments.sort((a, b) => {
        const severityOrder = { high: 3, medium: 2, low: 1 };
        return severityOrder[b.severity] - severityOrder[a.severity];
      });

      setRecommendations(sortedRecommendations);
    } else {
      setRecommendations([]);
    }
  }, [symptoms, treatments]);

  return {
    recommendations,
    hasRecommendations: recommendations.length > 0
  };
};

// Tedavi takibi hook'u
export const useTreatmentTracking = () => {
  const [appliedTreatments, setAppliedTreatments] = useState([]);

  // Tedavi uygula
  const applyTreatment = (treatmentId, applicationDate = new Date()) => {
    const application = {
      id: Date.now().toString(),
      treatmentId,
      applicationDate,
      status: 'applied'
    };

    setAppliedTreatments(prev => [...prev, application]);
    console.log('✅ Tedavi uygulandı:', treatmentId);
  };

  // Tedavi sonucunu güncelle
  const updateTreatmentResult = (applicationId, result, notes = '') => {
    setAppliedTreatments(prev => 
      prev.map(app => 
        app.id === applicationId 
          ? { ...app, result, notes, updatedAt: new Date() }
          : app
      )
    );
    console.log('✅ Tedavi sonucu güncellendi:', applicationId);
  };

  // Uygulanan tedavileri getir
  const getAppliedTreatments = (treatmentId) => {
    return appliedTreatments.filter(app => app.treatmentId === treatmentId);
  };

  // Son uygulama tarihini getir
  const getLastApplicationDate = (treatmentId) => {
    const applications = getAppliedTreatments(treatmentId);
    if (applications.length === 0) return null;
    
    return applications.reduce((latest, app) => 
      app.applicationDate > latest ? app.applicationDate : latest, 
      applications[0].applicationDate
    );
  };

  return {
    appliedTreatments,
    applyTreatment,
    updateTreatmentResult,
    getAppliedTreatments,
    getLastApplicationDate
  };
};
