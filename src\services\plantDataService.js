// Bitki veri y<PERSON><PERSON>im servisi
import ApiService from './api';

class PlantDataService {
  constructor() {
    this.cache = new Map();
    this.cacheTimeout = 5 * 60 * 1000; // 5 dakika
  }

  // <PERSON>ache kontrolü
  isCacheValid(key) {
    const cached = this.cache.get(key);
    if (!cached) return false;
    
    const now = Date.now();
    return (now - cached.timestamp) < this.cacheTimeout;
  }

  // Cache'den veri al
  getFromCache(key) {
    const cached = this.cache.get(key);
    return cached ? cached.data : null;
  }

  // Cache'e veri kaydet
  setCache(key, data) {
    this.cache.set(key, {
      data,
      timestamp: Date.now()
    });
  }

  // Tüm bitkileri getir (cache ile)
  async getAllPlants() {
    const cacheKey = 'all_plants';
    
    if (this.isCacheValid(cacheKey)) {
      return {
        success: true,
        data: this.getFromCache(cacheKey),
        fromCache: true
      };
    }

    try {
      const response = await ApiService.getAllPlants();
      if (response.success) {
        this.setCache(cacheKey, response.data);
      }
      return response;
    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }

  // Bitki detayını getir
  async getPlantDetail(id) {
    const cacheKey = `plant_${id}`;
    
    if (this.isCacheValid(cacheKey)) {
      return {
        success: true,
        data: this.getFromCache(cacheKey),
        fromCache: true
      };
    }

    try {
      const response = await ApiService.getPlantById(id);
      if (response.success) {
        this.setCache(cacheKey, response.data);
      }
      return response;
    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }

  // Bitki ara
  async searchPlants(query) {
    try {
      return await ApiService.searchPlants(query);
    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }

  // Popüler bitkileri getir
  async getPopularPlants() {
    const cacheKey = 'popular_plants';
    
    if (this.isCacheValid(cacheKey)) {
      return {
        success: true,
        data: this.getFromCache(cacheKey),
        fromCache: true
      };
    }

    try {
      const response = await ApiService.getPopularPlants();
      if (response.success) {
        this.setCache(cacheKey, response.data);
      }
      return response;
    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }

  // Yeni bitkileri getir
  async getNewPlants() {
    const cacheKey = 'new_plants';
    
    if (this.isCacheValid(cacheKey)) {
      return {
        success: true,
        data: this.getFromCache(cacheKey),
        fromCache: true
      };
    }

    try {
      const response = await ApiService.getNewPlants();
      if (response.success) {
        this.setCache(cacheKey, response.data);
      }
      return response;
    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }

  // Bilgi bankası makalelerini getir
  async getKnowledgeBaseArticles() {
    const cacheKey = 'knowledge_articles';
    
    if (this.isCacheValid(cacheKey)) {
      return {
        success: true,
        data: this.getFromCache(cacheKey),
        fromCache: true
      };
    }

    try {
      const response = await ApiService.getKnowledgeBaseArticles();
      if (response.success) {
        this.setCache(cacheKey, response.data);
      }
      return response;
    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }

  // Cache'i temizle
  clearCache() {
    this.cache.clear();
  }

  // Belirli bir cache anahtarını temizle
  clearCacheKey(key) {
    this.cache.delete(key);
  }
}

export default new PlantDataService();
