// Basit HomePage - Sadece Firebase verisi
import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  Image,
  ActivityIndicator,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';

// Firebase
import { collection, getDocs } from 'firebase/firestore';
import { db } from '../../config/firebase';

// Constants
import { COLORS } from '../../utils/constants';

const HomePage = () => {
  const navigation = useNavigation();
  const [plants, setPlants] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Firebase'den bitki verilerini çek
  const loadPlants = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const plantsCollection = collection(db, 'plants');
      const plantsSnapshot = await getDocs(plantsCollection);
      const plantsData = plantsSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));
      
      setPlants(plantsData);
      console.log('✅ Plants loaded:', plantsData.length);
    } catch (err) {
      console.error('❌ Error loading plants:', err);
      setError('Bitkiler yüklenirken hata oluştu');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadPlants();
  }, []);

  // Bitki detayına git
  const handlePlantPress = (plant) => {
    navigation.navigate('PlantDetail', { plantId: plant.id, plant });
  };

  // Loading state
  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={COLORS.primary} />
          <Text style={styles.loadingText}>Bitkiler yükleniyor...</Text>
        </View>
      </SafeAreaView>
    );
  }

  // Error state
  if (error) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.errorContainer}>
          <Ionicons name="alert-circle" size={64} color={COLORS.error} />
          <Text style={styles.errorText}>{error}</Text>
          <TouchableOpacity style={styles.retryButton} onPress={loadPlants}>
            <Text style={styles.retryButtonText}>Tekrar Dene</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Header */}
        <View style={styles.header}>
          <View style={styles.logoSection}>
            <Image
              source={require('../../../assets/wikiplantlogo.png')}
              style={styles.logo}
              resizeMode="contain"
            />
            <View style={styles.titleSection}>
              <Text style={styles.title}>Dallardan Bilgi</Text>
              <Text style={styles.subtitle}>Bitki Ansiklopedisi</Text>
            </View>
          </View>
        </View>

        {/* Plant Count */}
        <View style={styles.countContainer}>
          <Text style={styles.countText}>{plants.length} Bitki Bulundu</Text>
        </View>

        {/* Plant List */}
        {plants.length === 0 ? (
          <View style={styles.emptyContainer}>
            <Ionicons name="leaf" size={64} color={COLORS.gray} />
            <Text style={styles.emptyText}>Henüz bitki eklenmemiş</Text>
          </View>
        ) : (
          plants.map((plant) => (
            <TouchableOpacity
              key={plant.id}
              style={styles.plantCard}
              onPress={() => handlePlantPress(plant)}
              activeOpacity={0.8}
            >
              <Image
                source={{
                  uri: plant.mainImage || 'https://images.unsplash.com/photo-1416879595882-3373a0480b5b?w=400&h=400&fit=crop'
                }}
                style={styles.plantImage}
                resizeMode="cover"
              />
              <View style={styles.plantInfo}>
                <Text style={styles.plantName}>{plant.name}</Text>
                <Text style={styles.plantLatin}>{plant.latinName}</Text>
                <Text style={styles.plantCategory}>{plant.category}</Text>
                <Text style={styles.plantDescription} numberOfLines={2}>
                  {plant.description}
                </Text>
                <View style={styles.detailButton}>
                  <Text style={styles.detailButtonText}>Detay</Text>
                  <Ionicons name="chevron-forward" size={16} color={COLORS.white} />
                </View>
              </View>
            </TouchableOpacity>
          ))
        )}
      </ScrollView>
    </SafeAreaView>
  );
};

// Basit stiller
const styles = {
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  scrollView: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: COLORS.text,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    fontSize: 16,
    color: COLORS.error,
    textAlign: 'center',
    marginVertical: 16,
  },
  retryButton: {
    backgroundColor: COLORS.primary,
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 8,
  },
  retryButtonText: {
    color: COLORS.white,
    fontSize: 16,
    fontWeight: 'bold',
  },
  header: {
    backgroundColor: COLORS.orange,
    padding: 20,
    paddingTop: 40,
  },
  logoSection: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  logo: {
    width: 40,
    height: 40,
    marginRight: 12,
  },
  titleSection: {
    flex: 1,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: COLORS.white,
  },
  subtitle: {
    fontSize: 14,
    color: COLORS.white,
    opacity: 0.9,
  },
  countContainer: {
    padding: 16,
    backgroundColor: COLORS.white,
  },
  countText: {
    fontSize: 16,
    color: COLORS.text,
    fontWeight: '600',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
  },
  emptyText: {
    fontSize: 16,
    color: COLORS.gray,
    marginTop: 16,
  },
  plantCard: {
    backgroundColor: COLORS.white,
    marginHorizontal: 16,
    marginVertical: 8,
    borderRadius: 12,
    flexDirection: 'row',
    overflow: 'hidden',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  plantImage: {
    width: 100,
    height: 120,
  },
  plantInfo: {
    flex: 1,
    padding: 12,
    justifyContent: 'space-between',
  },
  plantName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: COLORS.text,
  },
  plantLatin: {
    fontSize: 14,
    fontStyle: 'italic',
    color: COLORS.gray,
    marginTop: 2,
  },
  plantCategory: {
    fontSize: 12,
    color: COLORS.primary,
    fontWeight: '600',
    marginTop: 4,
  },
  plantDescription: {
    fontSize: 14,
    color: COLORS.text,
    marginTop: 8,
    lineHeight: 20,
  },
  detailButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.primary,
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 6,
    alignSelf: 'flex-start',
    marginTop: 8,
  },
  detailButtonText: {
    color: COLORS.white,
    fontSize: 12,
    fontWeight: '600',
    marginRight: 4,
  },
};

export default HomePage;
