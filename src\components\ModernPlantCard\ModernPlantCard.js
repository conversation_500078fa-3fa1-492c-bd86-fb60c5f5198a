import React, { useState } from 'react';
import { View, Text, Image, TouchableOpacity, Animated } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { COLORS } from '../../utils/constants';
import { useFavoriteToggle } from '../../hooks/useFavorites';
import styles from './ModernPlantCard.styles.js';

const ModernPlantCard = ({
  plant,
  onPress,
  style,
  showFavoriteButton = true,
  ...props
}) => {
  const { toggleFavorite, isFavorite, loading } = useFavoriteToggle();
  const [scaleValue] = useState(new Animated.Value(1));

  const handleFavoritePress = (e) => {
    e.stopPropagation();
    toggleFavorite(plant.id);
  };

  const handleCardPress = () => {
    if (onPress) {
      onPress(plant);
    }
  };

  const handlePressIn = () => {
    Animated.spring(scaleValue, {
      toValue: 0.96,
      useNativeDriver: true,
      tension: 300,
      friction: 10,
    }).start();
  };

  const handlePressOut = () => {
    Animated.spring(scaleValue, {
      toValue: 1,
      useNativeDriver: true,
      tension: 300,
      friction: 10,
    }).start();
  };

  return (
    <Animated.View
      style={[
        styles.card,
        style,
        {
          transform: [{ scale: scaleValue }],
        },
      ]}
    >
      <TouchableOpacity
        style={styles.cardTouchable}
        onPress={handleCardPress}
        onPressIn={handlePressIn}
        onPressOut={handlePressOut}
        activeOpacity={1}
        {...props}
      >
        {/* Gradient Background */}
        <LinearGradient
          colors={['#f8f9fa', '#ffffff', '#f1f3f4']}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
          style={styles.gradientBackground}
        >
          {/* Bitki Görseli */}
          <View style={styles.imageContainer}>
            <Image
              source={{ uri: plant.mainImage }}
              style={styles.image}
              resizeMode="cover"
            />
            
            {/* Favori Butonu */}
            {showFavoriteButton && (
              <TouchableOpacity
                style={styles.favoriteButton}
                onPress={handleFavoritePress}
                disabled={loading}
                activeOpacity={0.7}
              >
                <LinearGradient
                  colors={isFavorite(plant.id) ? [COLORS.accent, '#FF8A50'] : ['rgba(255,255,255,0.9)', 'rgba(255,255,255,0.7)']}
                  style={styles.favoriteButtonBackground}
                >
                  <Ionicons
                    name={isFavorite(plant.id) ? 'heart' : 'heart-outline'}
                    size={16}
                    color={isFavorite(plant.id) ? COLORS.white : COLORS.darkGray}
                  />
                </LinearGradient>
              </TouchableOpacity>
            )}

            {/* Etiketler */}
            {(plant.isNew || plant.isPopular) && (
              <View style={styles.badges}>
                {plant.isNew && (
                  <LinearGradient
                    colors={[COLORS.secondary, '#A8D147']}
                    style={styles.badge}
                    start={{ x: 0, y: 0 }}
                    end={{ x: 1, y: 0 }}
                  >
                    <Text style={styles.badgeText}>YENİ</Text>
                  </LinearGradient>
                )}
                {plant.isPopular && (
                  <LinearGradient
                    colors={[COLORS.accent, '#FF8A50']}
                    style={styles.badge}
                    start={{ x: 0, y: 0 }}
                    end={{ x: 1, y: 0 }}
                  >
                    <Text style={styles.badgeText}>POPÜLER</Text>
                  </LinearGradient>
                )}
              </View>
            )}
          </View>

          {/* İçerik */}
          <View style={styles.content}>
            {/* Bitki Adı */}
            <Text style={styles.plantName} numberOfLines={1}>
              {plant.name}
            </Text>
            <Text style={styles.latinName} numberOfLines={1}>
              {plant.latinName}
            </Text>

            {/* Açıklama */}
            <Text style={styles.description} numberOfLines={2}>
              {plant.description}
            </Text>

            {/* Alt Bilgiler */}
            <View style={styles.footer}>
              {/* Bakım Bilgisi */}
              <View style={styles.careInfo}>
                <View style={styles.careItem}>
                  <Ionicons name="water" size={12} color={COLORS.secondary} />
                  <Text style={styles.careText}>Kolay</Text>
                </View>
                <View style={styles.careItem}>
                  <Ionicons name="sunny" size={12} color={COLORS.accent} />
                  <Text style={styles.careText}>Orta</Text>
                </View>
              </View>

              {/* Detay Butonu */}
              <TouchableOpacity style={styles.detailButton} onPress={handleCardPress}>
                <Ionicons name="arrow-forward" size={14} color={COLORS.white} />
              </TouchableOpacity>
            </View>
          </View>

          {/* Dekoratif Elementler */}
          <View style={styles.decorativeElement1} />
          <View style={styles.decorativeElement2} />
        </LinearGradient>
      </TouchableOpacity>
    </Animated.View>
  );
};

export default ModernPlantCard;
