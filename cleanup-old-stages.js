// Eski plant_stages collection'ını temizleme
const { initializeApp } = require('firebase/app');
const { getFirestore, collection, getDocs, deleteDoc, doc } = require('firebase/firestore');

const firebaseConfig = {
  apiKey: "AIzaSyCT9Tn0Z7MVjKeonhNuovpdbXVz7syQwJ8",
  authDomain: "wikiplant-aebaf.firebaseapp.com",
  projectId: "wikiplant-aebaf",
  storageBucket: "wikiplant-aebaf.firebasestorage.app",
  messagingSenderId: "847754813681",
  appId: "1:847754813681:web:22559d4547eca2ab594950",
  measurementId: "G-27R9QPVXF6"
};

const app = initializeApp(firebaseConfig);
const db = getFirestore(app);

async function cleanupOldStages() {
  try {
    console.log('🧹 Eski plant_stages collection\'ı temizleniyor...');
    
    const stagesSnapshot = await getDocs(collection(db, 'plant_stages'));
    
    for (const stageDoc of stagesSnapshot.docs) {
      await deleteDoc(doc(db, 'plant_stages', stageDoc.id));
      console.log(`🗑️ Stage silindi: ${stageDoc.id}`);
    }
    
    console.log(`✅ ${stagesSnapshot.size} adet eski stage silindi`);
    console.log('🎉 Temizlik tamamlandı!');
    
  } catch (error) {
    console.error('❌ Hata:', error);
  }
}

cleanupOldStages();
