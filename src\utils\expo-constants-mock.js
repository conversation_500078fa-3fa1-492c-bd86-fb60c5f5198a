// Mock for expo-constants to avoid URL.protocol Hermes errors
import { Platform } from 'react-native';

// Mock Constants object
const Constants = {
  // Basic app info
  appOwnership: 'expo',
  debugMode: __DEV__,
  experienceUrl: 'exp://localhost:8081',
  expoVersion: '53.0.0',
  installationId: 'mock-installation-id',
  isDevice: !Platform.OS === 'web',
  platform: {
    ios: Platform.OS === 'ios' ? {
      buildNumber: '1',
      platform: 'ios',
      model: 'iPhone',
      systemVersion: '17.0'
    } : undefined,
    android: Platform.OS === 'android' ? {
      versionCode: 1,
      platform: 'android'
    } : undefined,
    web: Platform.OS === 'web' ? {
      platform: 'web'
    } : undefined
  },
  
  // Mock manifest without URL parsing
  manifest: {
    id: 'mock-app-id',
    name: '<PERSON><PERSON><PERSON>',
    slug: 'dallardan-bilgi',
    version: '1.0.0',
    sdkVersion: '53.0.0',
    platforms: ['ios', 'android'],
    orientation: 'portrait',
    icon: './assets/wikiplantlogo.png',
    splash: {
      image: './assets/splash-icon.png',
      resizeMode: 'contain',
      backgroundColor: '#ffffff'
    }
  },

  // Mock manifest2 without URL parsing
  manifest2: {
    id: 'mock-app-id',
    name: 'Dallardan Bilgi',
    slug: 'dallardan-bilgi',
    version: '1.0.0',
    runtimeVersion: '1.0.0',
    extra: {}
  },

  // Session info
  sessionId: 'mock-session-id',
  statusBarHeight: Platform.OS === 'ios' ? 44 : 24,
  systemFonts: [],
  
  // Device info
  deviceName: 'Mock Device',
  deviceYearClass: 2023,
  
  // Mock functions that might use URL parsing
  getWebViewUserAgentAsync: async () => 'MockUserAgent/1.0',
  
  // Execution environment
  executionEnvironment: 'storeClient'
};

// Mock functions that might cause URL.protocol errors
const mockFunctions = {
  // Mock getManifestBaseUri without URL parsing
  getManifestBaseUri: () => {
    return 'exp://localhost:8081';
  },
  
  // Mock other URL-related functions
  getInitialURL: () => null,
  
  // Mock linking functions
  makeUrl: (path) => `exp://localhost:8081${path}`,
  
  // Mock asset functions
  getAssetByID: (id) => null
};

// Combine constants and functions
const ExpoConstants = {
  ...Constants,
  ...mockFunctions,
  default: Constants
};

export default ExpoConstants;
export { Constants as default };
