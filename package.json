{"name": "wikiplant-admin-api", "version": "1.0.0", "description": "External Admin API for WikiPlant Data Loading", "main": "external-admin-api.js", "scripts": {"start": "node external-admin-api.js", "dev": "nodemon external-admin-api.js"}, "dependencies": {"cors": "^2.8.5", "expo": "^53.0.11", "express": "^4.21.2", "firebase": "^10.14.1"}, "devDependencies": {"nodemon": "^3.0.2"}, "keywords": ["wikiplant", "admin", "api", "firebase", "data-loading"], "author": "WikiPlant Team", "license": "MIT"}