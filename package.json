{"name": "wikiplant", "version": "1.0.0", "main": "App.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"@expo/vector-icons": "^14.0.4", "@react-native-async-storage/async-storage": "~2.1.0", "@react-navigation/bottom-tabs": "^6.6.1", "@react-navigation/native": "^6.1.18", "@react-navigation/stack": "^6.4.1", "expo": "~53.0.0", "expo-linear-gradient": "~14.0.0", "expo-print": "^14.1.4", "expo-sharing": "^13.1.5", "expo-status-bar": "~2.0.0", "firebase": "^10.14.1", "react": "18.3.1", "react-native": "0.76.1", "react-native-gesture-handler": "~2.20.2", "react-native-safe-area-context": "4.12.0", "react-native-screens": "~4.1.0"}, "devDependencies": {"@babel/core": "^7.25.0"}, "private": true}