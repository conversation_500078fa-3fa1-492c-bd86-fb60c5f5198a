{"name": "wikiplant", "version": "1.0.0", "main": "App.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"expo": "~51.0.28", "expo-status-bar": "~1.12.1", "react": "18.2.0", "react-native": "0.74.5", "react-native-vector-icons": "^10.0.3", "@expo/vector-icons": "^14.0.2", "react-navigation": "^4.4.4", "@react-navigation/native": "^6.1.9", "@react-navigation/stack": "^6.3.20", "react-native-screens": "~3.31.1", "react-native-safe-area-context": "4.10.5", "react-native-gesture-handler": "~2.16.1", "@react-native-async-storage/async-storage": "1.23.1", "firebase": "^10.7.1"}, "devDependencies": {"@babel/core": "^7.20.0"}, "private": true}