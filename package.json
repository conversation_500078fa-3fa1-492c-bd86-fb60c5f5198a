{"name": "wikiplant", "version": "1.0.0", "main": "index.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"@expo/vector-icons": "^14.0.0", "@react-native-async-storage/async-storage": "^1.23.1", "@react-navigation/bottom-tabs": "^6.5.11", "@react-navigation/native": "^6.1.9", "@react-navigation/stack": "^6.3.20", "expo": "^53.0.9", "expo-linear-gradient": "~14.0.1", "expo-print": "^14.1.4", "expo-sharing": "^13.1.5", "expo-status-bar": "~2.2.3", "react": "19.0.0", "react-native": "0.79.2", "react-native-gesture-handler": "~2.20.2", "react-native-safe-area-context": "4.12.0", "react-native-screens": "~4.1.0", "react-native-vector-icons": "^10.0.3"}, "devDependencies": {"@babel/core": "^7.20.0"}, "private": true}