{"name": "wikiplant", "version": "1.0.0", "main": "App.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"@expo/vector-icons": "^14.0.4", "@react-native-async-storage/async-storage": "~2.1.0", "@react-navigation/bottom-tabs": "^6.6.1", "@react-navigation/native": "^6.1.18", "@react-navigation/stack": "^6.4.1", "expo": "~51.0.28", "expo-linear-gradient": "~13.0.2", "expo-status-bar": "~1.12.1", "firebase": "^10.14.1", "react": "18.2.0", "react-native": "0.74.5", "react-native-gesture-handler": "~2.16.1", "react-native-safe-area-context": "4.10.5", "react-native-screens": "~3.31.1"}, "devDependencies": {"@babel/core": "^7.25.0"}, "private": true}