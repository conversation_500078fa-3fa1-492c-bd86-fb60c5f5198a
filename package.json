{"name": "wikiplant", "version": "1.0.0", "main": "App.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"@expo/vector-icons": "^14.0.4", "@react-native-async-storage/async-storage": "~2.1.0", "@react-navigation/bottom-tabs": "^6.6.1", "@react-navigation/native": "^6.1.18", "@react-navigation/stack": "^6.4.1", "expo": "~53.0.0", "expo-linear-gradient": "~14.1.5", "expo-status-bar": "~2.2.3", "firebase": "^9.23.0", "react": "19.0.0", "react-native": "0.79.3", "react-native-gesture-handler": "~2.24.0", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-url-polyfill": "^2.0.0"}, "devDependencies": {"@babel/core": "^7.25.0", "@babel/plugin-proposal-class-properties": "^7.18.6", "@babel/plugin-transform-class-properties": "^7.27.1", "@babel/plugin-transform-flow-strip-types": "^7.27.1", "@babel/plugin-transform-private-methods": "^7.27.1"}, "private": true}