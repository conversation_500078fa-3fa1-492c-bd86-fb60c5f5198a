// Enhanced sample data for different plant types with varying stages and treatments

export const ENHANCED_PLANT_SAMPLES = [
  {
    id: 'rose_premium',
    name: 'Premium Gül',
    latinName: 'Rosa damascena',
    category: '<PERSON>i<PERSON><PERSON>',
    description: '<PERSON><PERSON><PERSON> kokulu, çok katmanlı çiçeklere sahip premium gül çeşidi.',
    origin: 'Bulgaristan',
    lifespan: 'Çok yıllık',
    size: '1.5-2 metre',
    difficulty: 'Orta',
    isNew: true,
    isPopular: true,
    mainImage: 'https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=400&h=400&fit=crop',
    images: [
      'https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=400&h=400&fit=crop',
      'https://images.unsplash.com/photo-1441974231531-c6227db76b6e?w=400&h=400&fit=crop'
    ],
    customStages: [
      {
        name: '<PERSON><PERSON> Hazırlığı',
        description: '<PERSON><PERSON><PERSON> tohum<PERSON><PERSON>nın soğuk stratifikasyon işlemi',
        duration: { min: 60, max: 90, average: 75, unit: 'gün' },
        specialCare: ['Buzdolabında bekletme', 'Nem kontrolü']
      },
      {
        name: 'Çimlenme',
        description: 'İlk filizlerin çıkması',
        duration: { min: 14, max: 21, average: 18, unit: 'gün' },
        specialCare: ['Sıcaklık kontrolü', 'Düzenli nem']
      },
      {
        name: 'Fide Gelişimi',
        description: 'İlk gerçek yaprakların oluşması',
        duration: { min: 30, max: 45, average: 35, unit: 'gün' },
        specialCare: ['Hafif gübre', 'Dolaylı ışık']
      },
      {
        name: 'Güçlenme',
        description: 'Kök sisteminin gelişmesi ve güçlenmesi',
        duration: { min: 45, max: 60, average: 52, unit: 'gün' },
        specialCare: ['Düzenli sulama', 'Gübre artırımı']
      },
      {
        name: 'Dallanma',
        description: 'Yan dalların oluşması ve büyümesi',
        duration: { min: 30, max: 45, average: 38, unit: 'gün' },
        specialCare: ['Budama', 'Destek çubukları']
      },
      {
        name: 'Tomurcuklanma',
        description: 'İlk çiçek tomurcuklarının oluşması',
        duration: { min: 21, max: 35, average: 28, unit: 'gün' },
        specialCare: ['Fosfor gübresi', 'Su kontrolü']
      },
      {
        name: 'Çiçeklenme',
        description: 'Çiçeklerin açması ve tam çiçeklenme',
        duration: { min: 30, max: 60, average: 45, unit: 'gün' },
        specialCare: ['Düzenli sulama', 'Solmuş çiçek temizliği']
      },
      {
        name: 'Meyve Oluşumu',
        description: 'Kuşburnu oluşumu (isteğe bağlı)',
        duration: { min: 60, max: 90, average: 75, unit: 'gün' },
        specialCare: ['Çiçekleri bırakma', 'Beslenme desteği']
      }
    ],
    customTreatments: [
      {
        problem: 'Gül Biti',
        category: 'Zararlı',
        severity: 'Orta',
        description: 'Gül bitkilerine özel yaprak biti türü',
        organicSolutions: ['Sabunlu su', 'Neem yağı', 'Uğur böceği'],
        chemicalSolutions: ['İmidakloprid', 'Thiamethoxam'],
        preventiveMeasures: ['Düzenli kontrol', 'Nem ayarı', 'Temiz çevre']
      },
      {
        problem: 'Külleme',
        category: 'Hastalık',
        severity: 'Yüksek',
        description: 'Yapraklarda beyaz pudra görünümü',
        organicSolutions: ['Karbonat çözeltisi', 'Süt karışımı'],
        chemicalSolutions: ['Bakır sülfat', 'Fungisit'],
        preventiveMeasures: ['Havalandırma', 'Yapraktan sulama yapmama']
      },
      {
        problem: 'Siyah Leke',
        category: 'Hastalık',
        severity: 'Yüksek',
        description: 'Yapraklarda siyah lekeler ve sararmalar',
        organicSolutions: ['Etkilenen yaprakları temizleme', 'Hava sirkülasyonu'],
        chemicalSolutions: ['Sistemik fungisit', 'Bakır bazlı ilaç'],
        preventiveMeasures: ['Kış temizliği', 'Uygun sulama']
      }
    ]
  },
  {
    id: 'tomato_heirloom',
    name: 'Eski Çeşit Domates',
    latinName: 'Solanum lycopersicum var. heirloom',
    category: 'Sebze',
    description: 'Geleneksel lezzeti korunmuş, büyük meyveli domates çeşidi.',
    origin: 'İtalya',
    lifespan: 'Yıllık',
    size: '1.5-2.5 metre',
    difficulty: 'Orta',
    isNew: false,
    isPopular: true,
    customStages: [
      {
        name: 'Tohum Ekimi',
        description: 'Tohum tepsilerine ekim',
        duration: { min: 1, max: 3, average: 2, unit: 'gün' },
        specialCare: ['Sıcak ortam', 'Nemli toprak']
      },
      {
        name: 'Çimlenme',
        description: 'İlk kotiledon yaprakların çıkması',
        duration: { min: 5, max: 10, average: 7, unit: 'gün' },
        specialCare: ['22-25°C sıcaklık', 'Dolaylı ışık']
      },
      {
        name: 'Gerçek Yaprak',
        description: 'İlk gerçek yaprakların oluşması',
        duration: { min: 10, max: 14, average: 12, unit: 'gün' },
        specialCare: ['Işık artırımı', 'Hafif gübre']
      },
      {
        name: 'Fide Büyümesi',
        description: 'Fidelerin güçlenmesi ve büyümesi',
        duration: { min: 21, max: 35, average: 28, unit: 'gün' },
        specialCare: ['Düzenli sulama', 'Sertleştirme']
      },
      {
        name: 'Dikim',
        description: 'Nihai yerine dikim',
        duration: { min: 1, max: 3, average: 2, unit: 'gün' },
        specialCare: ['Toprak hazırlığı', 'Destek sistemi']
      },
      {
        name: 'Büyüme',
        description: 'Hızlı büyüme ve dallanma',
        duration: { min: 30, max: 45, average: 38, unit: 'gün' },
        specialCare: ['Düzenli sulama', 'Budama', 'Bağlama']
      },
      {
        name: 'Çiçeklenme',
        description: 'İlk çiçek salkımlarının oluşması',
        duration: { min: 14, max: 21, average: 18, unit: 'gün' },
        specialCare: ['Fosfor gübresi', 'Tozlaşma desteği']
      },
      {
        name: 'Meyve Tutumu',
        description: 'Küçük meyvelerin oluşması',
        duration: { min: 7, max: 14, average: 10, unit: 'gün' },
        specialCare: ['Su kontrolü', 'Beslenme desteği']
      },
      {
        name: 'Meyve Gelişimi',
        description: 'Meyvelerin büyümesi',
        duration: { min: 30, max: 45, average: 38, unit: 'gün' },
        specialCare: ['Düzenli sulama', 'Potasyum gübresi']
      },
      {
        name: 'Olgunlaşma',
        description: 'Meyvelerin olgunlaşması ve hasat',
        duration: { min: 14, max: 28, average: 21, unit: 'gün' },
        specialCare: ['Su azaltma', 'Hasat zamanlaması']
      }
    ],
    customTreatments: [
      {
        problem: 'Geç Yanıklık',
        category: 'Hastalık',
        severity: 'Kritik',
        description: 'Yaprak ve meyvelerde kahverengi lekeler',
        organicSolutions: ['Bakır sülfat', 'Havalandırma artırımı'],
        chemicalSolutions: ['Sistemik fungisit', 'Koruyucu ilaçlama'],
        preventiveMeasures: ['Drip sulama', 'Mulçlama', 'Çeşit seçimi']
      },
      {
        problem: 'Beyaz Sinek',
        category: 'Zararlı',
        severity: 'Orta',
        description: 'Yaprak altında beyaz küçük sinekler',
        organicSolutions: ['Sarı tuzaklar', 'Sabunlu su'],
        chemicalSolutions: ['İmidakloprid', 'Spiromesifen'],
        preventiveMeasures: ['Sera hijyeni', 'Karantina']
      }
    ]
  },
  {
    id: 'lavender_english',
    name: 'İngiliz Lavantası',
    latinName: 'Lavandula angustifolia',
    category: 'Aromatik',
    description: 'Yoğun kokulu, mor çiçekli, tıbbi özellikli lavanta çeşidi.',
    origin: 'Akdeniz',
    lifespan: 'Çok yıllık',
    size: '30-60 cm',
    difficulty: 'Kolay',
    isNew: false,
    isPopular: true,
    customStages: [
      {
        name: 'Tohum Stratifikasyonu',
        description: 'Soğuk işlem uygulaması',
        duration: { min: 21, max: 30, average: 25, unit: 'gün' },
        specialCare: ['Buzdolabında bekletme', 'Nem kontrolü']
      },
      {
        name: 'Çimlenme',
        description: 'Yavaş çimlenme süreci',
        duration: { min: 14, max: 28, average: 21, unit: 'gün' },
        specialCare: ['Sabır', 'Sabit sıcaklık']
      },
      {
        name: 'Fide Gelişimi',
        description: 'Yavaş ama sağlam büyüme',
        duration: { min: 60, max: 90, average: 75, unit: 'gün' },
        specialCare: ['Az sulama', 'Bol ışık']
      },
      {
        name: 'Gençlik',
        description: 'İlk yıl büyümesi',
        duration: { min: 180, max: 365, average: 270, unit: 'gün' },
        specialCare: ['Kış koruması', 'Hafif budama']
      },
      {
        name: 'Olgunluk',
        description: 'Tam gelişmiş bitki',
        duration: { min: 365, max: 730, average: 545, unit: 'gün' },
        specialCare: ['Yıllık budama', 'Minimal bakım']
      },
      {
        name: 'Çiçeklenme',
        description: 'Yaz çiçeklenmesi',
        duration: { min: 45, max: 90, average: 65, unit: 'gün' },
        specialCare: ['Su kısıtlaması', 'Hasat zamanlaması']
      }
    ],
    customTreatments: [
      {
        problem: 'Kök Çürümesi',
        category: 'Hastalık',
        severity: 'Yüksek',
        description: 'Aşırı sulama sonucu kök çürümesi',
        organicSolutions: ['Drenaj iyileştirme', 'Toprak değişimi'],
        chemicalSolutions: ['Fungisit uygulaması'],
        preventiveMeasures: ['Az sulama', 'İyi drenaj', 'Uygun toprak']
      }
    ]
  }
];

export const STAGE_TEMPLATES = {
  FLOWER: [
    'Tohum', 'Çimlenme', 'Fide', 'Yapraklanma', 'Büyüme', 
    'Dallanma', 'Tomurcuklanma', 'Çiçeklenme', 'Meyve/Tohum', 'Olgunluk'
  ],
  VEGETABLE: [
    'Tohum', 'Çimlenme', 'Kotiledon', 'Gerçek Yaprak', 'Büyüme',
    'Çiçeklenme', 'Meyve Tutumu', 'Meyve Gelişimi', 'Olgunlaşma', 'Hasat'
  ],
  AROMATIC: [
    'Tohum', 'Çimlenme', 'Fide', 'Büyüme', 'Dallanma',
    'Çiçeklenme', 'Hasat', 'Dinlenme'
  ],
  SUCCULENT: [
    'Tohum/Çelik', 'Köklendirme', 'Küçük Rozet', 'Büyüme',
    'Olgunluk', 'Çiçeklenme', 'Dinlenme'
  ]
};

export const TREATMENT_TEMPLATES = {
  COMMON: [
    'Yaprak Sararmasi', 'Aşırı Sulama', 'Az Sulama', 'Işık Eksikliği',
    'Beslenme Eksikliği', 'Sıcaklık Stresi'
  ],
  FLOWER: [
    'Yaprak Biti', 'Külleme', 'Siyah Leke', 'Thrips', 'Çiçek Dökümü'
  ],
  VEGETABLE: [
    'Geç Yanıklık', 'Beyaz Sinek', 'Yaprak Biti', 'Mildiyö', 'Virus'
  ],
  AROMATIC: [
    'Kök Çürümesi', 'Yaprak Lekesi', 'Böcek Zararı'
  ],
  SUCCULENT: [
    'Kök Çürümesi', 'Yumuşak Çürüme', 'Ölçek Böceği', 'Aşırı Büyüme'
  ]
};
