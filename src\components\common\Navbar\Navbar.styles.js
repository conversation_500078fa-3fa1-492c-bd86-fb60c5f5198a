import { StyleSheet } from 'react-native';
import { COLORS, FONT_SIZES, SPACING } from '../../../utils/constants';

export default StyleSheet.create({
  navbar: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.sm,
    backgroundColor: COLORS.white,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.lightGray,
    height: 56,
  },
  
  navButton: {
    width: 40,
    height: 40,
    alignItems: 'center',
    justifyContent: 'center',
  },
  
  titleContainer: {
    flex: 1,
    alignItems: 'center',
    paddingHorizontal: SPACING.sm,
  },
  
  title: {
    fontSize: FONT_SIZES.large,
    fontWeight: '600',
    color: COLORS.primary,
  },
});
