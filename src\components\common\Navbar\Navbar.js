import React from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { COLORS } from '../../../utils/constants';
import styles from './Navbar.styles.js';

const Navbar = ({
  title,
  leftIcon,
  rightIcon,
  onLeftPress,
  onRightPress,
  style,
  ...props
}) => {
  return (
    <View style={[styles.navbar, style]} {...props}>
      {/* Left Button */}
      <TouchableOpacity
        style={styles.navButton}
        onPress={onLeftPress}
        disabled={!onLeftPress}
      >
        {leftIcon && (
          <Ionicons
            name={leftIcon}
            size={24}
            color={onLeftPress ? COLORS.primary : 'transparent'}
          />
        )}
      </TouchableOpacity>

      {/* Title */}
      <View style={styles.titleContainer}>
        <Text style={styles.title} numberOfLines={1}>
          {title}
        </Text>
      </View>

      {/* Right Button */}
      <TouchableOpacity
        style={styles.navButton}
        onPress={onRightPress}
        disabled={!onRightPress}
      >
        {rightIcon && (
          <Ionicons
            name={rightIcon}
            size={24}
            color={onRightPress ? COLORS.primary : 'transparent'}
          />
        )}
      </TouchableOpacity>
    </View>
  );
};

export default Navbar;
