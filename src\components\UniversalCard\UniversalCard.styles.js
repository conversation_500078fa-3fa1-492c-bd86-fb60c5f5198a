import { StyleSheet, Dimensions } from 'react-native';
import { COLORS, FONT_SIZES, SPACING } from '../../utils/constants';

const { width } = Dimensions.get('window');
const cardWidth = width - SPACING.lg * 2; // Tam geni<PERSON>
const maxCardWidth = 400; // Maksimum kart genişliği
const finalCardWidth = Math.min(cardWidth, maxCardWidth);

export default StyleSheet.create({
  card: {
    width: finalCardWidth,
    height: 160,
    borderRadius: 24,
    marginBottom: SPACING.lg,
    marginHorizontal: 'auto',
    alignSelf: 'center',
    shadowColor: COLORS.shadow,
    shadowOffset: {
      width: 0,
      height: 8,
    },
    shadowOpacity: 0.3,
    shadowRadius: 16,
    elevation: 12,
    overflow: 'hidden',
  },

  gradientBackground: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'stretch',
    padding: SPACING.lg,
    borderRadius: 24,
    overflow: 'hidden',
    backgroundColor: 'rgba(124, 77, 37, 0.9)', // <PERSON>hverengi arka plan
  },

  cardPressed: {
    shadowOffset: {
      width: 0,
      height: 12,
    },
    shadowOpacity: 0.4,
    shadowRadius: 20,
    elevation: 16,
  },

  imageContainer: {
    width: 120,
    height: 120,
    borderRadius: 20,
    overflow: 'hidden',
  },
  
  image: {
    width: '100%',
    height: '100%',
  },

  textContainer: {
    flex: 1,
    paddingLeft: SPACING.md,
    paddingRight: SPACING.sm,
    paddingBottom: SPACING.xxl, // Detay butonu için alt boşluk
    justifyContent: 'space-between',
    minHeight: 0, // Flex shrink için
  },

  contentArea: {
    flex: 1,
    justifyContent: 'center',
    paddingRight: SPACING.md, // Sağ tarafta daha fazla boşluk
  },

  category: {
    fontSize: FONT_SIZES.small - 1,
    color: COLORS.white,
    fontWeight: '500',
    opacity: 0.8,
    textTransform: 'uppercase',
    letterSpacing: 0.8,
    marginBottom: SPACING.xs,
  },

  title: {
    fontSize: FONT_SIZES.large + 2,
    color: COLORS.white,
    fontWeight: '700',
    marginBottom: SPACING.xs,
    lineHeight: FONT_SIZES.large + 6,
  },

  subtitle: {
    fontSize: FONT_SIZES.small,
    color: COLORS.white,
    fontStyle: 'italic',
    opacity: 0.85,
    lineHeight: FONT_SIZES.small + 4,
  },



  detailButton: {
    position: 'absolute',
    bottom: SPACING.md,
    right: SPACING.md,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#4CAF50', // Yeşil arka plan
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.sm,
    borderRadius: 20,
    gap: 6,
    shadowColor: COLORS.shadow,
    shadowOffset: {
      width: 0,
      height: 3,
    },
    shadowOpacity: 0.3,
    shadowRadius: 6,
    elevation: 5,
  },

  detailButtonText: {
    fontSize: FONT_SIZES.small - 1,
    color: COLORS.white,
    fontWeight: '600',
  },
});
