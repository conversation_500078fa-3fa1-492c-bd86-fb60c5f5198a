import { StyleSheet } from 'react-native';
import { COLORS, FONT_SIZES, SPACING } from '../../utils/constants';

export default StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },

  header: {
    backgroundColor: '#FC7138',
    paddingVertical: SPACING.xl,
    paddingHorizontal: SPACING.lg,
    borderBottomLeftRadius: 20,
    borderBottomRightRadius: 20,
  },

  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: SPACING.md,
  },

  headerText: {
    flex: 1,
  },

  headerTitle: {
    fontSize: FONT_SIZES.header + 4,
    fontWeight: '700',
    color: COLORS.white,
  },

  headerSubtitle: {
    fontSize: FONT_SIZES.medium,
    color: COLORS.white,
    opacity: 0.9,
  },

  content: {
    flex: 1,
    padding: SPACING.lg,
  },

  section: {
    marginBottom: SPACING.xl,
  },

  sectionTitle: {
    fontSize: FONT_SIZES.large,
    fontWeight: '600',
    color: COLORS.primary,
    marginBottom: SPACING.md,
  },

  buttonGrid: {
    gap: SPACING.md,
  },

  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: SPACING.lg,
    paddingHorizontal: SPACING.xl,
    borderRadius: 16,
    gap: SPACING.sm,
    shadowColor: COLORS.shadow,
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 6,
  },

  plantButton: {
    backgroundColor: COLORS.secondary,
  },

  articleButton: {
    backgroundColor: COLORS.accent,
  },

  categoryButton: {
    backgroundColor: COLORS.primary,
  },

  fullButton: {
    backgroundColor: '#2E7D32',
  },

  buttonText: {
    fontSize: FONT_SIZES.large,
    fontWeight: '600',
    color: COLORS.white,
  },

  infoCard: {
    backgroundColor: COLORS.white,
    borderRadius: 12,
    padding: SPACING.lg,
    borderWidth: 1,
    borderColor: COLORS.lightGray,
    gap: SPACING.sm,
  },

  infoRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: SPACING.sm,
  },

  infoText: {
    fontSize: FONT_SIZES.medium,
    color: COLORS.darkGray,
    fontWeight: '500',
  },

  logsHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SPACING.md,
  },

  clearButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: SPACING.xs,
    paddingHorizontal: SPACING.sm,
    paddingVertical: SPACING.xs,
    backgroundColor: COLORS.lightGray,
    borderRadius: 8,
  },

  clearButtonText: {
    fontSize: FONT_SIZES.small,
    color: COLORS.darkGray,
    fontWeight: '500',
  },

  logsContainer: {
    backgroundColor: COLORS.white,
    borderRadius: 12,
    padding: SPACING.md,
    minHeight: 200,
    maxHeight: 400,
    borderWidth: 1,
    borderColor: COLORS.lightGray,
  },

  noLogsText: {
    textAlign: 'center',
    color: COLORS.gray,
    fontStyle: 'italic',
    marginTop: SPACING.xl,
  },

  logItem: {
    paddingVertical: SPACING.sm,
    paddingHorizontal: SPACING.md,
    marginBottom: SPACING.xs,
    borderRadius: 8,
    borderLeftWidth: 4,
  },

  logInfo: {
    backgroundColor: '#E3F2FD',
    borderLeftColor: '#2196F3',
  },

  logSuccess: {
    backgroundColor: '#E8F5E8',
    borderLeftColor: '#4CAF50',
  },

  logError: {
    backgroundColor: '#FFEBEE',
    borderLeftColor: '#F44336',
  },

  logTimestamp: {
    fontSize: FONT_SIZES.small,
    color: COLORS.gray,
    marginBottom: SPACING.xs,
  },

  logMessage: {
    fontSize: FONT_SIZES.medium,
    color: COLORS.darkGray,
    lineHeight: 20,
  },

  loadingContainer: {
    alignItems: 'center',
    paddingVertical: SPACING.xl,
    backgroundColor: COLORS.white,
    borderRadius: 12,
    marginTop: SPACING.lg,
  },

  loadingText: {
    marginTop: SPACING.md,
    fontSize: FONT_SIZES.medium,
    color: COLORS.primary,
    fontWeight: '500',
  },
});
