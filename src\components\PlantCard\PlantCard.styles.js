import { StyleSheet, Dimensions } from 'react-native';
import { COLORS, FONT_SIZES, SPACING } from '../../utils/constants';

const { width } = Dimensions.get('window');
const cardWidth = width - SPACING.lg * 2; // Tam geni<PERSON>
const maxCardWidth = 400; // Maksimum kart genişliği
const finalCardWidth = Math.min(cardWidth, maxCardWidth);

export default StyleSheet.create({
  card: {
    width: finalCardWidth,
    height: 160,
    borderRadius: 24,
    marginBottom: SPACING.lg,
    marginHorizontal: 'auto',
    alignSelf: 'center',
    shadowColor: COLORS.shadow,
    shadowOffset: {
      width: 0,
      height: 8,
    },
    shadowOpacity: 0.3,
    shadowRadius: 16,
    elevation: 12,
    overflow: 'hidden',
  },

  gradientBackground: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'stretch',
    padding: SPACING.lg,
    borderRadius: 24,
    overflow: 'hidden',
    backgroundColor: 'rgba(124, 77, 37, 0.9)', // <PERSON>hverengi arka plan
  },

  cardPressed: {
    shadowOffset: {
      width: 0,
      height: 12,
    },
    shadowOpacity: 0.4,
    shadowRadius: 20,
    elevation: 16,
  },

  cardTouchable: {
    flex: 1,
  },

  textContainer: {
    flex: 1,
    paddingLeft: SPACING.md,
    paddingRight: SPACING.sm,
    paddingBottom: SPACING.xxl, // Detay butonu için alt boşluk
    justifyContent: 'space-between',
    minHeight: 0, // Flex shrink için
  },

  contentArea: {
    flex: 1,
    justifyContent: 'space-between',
    paddingRight: SPACING.md, // Sağ tarafta daha fazla boşluk
  },

  topSection: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: SPACING.xs,
  },

  category: {
    fontSize: FONT_SIZES.small - 2,
    color: COLORS.white,
    fontWeight: '500',
    opacity: 0.7,
    textTransform: 'uppercase',
    letterSpacing: 0.8,
  },

  statusBadge: {
    backgroundColor: 'rgba(255, 255, 255, 0.15)',
    paddingHorizontal: SPACING.xs,
    paddingVertical: 2,
    borderRadius: 8,
  },

  statusText: {
    fontSize: FONT_SIZES.small - 3,
    color: COLORS.white,
    fontWeight: '600',
    letterSpacing: 0.5,
  },

  title: {
    fontSize: FONT_SIZES.large,
    color: COLORS.white,
    fontWeight: '700',
    marginBottom: 2,
  },

  latinName: {
    fontSize: FONT_SIZES.small - 1,
    color: COLORS.white,
    fontStyle: 'italic',
    opacity: 0.75,
    marginBottom: SPACING.xs,
  },

  description: {
    fontSize: FONT_SIZES.small - 1,
    color: COLORS.white,
    lineHeight: 16,
    opacity: 0.8,
    marginBottom: SPACING.sm,
  },

  careSection: {
    flexDirection: 'row',
    gap: SPACING.xs,
    marginTop: 'auto',
  },

  careBubble: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(124, 77, 37, 0.8)', // Kahverengi
    paddingHorizontal: SPACING.sm,
    paddingVertical: SPACING.xs,
    borderRadius: 16,
    gap: 4,
  },

  careText: {
    fontSize: FONT_SIZES.small - 2,
    color: COLORS.white,
    fontWeight: '500',
  },

  imageContainer: {
    width: 120,
    height: 120,
    position: 'relative',
    borderRadius: 20,
    overflow: 'hidden',
  },

  image: {
    width: '100%',
    height: '100%',
  },

  detailButton: {
    position: 'absolute',
    bottom: SPACING.md,
    right: SPACING.md,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#4CAF50', // Yeşil arka plan
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.sm,
    borderRadius: 20,
    gap: 6,
    shadowColor: COLORS.shadow,
    shadowOffset: {
      width: 0,
      height: 3,
    },
    shadowOpacity: 0.3,
    shadowRadius: 6,
    elevation: 5,
  },

  detailButtonText: {
    fontSize: FONT_SIZES.small - 1,
    color: COLORS.white,
    fontWeight: '600',
  },
});
