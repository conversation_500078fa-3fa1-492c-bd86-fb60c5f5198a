import { StyleSheet } from 'react-native';
import { COLORS, FONT_SIZES, SPACING } from '../../utils/constants';

export default StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.xl,
  },

  container: {
    backgroundColor: COLORS.white,
    borderRadius: 24,
    width: '95%',
    maxWidth: 500,
    height: '85%',
    shadowColor: COLORS.shadow,
    shadowOffset: {
      width: 0,
      height: 15,
    },
    shadowOpacity: 0.4,
    shadowRadius: 25,
    elevation: 15,
  },

  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: SPACING.xl,
    paddingVertical: SPACING.lg,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.lightGray,
    backgroundColor: '#FC7138',
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
  },

  headerTitle: {
    fontSize: FONT_SIZES.header,
    fontWeight: '700',
    color: COLORS.white,
    flex: 1,
    textAlign: 'center',
  },

  closeButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    alignItems: 'center',
    justifyContent: 'center',
  },

  content: {
    flex: 1,
    paddingHorizontal: SPACING.xl,
    paddingVertical: SPACING.lg,
  },

  filterSection: {
    marginBottom: SPACING.xl,
  },

  sectionTitle: {
    fontSize: FONT_SIZES.large,
    fontWeight: '700',
    color: COLORS.primary,
    marginBottom: SPACING.md,
    letterSpacing: 0.5,
  },

  optionsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: SPACING.sm,
  },

  optionButton: {
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.sm,
    borderRadius: 20,
    borderWidth: 2,
    borderColor: COLORS.lightGray,
    backgroundColor: COLORS.white,
    shadowColor: COLORS.shadow,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },

  selectedOption: {
    backgroundColor: COLORS.secondary,
    borderColor: COLORS.secondary,
    shadowOpacity: 0.2,
    elevation: 4,
  },

  optionText: {
    fontSize: FONT_SIZES.medium,
    color: COLORS.darkGray,
    fontWeight: '600',
  },

  selectedOptionText: {
    color: COLORS.white,
    fontWeight: '700',
  },

  switchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: SPACING.md,
    paddingHorizontal: SPACING.lg,
    backgroundColor: COLORS.white,
    borderRadius: 16,
    marginBottom: SPACING.sm,
    borderWidth: 2,
    borderColor: COLORS.lightGray,
    shadowColor: COLORS.shadow,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },

  switchLabel: {
    fontSize: FONT_SIZES.medium,
    color: COLORS.primary,
    fontWeight: '600',
    flex: 1,
  },

  footer: {
    flexDirection: 'row',
    paddingHorizontal: SPACING.xl,
    paddingVertical: SPACING.lg,
    borderTopWidth: 1,
    borderTopColor: COLORS.lightGray,
    gap: SPACING.md,
    backgroundColor: COLORS.background,
    borderBottomLeftRadius: 24,
    borderBottomRightRadius: 24,
  },

  resetButton: {
    flex: 1,
    backgroundColor: COLORS.lightGray,
    paddingVertical: SPACING.md,
    borderRadius: 16,
    alignItems: 'center',
    shadowColor: COLORS.shadow,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },

  resetButtonText: {
    fontSize: FONT_SIZES.medium,
    fontWeight: '700',
    color: COLORS.darkGray,
  },

  applyButton: {
    flex: 2,
    backgroundColor: '#FC7138',
    paddingVertical: SPACING.md,
    borderRadius: 16,
    alignItems: 'center',
    shadowColor: COLORS.shadow,
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 6,
  },

  applyButtonText: {
    fontSize: FONT_SIZES.medium,
    fontWeight: '700',
    color: COLORS.white,
    letterSpacing: 0.5,
  },
});
