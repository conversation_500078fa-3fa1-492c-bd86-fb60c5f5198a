// Firebase Auth Service
import { 
  signInWithEmailAndPassword, 
  createUserWithEmailAndPassword, 
  signOut, 
  onAuthStateChanged,
  updateProfile,
  sendPasswordResetEmail
} from 'firebase/auth';
import { auth } from '../config/firebase';

// Safe auth wrapper to prevent component not registered errors
const safeAuthOperation = async (operation) => {
  try {
    if (!auth) {
      throw new Error('Auth not initialized');
    }
    return await operation();
  } catch (error) {
    console.error('Auth operation error:', error);
    throw error;
  }
};

// Sign in with email and password
export const signInUser = async (email, password) => {
  return safeAuthOperation(async () => {
    const userCredential = await signInWithEmailAndPassword(auth, email, password);
    return userCredential.user;
  });
};

// Create user with email and password
export const signUpUser = async (email, password, displayName = '') => {
  return safeAuthOperation(async () => {
    const userCredential = await createUserWithEmailAndPassword(auth, email, password);
    const user = userCredential.user;
    
    // Update profile with display name if provided
    if (displayName) {
      await updateProfile(user, { displayName });
    }
    
    return user;
  });
};

// Sign out user
export const signOutUser = async () => {
  return safeAuthOperation(async () => {
    await signOut(auth);
  });
};

// Send password reset email
export const resetPassword = async (email) => {
  return safeAuthOperation(async () => {
    await sendPasswordResetEmail(auth, email);
  });
};

// Auth state listener
export const onAuthStateChange = (callback) => {
  try {
    if (!auth) {
      console.warn('Auth not initialized for state listener');
      return () => {}; // Return empty unsubscribe function
    }
    return onAuthStateChanged(auth, callback);
  } catch (error) {
    console.error('Auth state listener error:', error);
    return () => {}; // Return empty unsubscribe function
  }
};

// Get current user
export const getCurrentUser = () => {
  try {
    return auth?.currentUser || null;
  } catch (error) {
    console.error('Get current user error:', error);
    return null;
  }
};

// Check if user is authenticated
export const isAuthenticated = () => {
  try {
    return !!auth?.currentUser;
  } catch (error) {
    console.error('Is authenticated check error:', error);
    return false;
  }
};
