import { useState, useEffect } from 'react';
import { useFavorites as useFavoritesContext } from '../contexts/FavoritesContext';
import PlantDataService from '../services/plantDataService';

export const useFavorites = () => {
  return useFavoritesContext();
};

export const useFavoritePlants = () => {
  const { favorites, loading: favoritesLoading } = useFavoritesContext();
  const [favoritePlants, setFavoritePlants] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  const loadFavoritePlants = async () => {
    if (favorites.length === 0) {
      setFavoritePlants([]);
      setLoading(false);
      return;
    }

    try {
      setError(null);
      setLoading(true);

      // Tüm bitkileri al ve favorileri filtrele
      const response = await PlantDataService.getAllPlants();
      
      if (response.success) {
        const filteredPlants = response.data.filter(plant => 
          favorites.includes(plant.id)
        );
        setFavoritePlants(filteredPlants);
      } else {
        setError(response.error || 'Favori bitkiler yüklenirken hata oluştu');
      }
    } catch (err) {
      setError('Favori bitkiler yüklenirken hata oluştu');
      console.error('Favorite plants loading error:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (!favoritesLoading) {
      loadFavoritePlants();
    }
  }, [favorites, favoritesLoading]);

  return {
    favoritePlants,
    loading: loading || favoritesLoading,
    error,
    reloadFavoritePlants: loadFavoritePlants
  };
};

export const useFavoriteToggle = () => {
  const { toggleFavorite, isFavorite } = useFavoritesContext();
  const [loading, setLoading] = useState(false);

  const handleToggleFavorite = async (plantId) => {
    try {
      setLoading(true);
      await toggleFavorite(plantId);
    } catch (error) {
      console.error('Favori durumu değiştirilirken hata:', error);
    } finally {
      setLoading(false);
    }
  };

  return {
    toggleFavorite: handleToggleFavorite,
    isFavorite,
    loading
  };
};
