// Bitki gelişim aşamalarını Firebase'e ekleme
const { initializeApp } = require('firebase/app');
const { getFirestore, collection, addDoc, getDocs, query, where } = require('firebase/firestore');

const firebaseConfig = {
  apiKey: "AIzaSyCT9Tn0Z7MVjKeonhNuovpdbXVz7syQwJ8",
  authDomain: "wikiplant-aebaf.firebaseapp.com",
  projectId: "wikiplant-aebaf",
  storageBucket: "wikiplant-aebaf.firebasestorage.app",
  messagingSenderId: "847754813681",
  appId: "1:847754813681:web:22559d4547eca2ab594950",
  measurementId: "G-27R9QPVXF6"
};

const app = initializeApp(firebaseConfig);
const db = getFirestore(app);

// Bitki aşamaları verisi
const plantStages = {
  // Gül için a<PERSON>
  'Gül': [
    {
      stageOrder: 1,
      name: '<PERSON><PERSON>',
      title: '<PERSON><PERSON> Hazırlığı ve Ekim',
      description: '<PERSON><PERSON><PERSON> tohumları soğuk stratifikasyon işleminden sonra nemli toprakta ekilir. Tohumlar çimlenmeye başlamadan önce 2-4 hafta soğukta bekletilmelidir.',
      duration: '1-2 hafta',
      image: 'https://images.unsplash.com/photo-1416879595882-3373a0480b5b?w=300&h=200&fit=crop',
      tips: [
        'Tohumları 24 saat suda bekletin',
        'Soğuk stratifikasyon uygulayın',
        'Nemli ama ıslak olmayan toprak kullanın',
        'Sıcaklığı 18-22°C arasında tutun'
      ],
      careInstructions: {
        watering: 'Günde 1 kez hafif sulama',
        light: 'Dolaylı güneş ışığı',
        temperature: '18-22°C',
        humidity: '%60-70'
      }
    },
    {
      stageOrder: 2,
      name: 'Çimlenme',
      title: 'İlk Filizlerin Çıkışı',
      description: 'Tohumlar çimlenir ve ilk kotiledon yapraklar görünür. Bu aşamada bitki çok hassastır ve dikkatli bakım gerektirir.',
      duration: '1-2 hafta',
      image: 'https://images.unsplash.com/photo-1416879595882-3373a0480b5b?w=300&h=200&fit=crop',
      tips: [
        'Toprak nemini koruyun',
        'Doğrudan güneşten koruyun',
        'Havalandırma sağlayın',
        'Aşırı sulamadan kaçının'
      ],
      careInstructions: {
        watering: 'Günde 1 kez, sabah saatlerinde',
        light: 'Parlak dolaylı ışık',
        temperature: '20-24°C',
        humidity: '%65-75'
      }
    },
    {
      stageOrder: 3,
      name: 'Fide Gelişimi',
      title: 'Gerçek Yaprakların Oluşumu',
      description: 'İlk gerçek yapraklar çıkar ve kök sistemi güçlenir. Bitki daha dayanıklı hale gelir ve büyüme hızlanır.',
      duration: '2-4 hafta',
      image: 'https://images.unsplash.com/photo-1416879595882-3373a0480b5b?w=300&h=200&fit=crop',
      tips: [
        'Hafif gübre vermeye başlayın',
        'Güneş ışığını artırın',
        'Düzenli sulama yapın',
        'Zararlılara karşı kontrol edin'
      ],
      careInstructions: {
        watering: 'Haftada 3-4 kez',
        light: 'Günde 4-6 saat güneş',
        temperature: '18-25°C',
        humidity: '%60-70'
      }
    },
    {
      stageOrder: 4,
      name: 'Genç Bitki',
      title: 'Dallanma ve Güçlenme',
      description: 'Bitki dallanmaya başlar ve daha güçlü bir yapıya kavuşur. Bu aşamada şekil verme budaması yapılabilir.',
      duration: '2-3 ay',
      image: 'https://images.unsplash.com/photo-1416879595882-3373a0480b5b?w=300&h=200&fit=crop',
      tips: [
        'Hafif budama yapın',
        'Düzenli gübreleme',
        'Destek çubuğu kullanın',
        'Hastalık kontrolü yapın'
      ],
      careInstructions: {
        watering: 'Haftada 2-3 kez derin sulama',
        light: 'Günde 6-8 saat güneş',
        temperature: '15-25°C',
        humidity: '%50-60'
      }
    },
    {
      stageOrder: 5,
      name: 'Çiçeklenme',
      title: 'İlk Çiçeklerin Açması',
      description: 'Bitki olgunlaşır ve ilk çiçeklerini açar. Bu aşamada düzenli bakım ve gübreleme çok önemlidir.',
      duration: '4-6 ay',
      image: 'https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=300&h=200&fit=crop',
      tips: [
        'Çiçeklenme gübresi verin',
        'Solmuş çiçekleri temizleyin',
        'Su stresinden kaçının',
        'Zararlı kontrolü yapın'
      ],
      careInstructions: {
        watering: 'Haftada 2-3 kez, köklere odaklı',
        light: 'Günde 6-8 saat doğrudan güneş',
        temperature: '15-25°C',
        humidity: '%50-60'
      }
    },
    {
      stageOrder: 6,
      name: 'Olgun Bitki',
      title: 'Tam Gelişmiş Gül Bitkisi',
      description: 'Bitki tam olgunluğa ulaşır ve düzenli çiçek verir. Yıllık bakım rutini oluşturulmalıdır.',
      duration: 'Sürekli',
      image: 'https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=300&h=200&fit=crop',
      tips: [
        'Yıllık budama yapın',
        'Mevsimsel gübreleme',
        'Kış koruması sağlayın',
        'Düzenli sağlık kontrolü'
      ],
      careInstructions: {
        watering: 'Mevsime göre ayarlayın',
        light: 'Günde 6-8 saat güneş',
        temperature: 'Dış ortam sıcaklığı',
        humidity: 'Doğal nem oranı'
      }
    }
  ],
  
  // Domates için aşamalar
  'Domates': [
    {
      stageOrder: 1,
      name: 'Tohum Çimlenmesi',
      title: 'Tohum Ekimi ve Çimlenme',
      description: 'Domates tohumları sıcak ve nemli ortamda hızla çimlenmeye başlar. İdeal çimlenme sıcaklığı 20-25°C\'dir.',
      duration: '5-10 gün',
      image: 'https://images.unsplash.com/photo-1416879595882-3373a0480b5b?w=300&h=200&fit=crop',
      tips: [
        'Sıcaklığı 20-25°C tutun',
        'Toprağı nemli ama ıslak değil',
        'Karanlık ortam sağlayın',
        'Plastik örtü ile nem koruyun'
      ],
      careInstructions: {
        watering: 'Günde 1 kez püskürtme',
        light: 'Çimlenene kadar karanlık',
        temperature: '20-25°C',
        humidity: '%80-90'
      }
    },
    {
      stageOrder: 2,
      name: 'Fide Dönemi',
      title: 'Gerçek Yaprakların Gelişimi',
      description: 'Kotiledon yapraklardan sonra gerçek yapraklar çıkar. Bu aşamada ışık ihtiyacı artar.',
      duration: '3-4 hafta',
      image: 'https://images.unsplash.com/photo-1416879595882-3373a0480b5b?w=300&h=200&fit=crop',
      tips: [
        'Günlük sulama yapın',
        'Yeterli ışık sağlayın',
        'Havalandırma yapın',
        'Gübre vermeye başlayın'
      ],
      careInstructions: {
        watering: 'Günde 1 kez, sabah',
        light: 'Günde 12-14 saat yapay ışık',
        temperature: '18-22°C',
        humidity: '%60-70'
      }
    },
    {
      stageOrder: 3,
      name: 'Dikim ve Büyüme',
      title: 'Ana Yere Dikim ve Hızlı Büyüme',
      description: 'Fideler ana yere dikilir ve hızla büyümeye başlar. Destek sistemi kurulmalıdır.',
      duration: '4-6 hafta',
      image: 'https://images.unsplash.com/photo-1546470427-e26264be0b0d?w=300&h=200&fit=crop',
      tips: [
        'Destek çubuğu kullanın',
        'Alt dalları budayın',
        'Düzenli sulama',
        'Organik gübre verin'
      ],
      careInstructions: {
        watering: 'Günde 1-2 kez derin sulama',
        light: 'Günde 8-10 saat güneş',
        temperature: '20-25°C',
        humidity: '%60-70'
      }
    },
    {
      stageOrder: 4,
      name: 'Çiçeklenme',
      title: 'Çiçek Oluşumu ve Tozlaşma',
      description: 'Sarı çiçekler açar ve tozlaşma gerçekleşir. Bu aşamada beslenme çok önemlidir.',
      duration: '6-8 hafta',
      image: 'https://images.unsplash.com/photo-1546470427-e26264be0b0d?w=300&h=200&fit=crop',
      tips: [
        'Çiçekleri hafifçe sallayın',
        'Bol gübre verin',
        'Su stresinden kaçının',
        'Sıcaklığı kontrol edin'
      ],
      careInstructions: {
        watering: 'Günde 2 kez, köklere odaklı',
        light: 'Günde 8-10 saat güneş',
        temperature: '18-25°C',
        humidity: '%60-70'
      }
    },
    {
      stageOrder: 5,
      name: 'Meyve Gelişimi',
      title: 'Yeşil Meyvelerin Büyümesi',
      description: 'Tozlaşmadan sonra yeşil meyveler oluşur ve büyümeye başlar. Düzenli sulama kritiktir.',
      duration: '8-12 hafta',
      image: 'https://images.unsplash.com/photo-1546470427-e26264be0b0d?w=300&h=200&fit=crop',
      tips: [
        'Düzenli sulama yapın',
        'Meyveleri destekleyin',
        'Hastalık kontrolü',
        'Potasyum gübresi verin'
      ],
      careInstructions: {
        watering: 'Günde 2 kez, bol su',
        light: 'Günde 8-10 saat güneş',
        temperature: '20-28°C',
        humidity: '%60-70'
      }
    },
    {
      stageOrder: 6,
      name: 'Olgunlaşma ve Hasat',
      title: 'Meyvelerin Kırmızılaşması',
      description: 'Meyveler kırmızıya döner ve hasat edilmeye hazır hale gelir. Düzenli hasat önemlidir.',
      duration: '2-4 hafta',
      image: 'https://images.unsplash.com/photo-1546470427-e26264be0b0d?w=300&h=200&fit=crop',
      tips: [
        'Kırmızılaşmaya başlayınca toplayın',
        'Düzenli hasat yapın',
        'Olgun meyveleri geciktirmeyin',
        'Depolama koşullarına dikkat edin'
      ],
      careInstructions: {
        watering: 'Hasat öncesi azaltın',
        light: 'Günde 8-10 saat güneş',
        temperature: '20-25°C',
        humidity: '%50-60'
      }
    }
  ]
};

async function addPlantStages() {
  try {
    console.log('🚀 Bitki aşamaları ekleniyor...');
    
    // Önce mevcut bitkileri al
    const plantsSnapshot = await getDocs(collection(db, 'plants'));
    const plants = {};
    
    plantsSnapshot.forEach((doc) => {
      const plantData = doc.data();
      plants[plantData.name] = doc.id;
    });
    
    console.log('📊 Bulunan bitkiler:', Object.keys(plants));
    
    // Her bitki için aşamaları ekle
    for (const [plantName, stages] of Object.entries(plantStages)) {
      if (plants[plantName]) {
        const plantId = plants[plantName];
        console.log(`🌱 ${plantName} için aşamalar ekleniyor...`);
        
        for (const stage of stages) {
          const docRef = await addDoc(collection(db, 'plant_stages'), {
            ...stage,
            plantId: plantId,
            plantName: plantName,
            createdAt: new Date(),
            updatedAt: new Date()
          });
          console.log(`  ✅ ${stage.name} eklendi (ID: ${docRef.id})`);
        }
      } else {
        console.log(`⚠️ ${plantName} bitkisi bulunamadı`);
      }
    }
    
    console.log('🎉 Tüm bitki aşamaları eklendi!');
    
  } catch (error) {
    console.error('❌ Hata:', error);
  }
}

addPlantStages();
