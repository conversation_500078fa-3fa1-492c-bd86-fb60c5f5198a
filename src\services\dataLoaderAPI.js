import apiService from './apiService';
import { SAMPLE_PLANTS, SAMPLE_ARTICLES } from '../utils/constants';

// Sample Categories Data
const SAMPLE_CATEGORIES = [
  {
    id: 'cicek',
    name: '<PERSON><PERSON><PERSON><PERSON>',
    description: 'Ç<PERSON><PERSON><PERSON><PERSON> bitkiler',
    icon: 'flower',
    color: '#FF6B9D'
  },
  {
    id: 'sebze',
    name: '<PERSON><PERSON><PERSON>',
    description: '<PERSON><PERSON><PERSON> bitkileri',
    icon: 'nutrition',
    color: '#4ECDC4'
  },
  {
    id: 'aromatik',
    name: 'Aromatik',
    description: 'Aromatik bitkiler',
    icon: 'leaf',
    color: '#45B7D1'
  },
  {
    id: 'sukulent',
    name: '<PERSON>ku<PERSON>',
    description: 'Sukulent bitkiler',
    icon: 'cactus',
    color: '#96CEB4'
  }
];

class DataLoaderAPI {
  constructor() {
    this.baseURL = 'https://your-api-endpoint.com/api'; // Replace with your API endpoint
    this.isLoading = false;
    this.loadingProgress = 0;
    this.logs = [];
  }

  // Add log entry
  addLog(message, type = 'info') {
    const timestamp = new Date().toLocaleTimeString();
    const logEntry = { message, type, timestamp };
    this.logs.push(logEntry);
    console.log(`[${type.toUpperCase()}] ${timestamp}: ${message}`);
    return logEntry;
  }

  // Get loading status
  getStatus() {
    return {
      isLoading: this.isLoading,
      progress: this.loadingProgress,
      logs: this.logs
    };
  }

  // Clear logs
  clearLogs() {
    this.logs = [];
  }

  // Load Plants Data
  async loadPlantsData() {
    try {
      this.isLoading = true;
      this.loadingProgress = 0;
      this.addLog('🌱 Bitki verilerini yükleme başlatıldı...', 'info');

      // Simulate API call or use direct Firebase
      this.loadingProgress = 25;
      this.addLog(`📊 ${SAMPLE_PLANTS.length} bitki verisi hazırlanıyor...`, 'info');

      // Use Firebase API Service
      await apiService.migratePlants(SAMPLE_PLANTS);
      
      this.loadingProgress = 100;
      this.addLog('✅ Bitki verileri başarıyla yüklendi!', 'success');
      
      return { success: true, count: SAMPLE_PLANTS.length };
    } catch (error) {
      this.addLog(`❌ Bitki verisi yükleme hatası: ${error.message}`, 'error');
      throw error;
    } finally {
      this.isLoading = false;
    }
  }

  // Load Articles Data
  async loadArticlesData() {
    try {
      this.isLoading = true;
      this.loadingProgress = 0;
      this.addLog('📚 Makale verilerini yükleme başlatıldı...', 'info');

      this.loadingProgress = 25;
      this.addLog(`📊 ${SAMPLE_ARTICLES.length} makale verisi hazırlanıyor...`, 'info');

      await apiService.migrateArticles(SAMPLE_ARTICLES);
      
      this.loadingProgress = 100;
      this.addLog('✅ Makale verileri başarıyla yüklendi!', 'success');
      
      return { success: true, count: SAMPLE_ARTICLES.length };
    } catch (error) {
      this.addLog(`❌ Makale verisi yükleme hatası: ${error.message}`, 'error');
      throw error;
    } finally {
      this.isLoading = false;
    }
  }

  // Load Categories Data
  async loadCategoriesData() {
    try {
      this.isLoading = true;
      this.loadingProgress = 0;
      this.addLog('📂 Kategori verilerini yükleme başlatıldı...', 'info');

      this.loadingProgress = 25;
      this.addLog(`📊 ${SAMPLE_CATEGORIES.length} kategori verisi hazırlanıyor...`, 'info');

      await apiService.migrateCategories(SAMPLE_CATEGORIES);
      
      this.loadingProgress = 100;
      this.addLog('✅ Kategori verileri başarıyla yüklendi!', 'success');
      
      return { success: true, count: SAMPLE_CATEGORIES.length };
    } catch (error) {
      this.addLog(`❌ Kategori verisi yükleme hatası: ${error.message}`, 'error');
      throw error;
    } finally {
      this.isLoading = false;
    }
  }

  // Load All Data
  async loadAllData() {
    try {
      this.isLoading = true;
      this.loadingProgress = 0;
      this.addLog('🚀 Tam veri yükleme başlatıldı...', 'info');

      // Load Categories first
      this.loadingProgress = 10;
      await this.loadCategoriesData();

      // Load Plants
      this.loadingProgress = 40;
      await this.loadPlantsData();

      // Load Articles
      this.loadingProgress = 80;
      await this.loadArticlesData();

      this.loadingProgress = 100;
      this.addLog('🎉 Tüm veriler başarıyla yüklendi!', 'success');
      
      return { 
        success: true, 
        counts: {
          plants: SAMPLE_PLANTS.length,
          articles: SAMPLE_ARTICLES.length,
          categories: SAMPLE_CATEGORIES.length
        }
      };
    } catch (error) {
      this.addLog(`❌ Tam veri yükleme hatası: ${error.message}`, 'error');
      throw error;
    } finally {
      this.isLoading = false;
    }
  }

  // REST API Methods (for external web admin)
  async callExternalAPI(endpoint, method = 'GET', data = null) {
    try {
      const url = `${this.baseURL}/${endpoint}`;
      const options = {
        method,
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer YOUR_API_TOKEN' // Replace with your token
        }
      };

      if (data && (method === 'POST' || method === 'PUT')) {
        options.body = JSON.stringify(data);
      }

      const response = await fetch(url, options);
      
      if (!response.ok) {
        throw new Error(`API Error: ${response.status} ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error('External API call failed:', error);
      throw error;
    }
  }

  // Load data from external API
  async loadFromExternalAPI() {
    try {
      this.addLog('🌐 External API\'den veri yükleme başlatıldı...', 'info');

      // Example API calls
      const plantsResponse = await this.callExternalAPI('plants');
      const articlesResponse = await this.callExternalAPI('articles');
      const categoriesResponse = await this.callExternalAPI('categories');

      // Process and save to Firebase
      if (plantsResponse.data) {
        await apiService.migratePlants(plantsResponse.data);
        this.addLog(`✅ ${plantsResponse.data.length} bitki API'den yüklendi`, 'success');
      }

      if (articlesResponse.data) {
        await apiService.migrateArticles(articlesResponse.data);
        this.addLog(`✅ ${articlesResponse.data.length} makale API'den yüklendi`, 'success');
      }

      if (categoriesResponse.data) {
        await apiService.migrateCategories(categoriesResponse.data);
        this.addLog(`✅ ${categoriesResponse.data.length} kategori API'den yüklendi`, 'success');
      }

      return { success: true };
    } catch (error) {
      this.addLog(`❌ External API yükleme hatası: ${error.message}`, 'error');
      throw error;
    }
  }

  // Database health check
  async checkDatabaseHealth() {
    try {
      this.addLog('🔍 Veritabanı durumu kontrol ediliyor...', 'info');

      const plants = await apiService.getPlants({ useCache: false, limitCount: 1 });
      const articles = await apiService.getArticles({ useCache: false, limitCount: 1 });
      const categories = await apiService.getCategories();

      const health = {
        plants: plants.length > 0,
        articles: articles.length > 0,
        categories: categories.length > 0,
        timestamp: new Date().toISOString()
      };

      this.addLog(`✅ Veritabanı durumu: ${JSON.stringify(health)}`, 'success');
      return health;
    } catch (error) {
      this.addLog(`❌ Veritabanı kontrol hatası: ${error.message}`, 'error');
      throw error;
    }
  }

  // Cache management
  async clearAllCache() {
    try {
      this.addLog('🧹 Cache temizleniyor...', 'info');
      await apiService.clearCache();
      this.addLog('✅ Cache başarıyla temizlendi', 'success');
    } catch (error) {
      this.addLog(`❌ Cache temizleme hatası: ${error.message}`, 'error');
      throw error;
    }
  }

  async getCacheInfo() {
    try {
      const info = await apiService.getCacheInfo();
      this.addLog(`📊 Cache bilgisi: ${JSON.stringify(info)}`, 'info');
      return info;
    } catch (error) {
      this.addLog(`❌ Cache bilgi hatası: ${error.message}`, 'error');
      throw error;
    }
  }

  // Preload essential data
  async preloadEssentialData() {
    try {
      this.addLog('⚡ Temel veriler önyükleniyor...', 'info');
      await apiService.preloadData();
      this.addLog('✅ Temel veriler önyüklendi', 'success');
    } catch (error) {
      this.addLog(`❌ Önyükleme hatası: ${error.message}`, 'error');
      throw error;
    }
  }
}

export default new DataLoaderAPI();
