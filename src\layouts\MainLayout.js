import React from 'react';
import { View, SafeAreaView, StatusBar } from 'react-native';
import { COLORS } from '../utils/constants';

const MainLayout = ({ children, style, safeArea = true }) => {
  const Container = safeArea ? SafeAreaView : View;
  
  return (
    <>
      <StatusBar barStyle="dark-content" backgroundColor={COLORS.white} />
      <Container style={[{ flex: 1, backgroundColor: COLORS.background }, style]}>
        {children}
      </Container>
    </>
  );
};

export default MainLayout;
