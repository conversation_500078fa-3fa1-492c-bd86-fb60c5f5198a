import { StyleSheet } from 'react-native';
import { COLORS, FONT_SIZES, SPACING } from '../../utils/constants';

export default StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FC7138', // Turuncu status bar için
  },

  scrollView: {
    flex: 1,
  },

  scrollContent: {
    backgroundColor: COLORS.background,
    paddingBottom: SPACING.tabBar, // Extra space for floating tab bar
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    marginTop: -20, // Header ile overlap
  },

  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: SPACING.lg,
    paddingVertical: SPACING.lg,
    paddingTop: SPACING.xxl,
    backgroundColor: '#FC7138', // Turuncu arka plan
    marginBottom: SPACING.lg,
    borderBottomLeftRadius: 20,
    borderBottomRightRadius: 20,
    shadowColor: COLORS.shadow,
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 6,
  },

  logoSection: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },

  logo: {
    width: 40,
    height: 40,
    marginRight: SPACING.md,
  },

  title: {
    fontSize: FONT_SIZES.header,
    fontWeight: '800',
    color: COLORS.white,
    letterSpacing: 0.5,
  },

  subtitle: {
    fontSize: FONT_SIZES.medium,
    color: COLORS.white,
    fontWeight: '500',
    opacity: 0.9,
    letterSpacing: 0.3,
    marginTop: 2,
  },
  
  listContent: {
    paddingHorizontal: SPACING.lg,
    paddingBottom: SPACING.tabBar, // Extra space for floating tab bar
    flexGrow: 1,
  },
  
  row: {
    justifyContent: 'space-between',
  },
  
  plantCard: {
    // PlantCard component'inin kendi stilleri kullanılacak
  },
  
  emptyState: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: SPACING.xl,
  },
  
  emptyStateTitle: {
    fontSize: FONT_SIZES.xlarge,
    fontWeight: '600',
    color: COLORS.primary,
    marginTop: SPACING.lg,
    marginBottom: SPACING.md,
    textAlign: 'center',
  },
  
  emptyStateText: {
    fontSize: FONT_SIZES.medium,
    color: COLORS.darkGray,
    textAlign: 'center',
    lineHeight: 22,
    marginBottom: SPACING.xl,
  },
  
  exploreButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.secondary,
    paddingHorizontal: SPACING.lg,
    paddingVertical: SPACING.md,
    borderRadius: 12,
    gap: SPACING.sm,
  },
  
  exploreButtonText: {
    color: COLORS.white,
    fontSize: FONT_SIZES.medium,
    fontWeight: '600',
  },
  
  errorState: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: SPACING.xl,
  },
  
  errorTitle: {
    fontSize: FONT_SIZES.xlarge,
    fontWeight: '600',
    color: COLORS.primary,
    marginTop: SPACING.md,
    marginBottom: SPACING.sm,
    textAlign: 'center',
  },
  
  errorText: {
    fontSize: FONT_SIZES.medium,
    color: COLORS.darkGray,
    textAlign: 'center',
    lineHeight: 20,
    marginBottom: SPACING.lg,
  },
  
  retryButton: {
    backgroundColor: COLORS.accent,
    paddingHorizontal: SPACING.lg,
    paddingVertical: SPACING.md,
    borderRadius: 8,
  },
  
  retryButtonText: {
    color: COLORS.white,
    fontSize: FONT_SIZES.medium,
    fontWeight: '600',
  },
});
