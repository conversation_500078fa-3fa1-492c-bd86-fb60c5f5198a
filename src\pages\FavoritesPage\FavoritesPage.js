import React from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  Image,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

import PlantCard from '../../components/PlantCard/PlantCard';
import LoadingSpinner from '../../components/common/LoadingSpinner/LoadingSpinner';
import { useFavoritePlants } from '../../hooks/useFavorites';
import { COLORS } from '../../utils/constants';
import styles from './FavoritesPage.styles.js';

const FavoritesPage = ({ navigation }) => {
  const { favoritePlants, loading, error, reloadFavoritePlants } = useFavoritePlants();

  const handlePlantPress = (plant) => {
    navigation.navigate('PlantDetail', { plantId: plant.id, plant });
  };

  const handleExplorePress = () => {
    navigation.navigate('Home');
  };

  const renderPlantItem = ({ item }) => (
    <PlantCard
      plant={item}
      onPress={handlePlantPress}
      style={styles.plantCard}
    />
  );

  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <Ionicons name="heart-outline" size={80} color={COLORS.gray} />
      <Text style={styles.emptyStateTitle}>Henüz Favori Bitkiniz Yok</Text>
      <Text style={styles.emptyStateText}>
        Beğendiğiniz bitkileri favorilerinize ekleyerek buradan kolayca erişebilirsiniz.
      </Text>
      <TouchableOpacity style={styles.exploreButton} onPress={handleExplorePress}>
        <Ionicons name="leaf" size={20} color={COLORS.white} />
        <Text style={styles.exploreButtonText}>Bitkileri Keşfet</Text>
      </TouchableOpacity>
    </View>
  );

  const renderErrorState = () => (
    <View style={styles.errorState}>
      <Ionicons name="alert-circle" size={64} color={COLORS.accent} />
      <Text style={styles.errorTitle}>Hata Oluştu</Text>
      <Text style={styles.errorText}>{error}</Text>
      <TouchableOpacity style={styles.retryButton} onPress={reloadFavoritePlants}>
        <Text style={styles.retryButtonText}>Tekrar Dene</Text>
      </TouchableOpacity>
    </View>
  );

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <LoadingSpinner text="Favori bitkiler yükleniyor..." />
      </SafeAreaView>
    );
  }

  if (error) {
    return (
      <SafeAreaView style={styles.container}>
        {renderErrorState()}
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        {/* Header */}
        <View style={styles.header}>
          <View style={styles.logoSection}>
            <Image
              source={require('../../../assets/wikiplantlogo.png')}
              style={styles.logo}
              resizeMode="contain"
            />
            <View>
              <Text style={styles.title}>WikiPlant</Text>
              <Text style={styles.subtitle}>
                {favoritePlants.length} favori bitki
              </Text>
            </View>
          </View>
        </View>

        {/* Favorites List */}
        {favoritePlants.length === 0 ? (
          renderEmptyState()
        ) : (
          favoritePlants.map((plant) => (
            <PlantCard
              key={plant.id}
              plant={plant}
              onPress={handlePlantPress}
              style={styles.plantCard}
            />
          ))
        )}
      </ScrollView>
    </SafeAreaView>
  );
};

export default FavoritesPage;
