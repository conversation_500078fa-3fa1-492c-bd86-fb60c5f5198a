const { getDefaultConfig } = require('expo/metro-config');

const config = getDefaultConfig(__dirname);

// Hermes engine optimizasyonları
config.resolver.platforms = ['ios', 'android', 'native', 'web'];

// Firebase ile Hermes uyumluluğu için
config.transformer.minifierConfig = {
  keep_fnames: true,
  mangle: {
    keep_fnames: true,
  },
};

// Resolver ayarları
config.resolver.alias = {
  // Firebase uyumluluğu için
  'react-native-sqlite-storage': false,
};

module.exports = config;
