// Firebase service
import { 
  collection, 
  doc, 
  getDocs, 
  getDoc, 
  addDoc, 
  updateDoc, 
  deleteDoc, 
  query, 
  where, 
  orderBy, 
  limit,
  onSnapshot
} from 'firebase/firestore';
import { db } from '../config/firebase';

console.log('🔥 Firebase Service loaded');

// Plants Service
export const plantsService = {
  // Tüm bitkileri getir
  async getAllPlants() {
    try {
      console.log('🔥 Firebase: Tüm bitkiler getiriliyor...');
      const plantsCollection = collection(db, 'plants');
      const plantsSnapshot = await getDocs(plantsCollection);
      const plants = plantsSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));
      console.log('✅ Firebase: Bitkiler getirildi:', plants.length);
      return plants;
    } catch (error) {
      console.error('❌ Firebase: Bitkiler getirilemedi:', error);
      return []; // Return empty array to trigger mock data fallback
    }
  },

  // Tek bitki getir
  async getPlantById(plantId) {
    try {
      console.log('🔥 Firebase: Bitki getiriliyor:', plantId);
      const plantDoc = doc(db, 'plants', plantId);
      const plantSnapshot = await getDoc(plantDoc);
      
      if (plantSnapshot.exists()) {
        const plant = { id: plantSnapshot.id, ...plantSnapshot.data() };
        console.log('✅ Firebase: Bitki getirildi:', plant.name);
        return plant;
      } else {
        console.log('⚠️ Firebase: Bitki bulunamadı:', plantId);
        return null;
      }
    } catch (error) {
      console.error('❌ Firebase: Bitki getirilemedi:', error);
      return null; // Return null to trigger mock data fallback
    }
  },

  // Kategoriye göre bitkiler getir
  async getPlantsByCategory(category) {
    try {
      console.log('🔥 Firebase: Kategoriye göre bitkiler getiriliyor:', category);
      const plantsCollection = collection(db, 'plants');
      const q = query(plantsCollection, where('category', '==', category));
      const plantsSnapshot = await getDocs(q);
      const plants = plantsSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));
      console.log('✅ Firebase: Kategori bitkileri getirildi:', plants.length);
      return plants;
    } catch (error) {
      console.error('❌ Firebase: Kategori bitkileri getirilemedi:', error);
      return []; // Return empty array to trigger mock data fallback
    }
  },

  // Yeni bitki ekle
  async addPlant(plantData) {
    try {
      console.log('🔥 Firebase: Yeni bitki ekleniyor...');
      const plantsCollection = collection(db, 'plants');
      const docRef = await addDoc(plantsCollection, {
        ...plantData,
        createdAt: new Date(),
        updatedAt: new Date()
      });
      console.log('✅ Firebase: Bitki eklendi:', docRef.id);
      return docRef.id;
    } catch (error) {
      console.error('❌ Firebase: Bitki eklenemedi:', error);
      throw error;
    }
  }
};

// Plant Stages Service
export const plantStagesService = {
  // Bitkinin aşamalarını getir
  async getPlantStages(plantId) {
    try {
      console.log('🔥 Firebase: Bitki aşamaları getiriliyor:', plantId);
      const stagesCollection = collection(db, 'plant_stages');
      const q = query(
        stagesCollection, 
        where('plantId', '==', plantId),
        orderBy('stageNumber', 'asc')
      );
      const stagesSnapshot = await getDocs(q);
      const stages = stagesSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));
      console.log('✅ Firebase: Bitki aşamaları getirildi:', stages.length);
      return stages;
    } catch (error) {
      console.error('❌ Firebase: Bitki aşamaları getirilemedi:', error);
      return []; // Return empty array to trigger mock data fallback
    }
  },

  // Yeni aşama ekle
  async addPlantStage(stageData) {
    try {
      console.log('🔥 Firebase: Yeni aşama ekleniyor...');
      const stagesCollection = collection(db, 'plant_stages');
      const docRef = await addDoc(stagesCollection, {
        ...stageData,
        createdAt: new Date(),
        updatedAt: new Date()
      });
      console.log('✅ Firebase: Aşama eklendi:', docRef.id);
      return docRef.id;
    } catch (error) {
      console.error('❌ Firebase: Aşama eklenemedi:', error);
      throw error;
    }
  }
};

// Treatments Service
export const treatmentsService = {
  // Bitkinin tedavilerini getir
  async getPlantTreatments(plantId) {
    try {
      console.log('🔥 Firebase: Bitki tedavileri getiriliyor:', plantId);
      const treatmentsCollection = collection(db, 'plant_treatments');
      const q = query(treatmentsCollection, where('plantId', '==', plantId));
      const treatmentsSnapshot = await getDocs(q);
      const treatments = treatmentsSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));
      console.log('✅ Firebase: Bitki tedavileri getirildi:', treatments.length);
      return treatments;
    } catch (error) {
      console.error('❌ Firebase: Bitki tedavileri getirilemedi:', error);
      return []; // Return empty array to trigger mock data fallback
    }
  },

  // Yeni tedavi ekle
  async addTreatment(treatmentData) {
    try {
      console.log('🔥 Firebase: Yeni tedavi ekleniyor...');
      const treatmentsCollection = collection(db, 'plant_treatments');
      const docRef = await addDoc(treatmentsCollection, {
        ...treatmentData,
        createdAt: new Date(),
        updatedAt: new Date()
      });
      console.log('✅ Firebase: Tedavi eklendi:', docRef.id);
      return docRef.id;
    } catch (error) {
      console.error('❌ Firebase: Tedavi eklenemedi:', error);
      throw error;
    }
  }
};

// Real-time listeners
export const realtimeService = {
  // Bitkileri real-time dinle
  subscribePlants(callback) {
    try {
      console.log('🔥 Firebase: Real-time plants listener başlatılıyor...');
      const plantsCollection = collection(db, 'plants');
      
      const unsubscribe = onSnapshot(plantsCollection, (snapshot) => {
        const plants = snapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data()
        }));
        console.log('🔄 Firebase: Real-time plants güncellendi:', plants.length);
        callback(plants);
      }, (error) => {
        console.error('❌ Firebase: Real-time plants hatası:', error);
        callback([]); // Trigger mock data fallback
      });
      
      return unsubscribe;
    } catch (error) {
      console.error('❌ Firebase: Real-time plants listener hatası:', error);
      // Trigger callback with empty array to use mock data
      setTimeout(() => callback([]), 100);
      return () => {}; // Return empty unsubscribe function
    }
  },

  // Bitki aşamalarını real-time dinle
  subscribePlantStages(plantId, callback) {
    try {
      console.log('🔥 Firebase: Real-time stages listener başlatılıyor:', plantId);
      const stagesCollection = collection(db, 'plant_stages');
      const q = query(
        stagesCollection, 
        where('plantId', '==', plantId),
        orderBy('stageNumber', 'asc')
      );
      
      const unsubscribe = onSnapshot(q, (snapshot) => {
        const stages = snapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data()
        }));
        console.log('🔄 Firebase: Real-time stages güncellendi:', stages.length);
        callback(stages);
      }, (error) => {
        console.error('❌ Firebase: Real-time stages hatası:', error);
        callback([]); // Trigger mock data fallback
      });
      
      return unsubscribe;
    } catch (error) {
      console.error('❌ Firebase: Real-time stages listener hatası:', error);
      // Trigger callback with empty array to use mock data
      setTimeout(() => callback([]), 100);
      return () => {}; // Return empty unsubscribe function
    }
  }
};

// Utility functions
export const firebaseUtils = {
  // Batch operations için
  async batchAddPlants(plantsArray) {
    try {
      console.log('🔥 Firebase: Batch plants ekleniyor:', plantsArray.length);
      const plantsCollection = collection(db, 'plants');
      const promises = plantsArray.map(plant => 
        addDoc(plantsCollection, {
          ...plant,
          createdAt: new Date(),
          updatedAt: new Date()
        })
      );
      const results = await Promise.all(promises);
      console.log('✅ Firebase: Batch plants eklendi:', results.length);
      return results.map(doc => doc.id);
    } catch (error) {
      console.error('❌ Firebase: Batch plants eklenemedi:', error);
      throw error;
    }
  },

  // Search functionality
  async searchPlants(searchTerm) {
    try {
      console.log('🔥 Firebase: Bitki aranıyor:', searchTerm);
      const plantsCollection = collection(db, 'plants');
      const plantsSnapshot = await getDocs(plantsCollection);
      
      const plants = plantsSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));
      
      // Client-side filtering for search
      const filteredPlants = plants.filter(plant => 
        plant.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        plant.latinName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        plant.category?.toLowerCase().includes(searchTerm.toLowerCase())
      );
      
      console.log('✅ Firebase: Arama tamamlandı:', filteredPlants.length);
      return filteredPlants;
    } catch (error) {
      console.error('❌ Firebase: Arama yapılamadı:', error);
      return []; // Return empty array to trigger mock data fallback
    }
  }
};
