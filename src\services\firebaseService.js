// Firebase service - Mock mode to prevent auth errors
console.log('📦 Mock Firebase Service loaded (preventing auth errors)');

// Plants Service
export const plantsService = {
  // Tüm bitkileri getir
  async getAllPlants() {
    console.log('🔥 Mock Firebase: Tüm bitkiler getiriliyor...');
    // Return empty array to trigger mock data fallback
    return [];
  },

  // Tek bitki getir
  async getPlantById(plantId) {
    console.log('🔥 Mock Firebase: Bitki getiriliyor:', plantId);
    // Return null to trigger mock data fallback
    return null;
  },

  // Kategoriye göre bitkiler getir
  async getPlantsByCategory(category) {
    console.log('🔥 Mock Firebase: Kategoriye göre bitkiler getiriliyor:', category);
    // Return empty array to trigger mock data fallback
    return [];
  },

  // Yeni bitki ekle
  async addPlant(plantData) {
    console.log('🔥 Mock Firebase: Yeni bitki ekleniyor...');
    return 'mock-id';
  }
};

// Plant Stages Service
export const plantStagesService = {
  // Bitkinin aşamalarını getir
  async getPlantStages(plantId) {
    console.log('🔥 Mock Firebase: Bitki aşamaları getiriliyor:', plantId);
    // Return empty array to trigger mock data fallback
    return [];
  },

  // Yeni aşama ekle
  async addPlantStage(stageData) {
    console.log('🔥 Mock Firebase: Yeni aşama ekleniyor...');
    return 'mock-stage-id';
  }
};

// Treatments Service
export const treatmentsService = {
  // Bitkinin tedavilerini getir
  async getPlantTreatments(plantId) {
    console.log('🔥 Mock Firebase: Bitki tedavileri getiriliyor:', plantId);
    // Return empty array to trigger mock data fallback
    return [];
  },

  // Yeni tedavi ekle
  async addTreatment(treatmentData) {
    console.log('🔥 Mock Firebase: Yeni tedavi ekleniyor...');
    return 'mock-treatment-id';
  }
};

// Real-time listeners
export const realtimeService = {
  // Bitkileri real-time dinle
  subscribePlants(callback) {
    console.log('🔥 Mock Firebase: Real-time plants listener başlatılıyor...');
    // Trigger callback with empty array to use mock data
    setTimeout(() => callback([]), 100);
    return () => {}; // Return empty unsubscribe function
  },

  // Bitki aşamalarını real-time dinle
  subscribePlantStages(plantId, callback) {
    console.log('🔥 Mock Firebase: Real-time stages listener başlatılıyor:', plantId);
    // Trigger callback with empty array to use mock data
    setTimeout(() => callback([]), 100);
    return () => {}; // Return empty unsubscribe function
  }
};

// Utility functions
export const firebaseUtils = {
  // Batch operations için
  async batchAddPlants(plantsArray) {
    console.log('🔥 Mock Firebase: Batch plants ekleniyor:', plantsArray.length);
    return [];
  },

  // Search functionality
  async searchPlants(searchTerm) {
    console.log('🔥 Mock Firebase: Bitki aranıyor:', searchTerm);
    // Return empty array to trigger mock data fallback
    return [];
  }
};
