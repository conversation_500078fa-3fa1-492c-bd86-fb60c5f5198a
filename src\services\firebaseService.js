import { 
  collection, 
  getDocs, 
  doc, 
  getDoc, 
  addDoc, 
  updateDoc, 
  deleteDoc, 
  query, 
  where, 
  orderBy, 
  limit,
  onSnapshot 
} from 'firebase/firestore';
import { db } from '../config/firebase';

// Plants Service
export const plantsService = {
  // Tüm bitkileri getir
  async getAllPlants() {
    try {
      console.log('🔥 Firebase: Tüm bitkiler getiriliyor...');
      const plantsCollection = collection(db, 'plants');
      const plantsSnapshot = await getDocs(plantsCollection);
      
      const plants = plantsSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));
      
      console.log('✅ Firebase: Bitkiler başarıyla getirildi:', plants.length);
      return plants;
    } catch (error) {
      console.error('❌ Firebase: Bitkiler getirilemedi:', error);
      throw error;
    }
  },

  // Tek bitki getir
  async getPlantById(plantId) {
    try {
      console.log('🔥 Firebase: Bitki getiriliyor:', plantId);
      const plantDoc = doc(db, 'plants', plantId);
      const plantSnapshot = await getDoc(plantDoc);
      
      if (plantSnapshot.exists()) {
        const plant = {
          id: plantSnapshot.id,
          ...plantSnapshot.data()
        };
        console.log('✅ Firebase: Bitki başarıyla getirildi:', plant.name);
        return plant;
      } else {
        console.log('❌ Firebase: Bitki bulunamadı:', plantId);
        return null;
      }
    } catch (error) {
      console.error('❌ Firebase: Bitki getirilemedi:', error);
      throw error;
    }
  },

  // Kategoriye göre bitkiler getir
  async getPlantsByCategory(category) {
    try {
      console.log('🔥 Firebase: Kategoriye göre bitkiler getiriliyor:', category);
      const plantsCollection = collection(db, 'plants');
      const q = query(plantsCollection, where('category', '==', category));
      const plantsSnapshot = await getDocs(q);
      
      const plants = plantsSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));
      
      console.log('✅ Firebase: Kategori bitkileri getirildi:', plants.length);
      return plants;
    } catch (error) {
      console.error('❌ Firebase: Kategori bitkileri getirilemedi:', error);
      throw error;
    }
  },

  // Yeni bitki ekle
  async addPlant(plantData) {
    try {
      console.log('🔥 Firebase: Yeni bitki ekleniyor...');
      const plantsCollection = collection(db, 'plants');
      const docRef = await addDoc(plantsCollection, {
        ...plantData,
        createdAt: new Date(),
        updatedAt: new Date()
      });
      
      console.log('✅ Firebase: Bitki başarıyla eklendi:', docRef.id);
      return docRef.id;
    } catch (error) {
      console.error('❌ Firebase: Bitki eklenemedi:', error);
      throw error;
    }
  }
};

// Plant Stages Service
export const plantStagesService = {
  // Bitkinin aşamalarını getir
  async getPlantStages(plantId) {
    try {
      console.log('🔥 Firebase: Bitki aşamaları getiriliyor:', plantId);
      const stagesCollection = collection(db, 'plantstages');
      const q = query(
        stagesCollection, 
        where('plantId', '==', plantId),
        orderBy('order', 'asc')
      );
      const stagesSnapshot = await getDocs(q);
      
      const stages = stagesSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));
      
      console.log('✅ Firebase: Bitki aşamaları getirildi:', stages.length);
      return stages;
    } catch (error) {
      console.error('❌ Firebase: Bitki aşamaları getirilemedi:', error);
      throw error;
    }
  },

  // Yeni aşama ekle
  async addPlantStage(stageData) {
    try {
      console.log('🔥 Firebase: Yeni aşama ekleniyor...');
      const stagesCollection = collection(db, 'plantstages');
      const docRef = await addDoc(stagesCollection, {
        ...stageData,
        createdAt: new Date()
      });
      
      console.log('✅ Firebase: Aşama başarıyla eklendi:', docRef.id);
      return docRef.id;
    } catch (error) {
      console.error('❌ Firebase: Aşama eklenemedi:', error);
      throw error;
    }
  }
};

// Treatments Service
export const treatmentsService = {
  // Bitkinin tedavilerini getir
  async getPlantTreatments(plantId) {
    try {
      console.log('🔥 Firebase: Bitki tedavileri getiriliyor:', plantId);
      const treatmentsCollection = collection(db, 'treatments');
      const q = query(treatmentsCollection, where('plantId', '==', plantId));
      const treatmentsSnapshot = await getDocs(q);
      
      const treatments = treatmentsSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));
      
      console.log('✅ Firebase: Bitki tedavileri getirildi:', treatments.length);
      return treatments;
    } catch (error) {
      console.error('❌ Firebase: Bitki tedavileri getirilemedi:', error);
      throw error;
    }
  },

  // Yeni tedavi ekle
  async addTreatment(treatmentData) {
    try {
      console.log('🔥 Firebase: Yeni tedavi ekleniyor...');
      const treatmentsCollection = collection(db, 'treatments');
      const docRef = await addDoc(treatmentsCollection, {
        ...treatmentData,
        createdAt: new Date()
      });
      
      console.log('✅ Firebase: Tedavi başarıyla eklendi:', docRef.id);
      return docRef.id;
    } catch (error) {
      console.error('❌ Firebase: Tedavi eklenemedi:', error);
      throw error;
    }
  }
};

// Real-time listeners
export const realtimeService = {
  // Bitkileri real-time dinle
  subscribePlants(callback) {
    console.log('🔥 Firebase: Real-time plants listener başlatılıyor...');
    const plantsCollection = collection(db, 'plants');
    
    return onSnapshot(plantsCollection, (snapshot) => {
      const plants = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));
      
      console.log('🔄 Firebase: Real-time plants güncellendi:', plants.length);
      callback(plants);
    }, (error) => {
      console.error('❌ Firebase: Real-time plants hatası:', error);
    });
  },

  // Bitki aşamalarını real-time dinle
  subscribePlantStages(plantId, callback) {
    console.log('🔥 Firebase: Real-time stages listener başlatılıyor:', plantId);
    const stagesCollection = collection(db, 'plantstages');
    const q = query(
      stagesCollection, 
      where('plantId', '==', plantId),
      orderBy('order', 'asc')
    );
    
    return onSnapshot(q, (snapshot) => {
      const stages = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));
      
      console.log('🔄 Firebase: Real-time stages güncellendi:', stages.length);
      callback(stages);
    }, (error) => {
      console.error('❌ Firebase: Real-time stages hatası:', error);
    });
  }
};

// Utility functions
export const firebaseUtils = {
  // Batch operations için
  async batchAddPlants(plantsArray) {
    try {
      console.log('🔥 Firebase: Batch plants ekleniyor:', plantsArray.length);
      const promises = plantsArray.map(plant => plantsService.addPlant(plant));
      const results = await Promise.all(promises);
      
      console.log('✅ Firebase: Batch plants eklendi:', results.length);
      return results;
    } catch (error) {
      console.error('❌ Firebase: Batch plants eklenemedi:', error);
      throw error;
    }
  },

  // Search functionality
  async searchPlants(searchTerm) {
    try {
      console.log('🔥 Firebase: Bitki aranıyor:', searchTerm);
      // Firestore doesn't support full-text search, so we get all and filter
      const allPlants = await plantsService.getAllPlants();
      
      const filteredPlants = allPlants.filter(plant => 
        plant.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        plant.latinName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        plant.description.toLowerCase().includes(searchTerm.toLowerCase())
      );
      
      console.log('✅ Firebase: Arama sonuçları:', filteredPlants.length);
      return filteredPlants;
    } catch (error) {
      console.error('❌ Firebase: Arama yapılamadı:', error);
      throw error;
    }
  }
};
